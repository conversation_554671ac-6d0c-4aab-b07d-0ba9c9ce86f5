<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui" />
	<title>龙坤智慧MDC</title>
	<link href="font-awesome.min.css" rel="stylesheet" />
	<link rel="shortcut icon" type="image/png" href="favicon-16x16.png" />
	<script src="vue.min.js"></script>
	<script src="dayjs.min.js"></script>
	<style type="text/css">
		html {
			font-family: "Source Sans Pro", Helvetica, Arial, sans-serif;
			font-size: 18px;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
		}

		body {
			padding: 0;
			margin: 0;
			color: black;
			text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
			padding-bottom: 60px;
			/* footer */
		}

		.cursor-pointer {
			cursor: pointer;
			user-select: none;
		}

		header,
		footer {
			text-align: center;
			color: white;
			text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
		}

		header a,
		header a.router-link-exact-active,
		footer a,
		footer a.router-link-exact-active {
			color: #63dcfd;
		}

		header {
			background-image: linear-gradient(45deg, #e37682 0%, #5f4d93 100%);
			padding: 1em;
			box-shadow: 0 3px 10px rgba(0, 0, 0, 0.6);
			margin-bottom: 1em;
		}

		footer {
			background-image: linear-gradient(135deg, #e37682 0%, #5f4d93 100%);
			padding: 0.75em;
			font-size: 0.8em;
			box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.6);
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			margin-top: 1em;
		}

		footer .footer-links {
			margin-top: 0.5em;
		}

		footer .footer-links a {
			margin: 0 0.5em;
		}

		a,
		a.router-link-exact-active {
			color: #3cafce;
			text-decoration: none;
		}

		nav ul {
			list-style: none;
			padding: 0;
			margin: 0;
		}

		nav ul li {
			display: inline-block;
			padding: 0.25em 0.75em;
			cursor: pointer;
			font-weight: 300;
			font-size: 1.25em;
			border-bottom: 2px solid transparent;
			transition: color 0.1s linear, border-bottom 0.1s linear;
		}

		nav ul li.active {
			border-bottom: 2px solid #63dcfd;
		}

		nav ul li:hover {
			color: #63dcfd;
		}

		button,
		.button {
			background-color: #3cafce;
			border: 0;
			border-radius: 8px;
			color: white;
			font-family: "Source Sans Pro", Helvetica, Arial, sans-serif;
			font-size: 16px;
			font-weight: 400;
			padding: 0.5em 1em;
			box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
			cursor: pointer;
			user-select: none;
		}

		button i,
		.button i {
			margin-right: 0.5em;
		}

		button:hover,
		.button:hover {
			background-color: red;
		}

		buttonRed,
		.buttonRed {
			background-color: red;
			border: 0;
			border-radius: 8px;
			color: white;
			font-family: "Source Sans Pro", Helvetica, Arial, sans-serif;
			font-size: 16px;
			font-weight: 400;
			padding: 0.5em 1em;
			box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
			cursor: pointer;
			user-select: none;
		}

		buttonRed i,
		.buttonRed i {
			margin-right: 0.5em;
		}

		buttonRed:hover,
		.buttonRed.caption {
			background-color: green;
		}

		code {
			font-family: "Consolas", "Courier New", Courier, monospace;
			color: #555;
		}

		main {
			max-width: 96vw;
			margin: 0 auto;
			padding: 1em 1em;
		}

		main section#home>.content {
			text-align: center;
		}

		main section#home h1 {
			font-size: 2em;
			font-weight: 400;
			margin-top: 0;
		}

		main section#home h3 {
			font-size: 1.25em;
			font-weight: 600;
		}

		pre.gateway-options {
			display: inline-block;
			text-align: left;
			font-size: 0.9em;
		}

		.boxes {
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
		}

		.boxes .box {
			width: 200px;
			padding: 0.25em 1em;
			margin: 0.5em;
			background: rgba(60, 175, 206, 0.1);

			border: 1px solid grey;
			border-radius: 0.25em;
		}

		.boxes .box .caption {
			font-weight: 300;
			font-size: 0.9em;
			margin-bottom: 0.5em;
		}

		.boxes .box .value {
			font-weight: 600;
			font-size: 1.1em;
		}

		main input {
			border: 1px solid #3cafce;
			border-radius: 4px;
			padding: 2px 6px;
			font-family: "Source Sans Pro";
		}

		main fieldset {
			border: 1px solid lightgrey;
			border-radius: 8px;
			box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.4);
			background-color: aliceblue;
			margin-bottom: 2em;
		}

		main fieldset legend {
			background-color: #cce7ff;
			border: 1px solid lightgrey;
			padding: 4px 10px;
			border-radius: 8px;
		}

		main fieldset .content {
			padding-left: 2em;
		}

		main fieldset .request {
			margin-bottom: 0.5em;
		}

		main fieldset .parameters .field {
			margin-bottom: 0.25em;
		}

		main fieldset .parameters .field label {
			min-width: 80px;
			display: inline-block;
			text-align: right;
			margin-right: 0.5em;
		}

		main fieldset .response {
			margin-top: 1em;
		}

		main fieldset .response pre {
			margin: 0.5em 0;
			font-size: 0.9em;
		}

		pre.json .string {
			color: #885800;
		}

		pre.json .number {
			color: blue;
		}

		pre.json .boolean {
			color: magenta;
		}

		pre.json .null {
			color: red;
		}

		pre.json .key {
			color: green;
		}

		main h4 {
			font-weight: 600;
			margin: 0.25em -1em;
		}

		.badge {
			display: inline-block;
			background-color: dimgray;
			color: white;
			padding: 2px 6px;
			border-radius: 7px;
			font-size: 0.7em;
			font-weight: 600;
		}

		.badge.green {
			background-color: limegreen;
		}

		.badge.red {
			background-color: firebrick;
		}

		.badge.orange {
			background-color: #fab000;
			color: black;
		}

		table {
			width: 100%;
			/*max-width: 1000px;*/
			border: 1px solid lightgrey;
			border-radius: 8px;
			background-color: aliceblue;
		}

		table th {
			padding: 2px 4px;
			background-color: #cce7ff;
			border-radius: 4px;
		}

		table tr.offline td {
			font-style: italic;
			color: #777;
		}

		table tr.local td {
			/*color: blue;*/
		}

		table tr:not(:last-child) td {
			border-bottom: 1px solid #ddd;
		}

		table td {
			text-align: center;
			position: relative;
			/* padding: 2px 4px; */
		}

		table th:nth-child(1),
		table td:nth-child(1) {
			text-align: left;
		}

		table tr.service td:nth-child(1) {
			font-weight: bold;
		}

		table tr.action td:nth-child(1) {
			padding-left: 2em;
		}

		table tr td:nth-child(2) {
			font-family: monospace;
			font-size: 0.8em;
		}

		.bar {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			width: 0;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);
		}
	</style>
</head>

<body>
	<div id="app">
		<header>
			<div style="font-size: 24px">

				
				龙坤智慧MDC监控
				<span v-if="gateway">{{gateway.config.agentCode}}</span>
			</div>
			<nav>
				<ul>
					<li v-for="item in menu" :class="{ active: page == item.id}" @click="changePage(item.id)">
						{{ item.caption }}
					</li>
				</ul>
			</nav>
		</header>

		<main>
			<section id="home" v-if="page == 'home'">
				<div class="content">
					<!-- <h1>欢迎使用Smart·XDC采集主机</h1> -->
					<h1 v-if="gateway===undefined" style="color: red">
						采集程序未启动
					</h1>
					<template v-if="gateway">
						<h3>配置信息</h3>
						<div class="boxes">
							<div class="box">
								<div class="caption">MQTT服务器</div>
								<div class="value">
									{{
									gateway.config.mqtt_cloud_ip+':'+gateway.config.mqtt_cloud_port}}
								</div>
							</div>

							<div class="box" @click="changePage('devices')">
								<div class="caption">设备数量</div>
								<div class="value">{{ gateway.devicesCount }}</div>
							</div>
							<div class="box">
								<div class="caption">参数数量</div>
								<div class="value">{{ gateway.deviceParamsCount }}</div>
							</div>
							<div class="box" @click="changePage('alarms')">
								<div class="caption">告警数量</div>
								<div class="value">{{ gateway.alarmEventLogsCount }}</div>
							</div>

							<div class="box" @click="changePage('disalarms')">
								<div class="caption">解除告警数量</div>
								<div class="value">{{ gateway.disalarmEventLogsCount }}</div>
							</div>
						</div>
						<div class="boxes">
							<div class="box">
								<div class="caption">数据服务版本</div>
								<div class="value">
									{{ gateway.version.setupVer }}
								</div>
							</div>

							<div class="box" @click="changePage('devices')">
								<div class="caption">采集服务版本</div>
								<div class="value">{{ xdc_collector_version }}</div>
							</div>
							<div class="box">
								<div class="caption">告警服务版本</div>
								<div class="value">{{ xdc_alarm_version }}</div>
							</div>
							<!-- <div class="box">
								<div class="caption">告警数量</div>
								<div class="value">{{ gateway.alarmEventLogsCount }}</div>
							</div>

							<div class="box">
								<div class="caption">解除告警数量</div>
								<div class="value">{{ gateway.disalarmEventLogsCount }}</div>
							</div> -->
						</div>

						<!-- <h3>服务器配置</h3>
						<pre v-if="showBrokerOptions" class="gateway-options"><code>{{ gateway }}</code></pre> -->
					</template>
				</div>
			</section>

			<section id="devices" v-if="page == 'devices'">
				<a class="buttonRed" @click="XDCCollectorExit"> 重启采集程序 </a>
				<a class="button" @click="deviceEnableAll" style="margin-left: 5px">
					全部启用
				</a>
				<a class="buttonRed" @click="deviceDisableAll" style="margin-left: 5px">
					全部禁用
				</a>
				<a class="button" @click="deviceDebugEnableAll" style="margin-left: 5px">
					全部启用调试
				</a>
				<a class="buttonRed" @click="deviceDebugDisableAll" style="margin-left: 5px">
					全部禁用调试
				</a>
				<table>
					<thead>
						<th>ID</th>
						<!-- <th>项目Code</th> -->
						<!-- <th>主机Code</th> -->
						<th>名称/设备ID</th>
						<th>设备品牌/型号/类型</th>
						<th>通讯参数</th>
						<th>地址</th>
						<th>DataType</th>
						<th>调试</th>
						<th>状态</th>
						<th>操作</th>
					</thead>
					<tbody>
						<tr v-for="(d,idx) in devices" :key="d.deviceId"
							:style="{ fontSize: '10px', background: d.health==='异常'?'#ff000030':'' }">
							<td style="padding: 2px">{{ idx+1 }}</td>
							<!-- <td>{{ d.projectCode }}</td> -->
							<!-- <td>{{ d.agentCode }}</td> -->
							<td>
								<div>{{ d.deviceId }}</div>
								<div>{{ d.deviceName }}</div>
								<div>
									{{ d.deviceLocationId.split('/').slice(4).join('/') }}
								</div>
							</td>
							<td>
								<div>{{ d.deviceBrand }}</div>
								<div>{{ d.deviceModel }}</div>
								<div>{{ d.deviceSystem }} - {{ d.deviceType }}</div>
							</td>
							<td>{{ d.comSetting }}</td>
							<td v-if="d.comAddress>256">{{ ipFromLong(d.comAddress) }}</td>
							<td v-else>{{ d.comAddress }}</td>
							<td>{{ d.comDataType }}</td>

							<td>
								<!-- {{ d.debug?'打开':'关闭' }} -->
								<a v-if="d.debug" class="buttonRed" style="margin-left:20px;" @click="deviceDebugDisable(d)">关闭</a><a
									v-else class="button" style="margin-left:20px;" @click="deviceDebugEnable(d)">打开</a>
							</td>
							<td>
								<span v-if="d.health==='异常'" style="background:red;padding:3px;color:white">{{d.health}}</span>
								<span v-else-if="d.health==='正常'" style="background:green;padding:3px;color:white">{{d.health}}</span>
								<span v-else style="background:yellow;padding:3px;color:white">{{d.health}}</span>
							</td>
							<td>

								<a v-if="d.deviceState === 0" class="buttonRed" @click="deviceEnable(d)">
									启用
								</a>
								<a v-if="d.deviceState === 1" class="button" @click="deviceDisable(d)">
									禁用
								</a>
								<a class="button" @click="showDeviceParams(d)" style="margin-left: 5px">
									参数详情
								</a>
							</td>
						</tr>
					</tbody>
				</table>
			</section>

			<section id="deviceParams" v-if="page == 'deviceParams'" style="font-size: 10px">
				<div v-if="workingDevice" style="height:36px;line-height:36px;text-align:center;">
					{{workingDevice.deviceId}}
				</div>
				<a class="buttonRed" @click="showDeviceParams(workingDevice)">
					刷新
				</a>
				<table>
					<thead>
						<th>#</th>
						<th>名称</th>
						<th>参数Code</th>
						<th>是否告警通知</th>
						<th>值</th>
						<th>公式</th>
						<th>起始</th>
						<th>长度</th>
						<th>引用参数</th>
						<th>CrcType</th>
						<th>协议</th>
						<th>TX</th>
						<th>更新时间</th>
						<th style="width: 200px">告警规则</th>
					</thead>
					<tbody style="font-size: 8px">
						<tr v-for="(dp,idx) in deviceParams" v-bind:key="'dp_'+idx">
							<td>{{idx+1}}</td>
							<td style="text-align: left">{{ dp.paramName }}({{dp.paramDataType}})</td>
							<td style="text-align: left">{{ dp.paramCode }}
								<div @click="sendControlOnOff(dp.deviceId,dp.paramCode)"
									v-if="dp.paramDataType.indexOf('Control')!==-1 && (dp.paramCode.endsWith('ON') || dp.paramCode.endsWith('OFF'))"
									style="margin-left:5px;padding:2px;background:red;text-align:center;width:100px;">
									控制
								</div>
								<div v-else-if="dp.paramDataType.indexOf('Control')!==-1">
									<span style="background:red;padding:2px;width:50px;"
										@click="sendControlAnalogy(dp.deviceId,dp.paramCode,20)">20</span>
									<span style="background:green;padding:2px;margin-left:10px;width:50px;"
										@click="sendControlAnalogy(dp.deviceId,dp.paramCode,21)">21</span>
								</div>
							</td>
							<td>{{dp.alarmEnable?'是':'否'}}</td>
							<td v-if="dp.paramDataType==='CommStatus'">
								<div v-if="nowValuesMap[dp.paramCode]&&nowValuesMap[dp.paramCode].v===0"
									style="background: green; color: white; padding: 5px">
									通讯正常{{nowValuesMap[dp.paramCode]&&nowValuesMap[dp.paramCode].v}}
								</div>
								<div v-else-if="nowValuesMap[dp.paramCode]!==undefined"
									style="background: red; color: white; padding: 5px">
									通讯异常{{nowValuesMap[dp.paramCode]&&nowValuesMap[dp.paramCode].v}}
								</div>
							</td>
							<td v-else-if="nowValuesMap[dp.paramCode]">
								{{nowValuesMap[dp.paramCode].v}}
							</td>
							<td>{{ dp.dataCalib }}</td>
							<td>{{ dp.dataStartPos || 0 }}</td>
							<td>{{ dp.dataValidLen || 0}} {{dp.now}}</td>
							<td>{{ dp.refParamCode}}</td>
							<td>{{ dp.dataCrcType}}</td>
							<td>{{ dp.comType }}</td>
							<td>{{ dp.tx }}</td>
							<td v-if="nowValuesMap[dp.paramCode] && nowValuesMap[dp.paramCode].v !== '未知'">
								{{ dayjs(nowValuesMap[dp.paramCode].t).format('YYYY-MM-DD HH:mm:ss') }}
							</td>
							<td v-else>
								<span style="color:red;font-weight:400;">更新时间-未知</span>
							</td>
							<td style="width: 200px; font-size: 8px">{{dp.alarmRule}}</td>
						</tr>
					</tbody>
				</table>
			</section>

			<section id="alarms" v-if="page == 'alarms'" style="font-size: 10px">
				<a class="buttonRed" @click="updateAlarmList"> 刷新 </a>
				<a class="buttonRed" @click="clearAlarmList"> 清空日志 </a>
				<table>
					<thead>
						<th>#</th>
						<th>eventId</th>
						<th>时间</th>
						<th>设备ID</th>
						<th>设备</th>
						<th>参数</th>
						<th>值</th>
						<th>内容</th>
					</thead>
					<tbody>
						<tr v-for="(alarm,idx) in alarmEventLogs" v-bind:key="'alarm_'+idx">
							<td style="text-align: left">{{idx+1}}</td>
							<td style="width: 200px">{{alarm.eventId}}</td>
							<td style="text-align: left">
								{{dayjs(alarm.alarmTime).format('YYYY-MM-DD HH:mm:ss')}}
							</td>
							<td style="text-align: left">{{alarm.deviceId}}</td>
							<td style="text-align: left">
								{{alarm.alarmLocation}}-{{alarm.deviceName}}
							</td>
							<td style="text-align: left">{{alarm.paramCode}}</td>
							<td style="text-align: left">{{alarm.paramValue}}</td>
							<td style="text-align: left">{{alarm.alarmContent}}</td>
						</tr>
					</tbody>
				</table>
			</section>

			<section id="disalarms" v-if="page == 'disalarms'" style="font-size: 10px">
				<a class="buttonRed" @click="updateAlarmList"> 刷新 </a>
				<a class="buttonRed" @click="clearDisalarmList"> 清空日志 </a>
				<table>
					<thead>
						<th>#</th>
						<th>eventId</th>
						<th>时间</th>
						<th>设备ID</th>
						<th>设备</th>
						<th>参数</th>
						<th>值</th>
						<th>内容</th>
					</thead>
					<tbody>
						<tr v-for="(alarm,idx) in disalarmEventLogs" v-bind:key="'disalarm_'+idx">
							<td>{{idx+1}}</td>
							<td style="width: 200px">{{alarm.eventId}}</td>
							<td style="text-align: left">
								{{dayjs(alarm.alarmTime).format('YYYY-MM-DD HH:mm:ss')}} ~
								{{dayjs(alarm.disalarmTime).format('YYYY-MM-DD HH:mm:ss')}}
							</td>
							<td style="text-align: left">{{alarm.deviceId}}</td>
							<td style="text-align: left">{{alarm.alarmLocation}} - {{alarm.deviceName}}</td>
							<td style="text-align: left">{{alarm.paramCode}}</td>
							<td style="text-align: left">{{alarm.paramValue}}</td>
							<td style="text-align: left">{{alarm.alarmContent}}</td>
						</tr>
					</tbody>
				</table>
			</section>
		</main>

		<footer>
			<div class="footer-copyright">Copyright 2023 &copy; 龙坤（无锡）智慧科技有限公司</div>
		</footer>
	</div>
	<script type="text/javascript">
		var app = new Vue({
			el: "#app",

			data() {
				return {
					menu: [
						{ id: "home", caption: "概要" },
						{ id: "devices", caption: "设备" },
						{ id: "deviceParams", caption: "参数详情" },
						{ id: "alarms", caption: "告警日志" },
						{ id: "disalarms", caption: "解除告警日志" },
					],
					page: "home",

					gateway: null,
					nodes: [],
					devices: [],
					services: [],
					actions: {},

					showBrokerOptions: true,

					deviceParams: [],
					nowValuesMap: [],
					workingDevice: undefined,
					alarmCount: 0,
					alarmEventLogs: [],
					disalarmCount: 0,
					disalarmEventLogs: [],
					xdc_alarm_version: '',
					xdc_collector_version: ''
				};
			},
			methods: {
				ipFromLong(ipl) {
					return (
						(ipl >>> 24) +
						"." +
						((ipl >> 16) & 255) +
						"." +
						((ipl >> 8) & 255) +
						"." +
						(ipl & 255)
					);
				},
				getDateString(t) {
					return dayjs(t).format("YYYY-MM-DD hh:mm:ss");
				},
				changePage(page) {
					this.page = page;
					this.updatePageResources();
				},

				XDCCollectorExit: function () {
					this.req("/api/gw/XDCCollectorExit", null).then((res) => { });
				},

				sendControlOnOff: async function (deviceId, paramCode) {
					await this.post("/api/xdc_collector/deviceControl", {
						"code": "204", //
						"data": {
							"paramid": paramCode, //
							"deviceid": deviceId, //
						}
					});
				},
				sendControlAnalogy: async function (deviceId, paramCode, value) {
					await this.post("/api/xdc_collector/deviceControl", {
						"code": "203", //
						"data": {
							"paramid": paramCode, //
							"deviceid": deviceId, //
							"value": value,
						}
					});
				},
				// 设备启用
				deviceEnable: async function (device) {
					await this.req("/api/gw/deviceEnable?id=" + device.id, null);
					this.updateDeviceList();
				},
				// 全部启用
				deviceEnableAll: async function (device) {
					for (let i = 0; i < this.devices.length; i++) {
						let device = this.devices[i];
						console.log("启用", device.id);
						await this.req("/api/gw/deviceEnable?id=" + device.id, null);
					}
					this.updateDeviceList();
				},
				// 禁用
				deviceDisable: async function (device) {
					await this.req("/api/gw/deviceDisable?id=" + device.id, null);
					this.updateDeviceList();
				},
				// 全部禁用
				deviceDisableAll: async function (device) {
					for (let i = 0; i < this.devices.length; i++) {
						let device = this.devices[i];
						console.log("禁用", device.id);
						await this.req("/api/gw/deviceDisable?id=" + device.id, null);
					}
					this.updateDeviceList();
				},
				// 设备调试启用
				deviceDebugEnable: async function (deviceDebug) {
					await this.req("/api/gw/deviceDebugEnable?id=" + deviceDebug.id, null);
					this.updateDeviceList();
				},
				// 全部调试启用
				deviceDebugEnableAll: async function () {
					for (let i = 0; i < this.devices.length; i++) {
						let device = this.devices[i];
						console.log("启用", device.id);
						device.debug = true
						await this.req("/api/gw/deviceDebugEnable?id=" + device.id, null);
					}
					this.updateDeviceList();
				},
				// 禁用调试
				deviceDebugDisable: async function (deviceDebug) {
					await this.req("/api/gw/deviceDebugDisable?id=" + deviceDebug.id, null);
					this.updateDeviceList();
				},
				// 全部调试禁用
				deviceDebugDisableAll: async function () {
					for (let i = 0; i < this.devices.length; i++) {
						let device = this.devices[i];
						console.log("禁用", device.id);
						device.debug = false
						await this.req("/api/gw/deviceDebugDisable?id=" + device.id, null);
					}
					this.updateDeviceList();
				},
				// 获取网关信息
				getGatewayInfo: function (name) {
					this.req("/api/gw/info", null).then((res) => {
						this.gateway = res.info;
					});
					this.req("/api/xdc_alarm/version", null).then((res) => {
						console.log('xdc_alarm version', res)
						this.xdc_alarm_version = res
					});
					this.req("/api/xdc_collector/version", null).then((res) => {
						console.log('xdc_collector version', res)
						this.xdc_collector_version = res
					});
					this.updateAlarmList();
				},
				// 更新设备列表
				updateDeviceList: function (name) {
					this.req("/api/gw/devices", null).then((res) => {
						res = res.devices;
						res.sort((a, b) => {
							return a.health.localeCompare(b.health)
							//return a.id - b.id;
						});
						this.devices = res;
					});
				},

				getDeviceParamsNowByDeviceId: async function (device) {
					// 获取DeviceId对应的所有DeviceParamsNow 当前实时值
					let res = await this.req(
						"/api/gw/getDeviceParamsNowByDeviceId?deviceId=" +
						device.deviceId,
						null
					);
					for (let i = 0; i < res.vs.length; i++) {
						let k = res.vs[i].k.replace(device.deviceId + "_", "");
						console.log(k, res.vs[i].t)
						this.nowValuesMap[k] = { v: res.vs[i].v, t: res.vs[i].t };
					}
				},

				getDeviceParamsByDeviceId: async function (device) {
					// 获取DeviceId对应的所有DeviceParams
					let res = await this.req(
						"/api/gw/getDeviceParamsByDeviceId?deviceId=" + device.deviceId,
						null
					);
					let deviceParams = [];
					for (let i = 0; i < res.deviceParams.length; i++) {
						let dp = res.deviceParams[i];
						if (dp.alarmEnable === null) {
							dp.alarmEnable = true;
						}
						dp.dataExtra = device.dataExtra
						dp.comType = device.comType;
						deviceParams.push(dp);
						this.nowValuesMap[dp.paramCode] = {
							v: "未知",
							t: new Date().getTime(),
						};
					}
					await this.getDeviceParamsNowByDeviceId(device);
					this.deviceParams = deviceParams;
				},
				showDeviceParams: function (device) {
					this.workingDevice = device;
					this.page = "deviceParams";
					this.nowValuesMap = {};
					this.getDeviceParamsByDeviceId(device);
				},

				// 清空 解除告警日志
				clearDisalarmList: async function () {
					await this.post("/api/disalarmEventLogs/clearDatabase", null);
					this.updateDisalarmList();
				},

				// 清空 告警日志
				clearAlarmList: async function () {
					await this.post("/api/alarmEventLogs/clearDatabase", null);
					this.updateAlarmList();
				},

				// 更新最新告警
				updateAlarmList: async function () {
					let count = await this.post("/api/alarmEventLogs/count", null);
					let rslt = await this.post(
						"/api/alarmEventLogs/getLatest100Logs",
						{}
					);
					this.alarmEventLogs = rslt;
					this.alarmCount = count;
					this.menu = [
						{ id: "home", caption: "概要" },
						{ id: "devices", caption: "设备" },
						{ id: "deviceParams", caption: "参数详情" },
						{ id: "alarms", caption: "告警日志(" + this.alarmCount + ")" },
						{
							id: "disalarms",
							caption: "解除告警日志(" + this.disalarmCount + ")",
						},
					];
				},
				// 更新 解除告警 日志
				updateDisalarmList: async function () {
					let count = await this.post("/api/disalarmEventLogs/count", null);
					if (this.disalarmCount !== count) {
						let rslt = await this.post(
							"/api/disalarmEventLogs/getLatest100Logs",
							{}
						);
						this.disalarmEventLogs = rslt;
					}
					this.disalarmCount = count;
					this.menu = [
						{ id: "home", caption: "概要" },
						{ id: "devices", caption: "设备" },
						{ id: "deviceParams", caption: "参数详情" },
						{ id: "alarms", caption: "告警日志(" + this.alarmCount + ")" },
						{
							id: "disalarms",
							caption: "解除告警日志(" + this.disalarmCount + ")",
						},
					];
				},
				req: function (url, params) {
					return fetch(url, {
						method: "GET",
						body: params ? JSON.stringify(params) : null,
					}).then(function (res) {
						return res.json();
					});
				},
				post: function (url, params) {
					return fetch(url, {
						method: "POST",
						headers: {
							'Content-Type': 'application/json'
						},
						body: params ? JSON.stringify(params) : null,
					}).then(function (res) {
						return res.json();
					});
				},
				updatePageResources() {
					if (this.page == "devices") return this.updateDeviceList();
					if (this.page == "alarms") return this.updateAlarmList();
					if (this.page == "disalarms") return this.updateDisalarmList();
				},
			},

			mounted() {
				var self = this;
				setInterval(function () {
					self.updatePageResources();
				}, 10 * 1000);
				this.getGatewayInfo();
				this.updateAlarmList();
				this.updateDisalarmList();
			},
		});
	</script>
</body>

</html>
