package com.hongxuan.xs.jdbcutil.controller;

import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hongxuan.xs.jdbcutil.entity.CommonResult;
import com.hongxuan.xs.jdbcutil.entity.SyncInfo;
import com.hongxuan.xs.jdbcutil.entity.SyncInfo2;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
@RequestMapping("/sync2")
@Slf4j
public class Sync2Controller {

    private JdbcTemplate jdbcTemplate;

    private String token;


    /**
     * 同步嵌入式
     *
     * @return
     */
    @RequestMapping("/syncAgent")
    public CommonResult syncAgent(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);
        if (commonResult != null) {
            return commonResult;
        }

        truncate(syncInfo, "agent");
        JSONObject whereObj = new JSONObject();
        whereObj.set("code", syncInfo.getPrj());
        String result = getResult(syncInfo, whereObj);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");


        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        jsonArray.forEach(obj -> {

            JSONObject x = JSONUtil.parseObj(obj);

            JSONArray gateways = x.getJSONArray("gateways");

            gateways.forEach(gateway -> {
                JSONObject gatewayObj = JSONUtil.parseObj(gateway);

                // 先判断数据是否存在，存在更新，不存在删除。
                String selectSql = "select * from agent where Agentbm ='{}'";
                List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, gatewayObj.getStr("code")));

                if (queryForList.size() == 0) {
                    // 新增
                    String insertSqlTemp = "insert into agent (Agentbm,AgentName,IP,URL,State,SQLIp,SQLPort,SQLName,SQLUser,SQLPassword,AddrId) " +
                            "values('{}','{}','{}','{}',1,'{}','{}','{}','{}','{}','{}')";

                    String insertSql = StrUtil.format(insertSqlTemp,
                            gatewayObj.getStr("code"),
                            gatewayObj.getStr("name"),
                            gatewayObj.getStr("ip1"),
                            "http://" + gatewayObj.getStr("ip1") + ":8888/api/deviceParams/updateByDeviceIdAndParamCode",
                            gatewayObj.getStr("databaseIP"),
                            gatewayObj.getStr("databasePort"),
                            gatewayObj.getStr("databaseName"),
                            gatewayObj.getStr("databaseUsername"),
                            gatewayObj.getStr("databasePassword"),
                            gatewayObj.getStr("deviceLocationId"));

                    log.info("插入：{}", insertSql);
                    jdbcTemplate.execute(insertSql);

                } else {
                    // 更新
                    String updateSqlTemp = "update agent set AgentName='{}',IP='{}',URL='{}',SQLIp='{}',SQLPort='{}',SQLName='{}',SQLUser='{}',SQLPassword='{}',AddrId='{}' where Agentbm = '{}'";
                    String updateSql = StrUtil.format(updateSqlTemp,
                            gatewayObj.getStr("name"),
                            gatewayObj.getStr("ip1"),
                            "http://" + gatewayObj.getStr("ip1") + ":8888/api/deviceParams/updateByDeviceIdAndParamCode",
                            gatewayObj.getStr("databaseIP"),
                            gatewayObj.getStr("databasePort"),
                            gatewayObj.getStr("databaseName"),
                            gatewayObj.getStr("databaseUsername"),
                            gatewayObj.getStr("databasePassword"),
                            gatewayObj.getStr("deviceLocationId"), gatewayObj.getStr("code"));

                    log.info("更新：{}", updateSql);
                    jdbcTemplate.execute(updateSql);

                }


            });


        });


        log.info("同步结束。。");
        resultList.add("同步完成。");
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("result", resultList);
        jsonObject.set("error", errorList);
        return CommonResult.success(jsonObject);

    }


    /**
     * 同步地点
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncAddress")
    public CommonResult syncAddress(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "topology_site");

        JSONObject whereObj = new JSONObject();
        whereObj.set("projectCode", syncInfo.getPrj());
        String result = getResult(syncInfo, whereObj);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        AtomicInteger addressSize = new AtomicInteger();
        jsonArray.forEach(obj -> {
            JSONObject x = JSONUtil.parseObj(obj);
            if (x.getInt("type") >= 4) {
                boolean flag = false;
                addressSize.getAndIncrement();

                // 判断是否存在，存在就更新
                String selectSql = "select * from topology_site where AddressId='" + x.getStr("key") + "'";

                List<Map<String, Object>> selectResult = jdbcTemplate.queryForList(selectSql);

                if (selectResult.size() == 0) {
                    String insertSql = "";
                    if (x.getInt("type") == 4) {
                        String tempString = "insert into topology_site (AddressId,AddressCode,AddressName,ParentId,AddressType) values('{}','{}','{}','-1',0);";
                        insertSql = StrFormatter.format(tempString, x.getStr("key"), x.getStr("key"), x.getStr("title"));
                    } else {
                        String tempString = "insert into topology_site (AddressId,AddressCode,AddressName,ParentId,AddressType) values('{}','{}','{}','{}',{});";
                        insertSql = StrFormatter.format(tempString, x.getStr("key"), x.getStr("key"), x.getStr("title"), x.getStr("parentId"), x.getInt("type") - 3);
                    }

                    jdbcTemplate.execute(insertSql);

                } else {

                    String updateSqlTemp = "update topology_site set AddressName='{}' where AddressId = '{}'";
                    String updateSql = StrFormatter.format(updateSqlTemp, x.getStr("title"), x.getStr("key"));
                    jdbcTemplate.execute(updateSql);

                }

            }

        });
        log.info("同步结束。。");
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("result", resultList);
        jsonObject.set("error", errorList);
        return CommonResult.success(jsonObject);

    }


    /**
     * 同步设备类型
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncDeviceType")
    public CommonResult syncDeviceType(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "device_type");


        String result = getResult(syncInfo, null);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        jsonArray.forEach(obj -> {

            JSONObject x = JSONUtil.parseObj(obj);

            // 查询环境系统信息
            JSONObject whereObj = new JSONObject();
            whereObj.set("name", x.getStr("system"));

            String brandListStr = getResultByWhere(syncInfo, "System", whereObj);
            JSONArray systemListArray = JSONUtil.parseArray(JSONUtil.parseObj(brandListStr).get("results"));

            JSONObject systemObj = JSONUtil.parseObj(systemListArray.get(0));
            String systemCode = systemObj.getStr("code");

            // 先判断有没有

            String selectSql = "select * from device_type where TypeId = '{}'";

            List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, x.getStr("code")));

            if (queryForList.size() == 0) {
                String insertSqlTemp = "insert into device_type(TypeId,TypeName,DeviceSystemId,ParentId,IsDeleted) values('{}','{}','{}','{}',0)";
                String insertSql = StrUtil.format(insertSqlTemp, x.getStr("code"), x.getStr("name"), systemCode, x.getStr("parent"));
                jdbcTemplate.execute(insertSql);
                resultList.add("插入--->：" + x.getStr("name"));

            } else {
                String updateSqlTemp = "update device_type set TypeName='{}',DeviceSystemId='{}',ParentId='{}' where TypeId = '{}'";
                String updateSql = StrUtil.format(updateSqlTemp, x.getStr("name"), systemCode, x.getStr("parent"), x.getStr("code"));
                jdbcTemplate.execute(updateSql);
                resultList.add("更新--->：" + x.getStr("name"));
            }


        });

        log.info("同步结束。。");
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("result", resultList);
        jsonObject.set("error", errorList);
        return CommonResult.success(jsonObject);

    }

    /**
     * 同步品牌
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncBrand")
    public CommonResult syncBrand(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "device_brand");


        String result = getResult(syncInfo, null);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        jsonArray.forEach(obj -> {

            JSONObject x = JSONUtil.parseObj(obj);
            // 先判断有没有

            String selectSql = "select * from device_brand where BrandId = '{}'";

            List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, x.getStr("code")));

            String deviceType = x.getJSONArray("deviceType").join("\\\\").replaceAll("\"", "");

            if (queryForList.size() == 0) {
                String insertSqlTemp = "insert into device_brand(BrandId,BrandName,BrandDesc,IsDeleted) values('{}','{}','{}',0)";
                String insertSql = StrUtil.format(insertSqlTemp, x.getStr("code"), x.getStr("name"), deviceType);
                jdbcTemplate.execute(insertSql);
                resultList.add("插入--->：" + x.getStr("name"));

            } else {
                String updateSqlTemp = "update device_brand set BrandName='{}',BrandDesc='{}' where BrandId = '{}'";
                String updateSql = StrUtil.format(updateSqlTemp, x.getStr("name"), deviceType, x.getStr("code"));
                jdbcTemplate.execute(updateSql);
                resultList.add("更新--->：" + x.getStr("name"));
            }

        });

        log.info("同步结束。。");
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("result", resultList);
        jsonObject.set("error", errorList);
        return CommonResult.success(jsonObject);

    }

    /**
     * 同步型号
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncModel")
    public CommonResult syncModel(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "device_model");


        String result = getResult(syncInfo, null);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        jsonArray.forEach(obj -> {

            JSONObject x = JSONUtil.parseObj(obj);

            // 查询对应的品牌
            JSONObject whereObj = new JSONObject();
            whereObj.set("name", x.getStr("brand"));

            String brandListStr = getResultByWhere(syncInfo, "Brand", whereObj);
            JSONArray systemListArray = JSONUtil.parseArray(JSONUtil.parseObj(brandListStr).get("results"));
            if (systemListArray.size() == 0) {
                errorList.add("品牌：" + x.getStr("brand") + "，查不到");
                return;
            }

            JSONObject systemObj = JSONUtil.parseObj(systemListArray.get(0));

            String brandCode = systemObj.getStr("code");

            // 先判断有没有

            String selectSql = "select * from device_model where ModelId = '{}'";

            List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, x.getStr("code")));

            if (queryForList.size() == 0) {
                String insertSqlTemp = "insert into device_model(ModelId,ModelName,ModelDesc,BrandId,IsDeleted) values('{}','{}','{}','{}',0)";
                String insertSql = StrFormatter.format(insertSqlTemp, x.getStr("code"), x.getStr("name"), x.getStr("memo"), brandCode);
                jdbcTemplate.execute(insertSql);
                resultList.add("插入--->：" + x.getStr("name"));

            } else {
                String updateSqlTemp = "update device_model set ModelName='{}',ModelDesc='{}',BrandId='{}' where ModelId = '{}' ";
                String updateSql = StrUtil.format(updateSqlTemp, x.getStr("name"), x.getStr("memo"), brandCode, x.getStr("code"));
                jdbcTemplate.execute(updateSql);
                resultList.add("更新--->：" + x.getStr("name"));
            }


        });

        log.info("同步结束。。");
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("result", resultList);
        jsonObject.set("error", errorList);
        return CommonResult.success(jsonObject);

    }

    /**
     * 同步设备
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncDevice")
    public CommonResult syncDevice(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "device");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        JSONObject jsonObject = getParam();
        JSONObject whereObj = new JSONObject();
        whereObj.set("projectCode", syncInfo.getPrj());
        jsonObject.set("where", whereObj);
        String tempUrl = "http://{}:{}/engineering_platform/classes/";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());

        //根据project code 获取产品信息
        String result1 = HttpRequest.post(httpBaseUrl + "ProjectProduct")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray productArray = JSONUtil.parseObj(result1).getJSONArray("results");

        Map<String, JSONObject> productMap = new HashMap<>();

        // 缓存产品信息
        productArray.forEach(product -> {
            JSONObject productJsonObj = JSONUtil.parseObj(product);
            productMap.put(productJsonObj.getStr("model"), productJsonObj);

        });


        if ("option".equals(syncInfo.getAgentRadio())) {
            whereObj.set("agentCode", syncInfo.getAgentBm());
        }
        jsonObject.set("where", whereObj);

        //根据project code 获取设备信息
        String result2 = HttpRequest.post(httpBaseUrl + "ProjectDevice")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray deviceArray = JSONUtil.parseObj(result2).getJSONArray("results");


        log.info("共{}个设备", deviceArray.size());
        AtomicInteger len = new AtomicInteger(1);
        AtomicInteger insertCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();
        deviceArray.forEach(device -> {

            JSONObject deviceJsonObj = JSONUtil.parseObj(device);


            // 去parse 查型号
            JSONObject modelWhereObj = new JSONObject();
            modelWhereObj.set("name", deviceJsonObj.getStr("deviceModel"));
            String resultStr = getResultByWhere(syncInfo, "Model", modelWhereObj);
            JSONArray modelListArray = JSONUtil.parseArray(JSONUtil.parseObj(resultStr).get("results"));

            // 去parse 查品牌
            JSONObject brandWhereObj = new JSONObject();
            brandWhereObj.set("name", deviceJsonObj.getStr("deviceBrand"));
            String resultBrandStr = getResultByWhere(syncInfo, "Brand", brandWhereObj);
            JSONArray brandListArray = JSONUtil.parseArray(JSONUtil.parseObj(resultBrandStr).get("results"));


            // 去parse 查设备类型
            JSONObject deviceTypeWhereObj = new JSONObject();
            deviceTypeWhereObj.set("name", deviceJsonObj.getStr("deviceType"));
            String resultStr2 = getResultByWhere(syncInfo, "DeviceType", deviceTypeWhereObj);
            JSONArray deviceTypeListArray = JSONUtil.parseArray(JSONUtil.parseObj(resultStr2).get("results"));

            JSONObject product;
          /*  if (StrUtil.isNotBlank(deviceJsonObj.getStr("rules")) && JSONUtil.parseArray(deviceJsonObj.getStr("rules")).size() != 0) {
               log.info("{}",deviceJsonObj.getStr("rules"));
                product = JSONUtil.parseObj(deviceJsonObj.getStr("rules"));
                log.info("device rules :{}", product);
            } else {
                product = productMap.get(deviceJsonObj.getStr("deviceModel"));
            }
*/
            product = productMap.get(deviceJsonObj.getStr("deviceModel"));

            if (product == null) {
                errorList.add("产品：" + deviceJsonObj.getStr("deviceModel") + "，不存在");
                return;
            }

            log.info(" {},设备：{}", len.getAndIncrement(), deviceJsonObj.getStr("deviceName"));

            String brandId = "";
            String modelId = "";
            String typeId = "";
            if (!modelListArray.isEmpty()) {
                JSONObject modelObj = JSONUtil.parseObj(modelListArray.get(0));
                modelId = modelObj.get("code").toString();
            } else {
                errorList.add("型号：" + deviceJsonObj.getStr("deviceModel") + " ,找不到 ，设备：" + deviceJsonObj.getStr("deviceName") + "，将不会被插入");
                log.info("型号：{}，找不到", deviceJsonObj.getStr("deviceModel"));
                return;
            }

            if (!brandListArray.isEmpty()) {
                JSONObject brandObj = JSONUtil.parseObj(brandListArray.get(0));
                brandId = brandObj.get("code").toString();
            } else {
                errorList.add("品牌：" + deviceJsonObj.getStr("deviceBrand") + " ,找不到 ，设备：" + deviceJsonObj.getStr("deviceName") + "，将不会被插入");
                log.info("型号：{}，找不到", deviceJsonObj.getStr("deviceBrand"));
                return;
            }


            if (!deviceTypeListArray.isEmpty()) {
                JSONObject typeObj = JSONUtil.parseObj(deviceTypeListArray.get(0));
                typeId = typeObj.get("code").toString();
            } else {
                errorList.add("设备类型：" + deviceJsonObj.getStr("deviceType") + "，设备：" + deviceJsonObj.getStr("deviceName") + "，将不会被插入");
                log.info("设备类型：{}，找不到", deviceJsonObj.getStr("deviceType"));
                return;
            }


            // 判断 设备是否存在
            String selectSql = "select * from device where DeviceId = '{}'";
            List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, deviceJsonObj.getStr("deviceId")));
            String templateId = StrUtil.isBlank(product.getStr("detailPageId")) ? "" : product.getStr("detailPageId");
            if (queryForList.size() == 0) {

                String tempString = "insert into device (DeviceId,DeviceBrand,DeviceModel,DeviceName,TemplateId," +
                        "IsDeleted,AddressId,DeviceTypeId,Agentbm) " +
                        "" +
                        "values('{}','{}','{}','{}','{}',0,'{}','{}','{}');";
                String insertDeviceSql = StrFormatter.format(tempString, deviceJsonObj.getStr("deviceId"), brandId, modelId,
                        deviceJsonObj.getStr("deviceName"), templateId, deviceJsonObj.getStr("deviceLocationId"), typeId,
                        deviceJsonObj.getStr("agentCode")
                );
                //   resultList.add("插入--->" + deviceJsonObj.getStr("deviceName"));
                insertCount.getAndIncrement();
                jdbcTemplate.execute(insertDeviceSql);

            } else {

                String updateDeviceTempSql = "update device set DeviceBrand='{}',DeviceModel='{}',DeviceName='{}',TemplateId='{}'" +
                        ",AddressId='{}',DeviceTypeId='{}',DeviceTypeId='{}',Agentbm='{}' where deviceId='{}'";

                String updateDeviceSql = StrUtil.format(updateDeviceTempSql, brandId, modelId, deviceJsonObj.getStr("deviceName"), templateId,
                        deviceJsonObj.getStr("deviceLocationId"), deviceJsonObj.getStr("deviceLocationId"), typeId, deviceJsonObj.getStr("agentCode"),
                        deviceJsonObj.getStr("deviceId"));

                jdbcTemplate.execute(updateDeviceSql);
                // resultList.add("更新--->" + deviceJsonObj.getStr("deviceName"));
                updateCount.getAndIncrement();
            }
        });
        log.info("同步结束。。");
        JSONObject result = new JSONObject();
        resultList.add("共插入设备：" + insertCount.get() + "个；更新设备" + updateCount.get() + "个。");
        result.set("result", resultList);
        result.set("error", errorList);
        return CommonResult.success(result);

    }

    /**
     * 同步参数
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncDeviceParams")
    public CommonResult syncDeviceParams(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        truncate(syncInfo, "device_parameter");
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        JSONObject jsonObject = getParam();
        JSONObject whereObj = new JSONObject();
        whereObj.set("projectCode", syncInfo.getPrj());
        jsonObject.set("where", whereObj);

        String tempUrl = "http://{}:{}/engineering_platform/classes/";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());

        //根据project code 获取产品信息
        String result1 = HttpRequest.post(httpBaseUrl + "ProjectProduct")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray productArray = JSONUtil.parseObj(result1).getJSONArray("results");

        Map<String, JSONObject> productMap = new HashMap<>();

        // 缓存产品信息
        productArray.forEach(product -> {
            JSONObject productJsonObj = JSONUtil.parseObj(product);
            productMap.put(productJsonObj.getStr("model"), productJsonObj);

        });
        if ("option".equals(syncInfo.getAgentRadio())) {
            whereObj.set("agentCode", syncInfo.getAgentBm());
        }
        jsonObject.set("where", whereObj);

        //根据project code 获取设备信息
        String result2 = HttpRequest.post(httpBaseUrl + "ProjectDevice")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray deviceArray = JSONUtil.parseObj(result2).getJSONArray("results");


        log.info("共{}个设备", deviceArray.size());
        deviceArray.forEach(device -> {

            JSONObject deviceJsonObj = JSONUtil.parseObj(device);

            JSONArray rules = new JSONArray();

            try {
                if (StrUtil.isNotBlank(deviceJsonObj.getStr("rules")) && JSONUtil.parseArray(deviceJsonObj.getStr("rules")).size() != 0) {
                    rules = JSONUtil.parseArray(deviceJsonObj.getStr("rules"));
                } else {
                    rules = productMap.get(deviceJsonObj.getStr("deviceModel")).getJSONArray("rules");

                }

            } catch (NullPointerException e) {

            }


            log.info("----------");

            // 插入参数


            rules.forEach(o -> {

                log.info("rules:{}", o);
                JSONObject ruleObj = JSONUtil.parseObj(o);
                // 判断 参数是否存在
                String selectSql = "select * from device_parameter where DeviceId = '{}' and ParameterCode = '{}'";

                List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(StrUtil.format(selectSql, deviceJsonObj.getStr("deviceId"), ruleObj.getStr("paramCode")));

                String paramType = ruleObj.getStr("paramDataType");
                Integer type = null;
                String stateParameter = "";
                String alarmLevelJson = "";
                if (paramType == null) {
                    errorList.add("设备：" + deviceJsonObj.getStr("deviceName") + ",参数：" + ruleObj.getStr("paramName") + "，参数类型DataChar ,不能为空");
                    return;
                }

                // JSONObject orig = ruleObj.getJSONObject("orig");


                if ("Digital".equals(paramType) || "CommStatus".equals(paramType) || "Alarm".equals(paramType) || "COMMSTATUS".equals(paramType)) {
                    // 状态量ComStatus
                    type = 1;
                    //stateParameter = "0";
                    alarmLevelJson = "[{\"ScopeType\":1,\"AlarmLevelNumber\":3}]";


                } else if ("Analogy".equals(paramType)) {
                    // 模拟量
                    type = 2;
                    alarmLevelJson = "[{\"AlarmLevelNumber\":3,\"ScopeType\":0}]";
                } else if (paramType.contains("Contr")) {
                    type = 3;
                } else {
                    log.info("设备：" + deviceJsonObj.getStr("deviceName") + ",参数：" + ruleObj.getStr("paramName") + "，不支持的参数类型DataChar:" + paramType);
                    errorList.add("设备：" + deviceJsonObj.getStr("deviceName") + ",参数：" + ruleObj.getStr("paramName") + "，不支持的参数类型DataChar:" + paramType);
                    return;
                }

                String unit = ruleObj.getStr("dataUnit") == null ? "" : ruleObj.getStr("dataUnit");
                boolean alarmEnable = false;
                if (ruleObj.getBool("alarmEnable") != null) {
                    alarmEnable = ruleObj.getBool("alarmEnable");
                }

                int IsAlarmEnable = 0;
                if (alarmEnable) {
                    IsAlarmEnable = 1;
                }

                if (deviceJsonObj.getStr("deviceId").equals("TEVUXWFGTL")) {
                    System.out.println();
                }

                String State0Description = "";
                String State1Description = "";
                String MaxAlarmDescription = "";
                String MaxDisalarmDescription = "";
                String MinAlarmDescription = "";
                String MinDisalarmDescription = "";


                State0Description = StrUtil.isNotBlank(ruleObj.getStr("State0")) ? ruleObj.getStr("State0") : "";
                State1Description = StrUtil.isNotBlank(ruleObj.getStr("State1")) ? ruleObj.getStr("State1") : "";
                MaxAlarmDescription = StrUtil.isNotBlank(ruleObj.getStr("上限告警内容MaxContentDo")) ? ruleObj.getStr("上限告警内容MaxContentDo") : "";
                MaxDisalarmDescription = StrUtil.isNotBlank(ruleObj.getStr("上限告警解除内容MaxContentUnDo")) ? ruleObj.getStr("上限告警解除内容MaxContentUnDo") : "";
                MinAlarmDescription = StrUtil.isNotBlank(ruleObj.getStr("下限告警内容MinContentDo")) ? ruleObj.getStr("下限告警内容MinContentDo") : "";
                MinDisalarmDescription = StrUtil.isNotBlank(ruleObj.getStr("下限告警解除内容MinContentUnDo")) ? ruleObj.getStr("下限告警解除内容MinContentUnDo") : "";

                if (StrUtil.isBlank(State0Description) || StrUtil.isBlank(State1Description)) {
                    // 试着从orig 里面获取
                    JSONObject orig = ruleObj.getJSONObject("orig");
                    if (orig != null) {
                        State0Description = orig.getStr("状态0State0");
                        State1Description = orig.getStr("状态1State1");
                    }
                }

                if (queryForList.size() == 0) {
                    String tempParametersSql = "insert into device_parameter (" +
                            "ParameterName,ParameterCode,StateParameter,DeviceId,DataType,IsAlarmEnable,IsDisalarmEnable,Unit," +
                            "Agentbm,IsCoreParameter,IsDeleted,State0Description,State1Description,MaxAlarmDescription,MaxDisalarmDescription,MinAlarmDescription,MinDisalarmDescription,alarmLevelJson" +
                            ") values('{}','{}','{}','{}',{},{},{},'{}',{},{},{},'{}','{}','{}','{}','{}','{}','{}')";
                    String insertParamsSql = StrFormatter.format(tempParametersSql, ruleObj.getStr("paramName"), ruleObj.getStr("paramCode"), stateParameter,
                            deviceJsonObj.getStr("deviceId"), type, 0, 0, unit, deviceJsonObj.getStr("agentCode"), 0, 0, State0Description,
                            State1Description, MaxAlarmDescription, MaxDisalarmDescription, MinAlarmDescription, MinDisalarmDescription, "[]"
                    );
                    log.info(insertParamsSql);
                    jdbcTemplate.execute(insertParamsSql);

                } else {

                    String updateParamSqlTemp = "update device_parameter set ParameterName='{}',StateParameter='{}',DataType={},IsAlarmEnable={}," +
                            "IsDisalarmEnable={},Unit='{}',Agentbm='{}',State0Description='{}',State1Description='{}' where DeviceId = '{}' and ParameterCode = '{}' ";

                    String updateParamSql = StrUtil.format(updateParamSqlTemp,
                            ruleObj.getStr("paramName"), stateParameter, type, IsAlarmEnable, IsAlarmEnable, unit, deviceJsonObj.getStr("agentCode"),
                            deviceJsonObj.getStr("deviceId"), ruleObj.getStr("paramCode"), State0Description, State1Description);

                    log.info(updateParamSql);

                    jdbcTemplate.execute(updateParamSql);

                    // 如果 State0Description State1Description MaxAlarmDescription MaxDisalarmDescription
                    //   MinAlarmDescription MinDisalarmDescription
                    // 这些为空，那么给更新上去。

               /*     log.info("State0Description:{} , State1Description", State0Description, State1Description);*/

                 /*   Map<String, Object> paramater = queryForList.get(0);*/
/*

                        jdbcTemplate.execute(
                                StrUtil.format("update device_parameter set State0Description='{}',State1Description='{}' where ID = {}", State0Description,State1Description, paramater.get("ID"))
                        );
*/

                  /*
                    if (paramater.get("MaxAlarmDescription") != null && StrUtil.isBlank(paramater.get("MaxAlarmDescription").toString()) && StrUtil.isNotBlank(MaxAlarmDescription)) {

                        jdbcTemplate.execute(
                                StrUtil.format("update device_parameter set MaxAlarmDescription='{}' where ID = {}", MaxAlarmDescription, paramater.get("ID"))
                        );

                    }
                    if (paramater.get("MaxDisalarmDescription") != null && StrUtil.isBlank(paramater.get("MaxDisalarmDescription").toString()) && StrUtil.isNotBlank(MaxDisalarmDescription)) {

                        jdbcTemplate.execute(
                                StrUtil.format("update device_parameter set MaxDisalarmDescription='{}' where ID = {}", MaxDisalarmDescription, paramater.get("ID"))
                        );

                    }
                    if (paramater.get("MinAlarmDescription") != null && StrUtil.isBlank(paramater.get("MinAlarmDescription").toString()) && StrUtil.isNotBlank(MinAlarmDescription)) {

                        jdbcTemplate.execute(
                                StrUtil.format("update device_parameter set MinAlarmDescription='{}' where ID = {}", MinAlarmDescription, paramater.get("ID"))
                        );

                    }
                    if (paramater.get("MinDisalarmDescription") != null && StrUtil.isBlank(paramater.get("MinDisalarmDescription").toString()) && StrUtil.isNotBlank(MinDisalarmDescription)) {

                        jdbcTemplate.execute(
                                StrUtil.format("update device_parameter set MinDisalarmDescription='{}' where ID = {}", MinDisalarmDescription, paramater.get("ID"))
                        );

                    }*/


                }
            });

        });

        log.info("同步结束。。");
        JSONObject result = new JSONObject();
        result.set("result", resultList);
        result.set("error", errorList);
        return CommonResult.success(result);

    }

    /**
     * 同步参数
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/syncStatus")
    public CommonResult syncStatus(@RequestBody SyncInfo2 syncInfo) {
        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }
        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        JSONObject jsonObject = getParam();
        JSONObject whereObj = new JSONObject();
        whereObj.set("projectCode", syncInfo.getPrj());
        jsonObject.set("where", whereObj);

        String tempUrl = "http://{}:{}/engineering_platform/classes/";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());

        //根据project code 获取产品信息
        String result1 = HttpRequest.post(httpBaseUrl + "ProjectProduct")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray productArray = JSONUtil.parseObj(result1).getJSONArray("results");

        Map<String, JSONObject> productMap = new HashMap<>();

        // 缓存产品信息
        productArray.forEach(product -> {
            JSONObject productJsonObj = JSONUtil.parseObj(product);
            productMap.put(productJsonObj.getStr("model"), productJsonObj);

        });
        if ("option".equals(syncInfo.getAgentRadio())) {
            whereObj.set("agentCode", syncInfo.getAgentBm());
        }
        jsonObject.set("where", whereObj);

        //根据project code 获取设备信息
        String result2 = HttpRequest.post(httpBaseUrl + "ProjectDevice")
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        JSONArray deviceArray = JSONUtil.parseObj(result2).getJSONArray("results");


        log.info("共{}个设备", deviceArray.size());
        deviceArray.forEach(device -> {

            JSONObject deviceJsonObj = JSONUtil.parseObj(device);

            JSONArray rules = new JSONArray();

            try {
                if (StrUtil.isNotBlank(deviceJsonObj.getStr("rules")) && JSONUtil.parseArray(deviceJsonObj.getStr("rules")).size() != 0) {
                    rules = JSONUtil.parseArray(deviceJsonObj.getStr("rules"));
                } else {
                    rules = productMap.get(deviceJsonObj.getStr("deviceModel")).getJSONArray("rules");

                }

            } catch (NullPointerException e) {
                e.printStackTrace();
            }


            log.info("----------");

            // 插入参数


            rules.forEach(o -> {

                log.info("rules:{}", o);
                JSONObject ruleObj = JSONUtil.parseObj(o);
                String State0Description = "";
                String State1Description = "";
                String MaxAlarmDescription = "";
                String MaxDisalarmDescription = "";
                String MinAlarmDescription = "";
                String MinDisalarmDescription = "";

                State0Description = StrUtil.isNotBlank(ruleObj.getStr("State0")) ? ruleObj.getStr("State0") : "";
                State1Description = StrUtil.isNotBlank(ruleObj.getStr("State1")) ? ruleObj.getStr("State1") : "";
                MaxAlarmDescription = StrUtil.isNotBlank(ruleObj.getStr("上限告警内容MaxContentDo")) ? ruleObj.getStr("上限告警内容MaxContentDo") : "";
                MaxDisalarmDescription = StrUtil.isNotBlank(ruleObj.getStr("上限告警解除内容MaxContentUnDo")) ? ruleObj.getStr("上限告警解除内容MaxContentUnDo") : "";
                MinAlarmDescription = StrUtil.isNotBlank(ruleObj.getStr("下限告警内容MinContentDo")) ? ruleObj.getStr("下限告警内容MinContentDo") : "";
                MinDisalarmDescription = StrUtil.isNotBlank(ruleObj.getStr("下限告警解除内容MinContentUnDo")) ? ruleObj.getStr("下限告警解除内容MinContentUnDo") : "";

                if (StrUtil.isBlank(State0Description) || StrUtil.isBlank(State1Description)) {
                    // 试着从orig 里面获取
                    JSONObject orig = ruleObj.getJSONObject("orig");
                    if (orig != null) {
                        State0Description = orig.getStr("状态0State0");
                        State1Description = orig.getStr("状态1State1");
                    }
                }


                if (StrUtil.isBlank(MaxAlarmDescription)) {
                    // 试着从orig 里面获取
                    JSONObject orig = ruleObj.getJSONObject("orig");
                    if (orig != null) {
                        MaxAlarmDescription = orig.getStr("上限告警内容MaxContentDo");
                        MaxDisalarmDescription = orig.getStr("上限告警解除内容MaxContentUnDo");
                        MinAlarmDescription = orig.getStr("下限告警内容MinContentDo");
                        MinDisalarmDescription = orig.getStr("下限告警解除内容MinContentUnDo");
                    }
                }


                log.info("State0Description:{} , State1Description:{}", State0Description, State1Description);


                String updateSql = "update device_parameter set State0Description='{}',State1Description='{}',MaxAlarmDescription='{}', MaxDisalarmDescription='{}',MinAlarmDescription='{}',MinDisalarmDescription='{}'  where DeviceId = '{}' and ParameterCode='{}'";

                updateSql =StrUtil.format(updateSql, State0Description,State1Description,MaxAlarmDescription,MaxDisalarmDescription,MinAlarmDescription,MinDisalarmDescription,deviceJsonObj.getStr("deviceId"), ruleObj.getStr("paramCode"));
                log.info("UpdateSql:{}", updateSql);
                jdbcTemplate.execute(
                        updateSql
                );


            });

        });

        log.info("同步结束。。");
        JSONObject result = new JSONObject();
        result.set("result", resultList);
        result.set("error", errorList);
        return CommonResult.success(result);

    }


    /**
     * 删除设备
     *
     * @param syncInfo
     * @return
     */
    @RequestMapping("/deleteDevice")
    public CommonResult deleteDevice(@RequestBody SyncInfo2 syncInfo) {

        CommonResult commonResult = testConn(syncInfo);

        if (commonResult != null) {
            return commonResult;
        }

        List<String> resultList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        JSONObject jsonObject = getParam();


        String tempUrl = "http://{}:{}/engineering_platform/classes/ProjectDevice";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());


        String selectSql = "select * from device";

        List<Map<String, Object>> deviceList = jdbcTemplate.queryForList(selectSql);

        deviceList.forEach(map -> {

            String deviceId = map.get("DeviceId").toString();
            JSONObject whereObj = new JSONObject();
            whereObj.set("projectCode", syncInfo.getPrj());
            whereObj.set("deviceId", deviceId);
            jsonObject.set("where", whereObj);

            //根据project code 获取产品信息
            String result = HttpRequest.post(httpBaseUrl)
                    .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                    .body(JSONUtil.toJsonStr(jsonObject))
                    .timeout(20000)//超时，毫秒
                    .execute().body();
            JSONArray productArray = JSONUtil.parseObj(result).getJSONArray("results");
            if (productArray.size() == 0) {
                // 删除
                log.info("设备：{}，不存在，删除。", map.get("DeviceName"));
                String deleteSql = "delete from device where DeviceId = '" + deviceId + "';";
                jdbcTemplate.execute(deleteSql);
                resultList.add("设备：" + map.get("DeviceName") + "，不存在，已被删除");

                String deleteParams = "delete from device_parameter where DeviceId = '" + deviceId + "';";
                jdbcTemplate.execute(deleteParams);

            }

        });

        log.info("结束");
        JSONObject resultObj = new JSONObject();
        resultObj.set("result", resultList);
        resultObj.set("error", errorList);
        return CommonResult.success(resultObj);

    }


    @RequestMapping("agentSelect")
    public CommonResult agentSelect(@RequestBody SyncInfo2 syncInfo) {
        syncInfo.setAction("Project");
        CommonResult commonResult = testConn(syncInfo);
        if (commonResult != null) {
            return commonResult;
        }

        JSONObject whereObj = new JSONObject();
        whereObj.set("code", syncInfo.getPrj());
        String result = getResult(syncInfo, whereObj);
        JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("results");
        List<Map<String, String>> resultList = new ArrayList<>();
        jsonArray.forEach(obj -> {

            JSONObject x = JSONUtil.parseObj(obj);

            JSONArray gateways = x.getJSONArray("gateways");

            gateways.forEach(gateway -> {
                JSONObject gatewayObj = JSONUtil.parseObj(gateway);
                Map<String, String> map = new LinkedHashMap<>();

                map.put("code", gatewayObj.getStr("code"));
                map.put("name", gatewayObj.getStr("name"));

                resultList.add(map);

            });

        });

        return CommonResult.success(resultList);

    }

    private CommonResult testConn(SyncInfo2 syncInfo) {


        if (jdbcTemplate!=null){
            return null;
        }

        // 1. 测试mysql 连接
        DataSource dataSource = getDataSource(syncInfo);
        if (dataSource == null) {
            return CommonResult.error("获取 mysql 连接失败");
        }

        jdbcTemplate = new JdbcTemplate(dataSource);
        // 2. 测试parse 连接
        String parseTestResult = testParseConn(syncInfo.getParseUrl(), syncInfo.getParsePort(), syncInfo.getPrj());
        if (StrUtil.isBlank(parseTestResult)) {
            return CommonResult.error("获取 parse 连接失败");
        }

        JSONObject parseObj = JSONUtil.parseObj(parseTestResult);
        if (parseObj.getJSONArray("results").size() == 0) {
            return CommonResult.error("工程代码不存在！");
        }


        return null;

    }


    public DataSource getDataSource(SyncInfo2 syncInfo) {

        HikariDataSource dataSource = null;
        try {
            //1.数据库连接的4个基本要素：
            String urlTemp = "jdbc:mysql://{}:{}/{}";
            String url = StrUtil.format(urlTemp, syncInfo.getMysqlUrl(), syncInfo.getMysqlPort(), syncInfo.getMysqlDataName());
            String user = syncInfo.getMysqlUser();
            String password = syncInfo.getMysqlPassWd();
            String driverName = "com.mysql.cj.jdbc.Driver";

            HikariConfig conf = new HikariConfig();
            conf.setUsername(user);
            conf.setPassword(password);
            conf.setJdbcUrl(url);
            conf.setDriverClassName(driverName);
            conf.setMaxLifetime(18000000);
            conf.setIdleTimeout(6000000);
            dataSource = new HikariDataSource(conf);
        } catch (Exception e) {
            return null;
        }


        return dataSource;

    }

    public String testParseConn(String url, String port, String prj) {
        JSONObject jsonObject = getParam();
        JSONObject whereObj = new JSONObject();
        whereObj.set("code", prj);
        jsonObject.set("where", whereObj);
        String tempUrl = "http://{}:{}/engineering_platform/classes/Project";
        String httpBaseUrl = StrUtil.format(tempUrl, url, port);
        String result = "";
        try {
            result = HttpRequest.post(httpBaseUrl)
                    .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                    .body(JSONUtil.toJsonStr(jsonObject))
                    .timeout(20000)//超时，毫秒
                    .execute().body();

            parseLogin(url, port);

        } catch (Exception e) {
            System.out.println();
        }

        return result;

    }

    public String parseLogin(String url, String port) {
        String loginUrl = "http://{}:{}/engineering_platform/login";
        JSONObject parmaMap = new JSONObject();
        parmaMap.set("username", "luxs");
        parmaMap.set("password", "LKsoft@123");
        String body = HttpRequest.post(StrUtil.format(loginUrl, url, port)).header("X-Parse-Application-Id", "com.smartxdc.engineering_platform")
                .header("X-Parse-REST-API-Key", "LKSoft@123Rest")
                .header("X-Parse-Revocable-Session", "1")
                .header("Content-Type", "application/json").body(JSONUtil.toJsonStr(parmaMap))
                .execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String sessionToken = jsonObject.getStr("sessionToken");
        token = sessionToken;
        return token;

    }

    public JSONObject getParam() {
        JSONObject map = new JSONObject();
        map.set("_ApplicationId", "com.smartxdc.engineering_platform");
        map.set("limit", 900000);
        map.set("_ClientVersion", "js3.4.1");
        map.set("_InstallationId", IdUtil.fastUUID());
        map.set("_JavaScriptKey", "LKSoft@123");
        map.set("_SessionToken", token);
        map.set("_method", "GET");

        return map;
    }

    public String getResult(SyncInfo2 syncInfo, JSONObject whereObj) {
        String tempUrl = "http://{}:{}/engineering_platform/classes/";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());
        JSONObject jsonObject = getParam();

        if (whereObj != null) {
            jsonObject.set("where", whereObj);
        }

        //链式构建请求
        String result = HttpRequest.post(httpBaseUrl + syncInfo.getAction())
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        return result;
    }

    public String getResultByWhere(SyncInfo2 syncInfo, String tableName, JSONObject whereObj) {
        String tempUrl = "http://{}:{}/engineering_platform/classes/";
        String httpBaseUrl = StrUtil.format(tempUrl, syncInfo.getParseUrl(), syncInfo.getParsePort());
        JSONObject jsonObject = getParam();
        jsonObject.set("where", whereObj);

        //链式构建请求
        String result = HttpRequest.post(httpBaseUrl + tableName)
                .header(Header.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .body(JSONUtil.toJsonStr(jsonObject))
                .timeout(20000)//超时，毫秒
                .execute().body();

        return result;
    }


    public void truncate(SyncInfo2 syncInfo2, String tableName) {

        if (syncInfo2.getAgentBm() != null) {

        }

        if (syncInfo2.getRemove() == 1) {
            String truncSql = "TRUNCATE `" + tableName + "`";
            jdbcTemplate.execute(truncSql);

        }

    }
}
