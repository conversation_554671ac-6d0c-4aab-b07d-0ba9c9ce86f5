/**
 * @<NAME_EMAIL>
 * @description vue.config.js全局配置
 */
const path = require('path')
const dayjs = require('dayjs')
const date = dayjs().format('YYYY_M_D')
const time = dayjs().format('YYYY-M-D HH:mm:ss')
const { version, author } = require('./package.json')
const {
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  title,
  abbreviation,
  devPort,
  providePlugin,
  build7z,
  donation,
} = require('./src/config')

// 定义 webpack 相关配置
const webpackBarName = 'SmartXDC工程配置工具'

// 捐赠控制台输出
const donationConsole = () => {
  if (donation) {
    console.log(
      `%c感谢您使用 ${title} %c https://github.com/chuzhixin/vue-admin-beautiful`,
      'color: #fff; background: #409eff; padding: 2px 5px; border-radius: 3px;',
      'color: #fff; background: #67c23a; padding: 2px 5px; border-radius: 3px;'
    )
  }
}

if (donation) donationConsole()
const Webpack = require('webpack')
const WebpackBar = require('webpackbar')
const FileManagerPlugin = require('filemanager-webpack-plugin')
process.env.VUE_APP_TITLE = title || 'SmartXDC工程配置工具'
process.env.VUE_APP_AUTHOR = author || 'chuzhixin'
process.env.VUE_APP_UPDATE_TIME = time
process.env.VUE_APP_VERSION = version

const resolve = (dir) => {
  return path.join(__dirname, dir)
}


module.exports = {
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  transpileDependencies: ['net-snmp'],
  devServer: {
    disableHostCheck: true,
    hot: true,
    port: devPort,
    open: true,
    noInfo: false,
    overlay: {
      warnings: true,
      errors: true,
    },
    proxy: {
      '/api/host': {
        target: 'http://localhost:8088',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api/host': '',
        },
        logLevel: 'debug',
        onProxyRes: function(proxyRes, req, res) {
          console.log('Response from backend:', proxyRes.statusCode);
        },
        onError: function(err, req, res) {
          console.log('Proxy error:', err);
        }
      },
      '/api': {
        target: 'http://127.0.0.1:3000',
        // target: 'http://*************:3000',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',
        },
        logLevel: 'debug',
        onProxyRes: function(proxyRes, req, res) {
          console.log('Response from backend:', proxyRes.statusCode);
        },
        onError: function(err, req, res) {
          console.log('Proxy error:', err);
        }
      }
    },
  },
  configureWebpack(config) {
    return {
      resolve: {
        alias: {
          '@': resolve('src'),
          '*': resolve(''),
        },
      },
      plugins: [
        new WebpackBar({
          name: webpackBarName,
          color: '#85d',
        }),
        new Webpack.ProvidePlugin(providePlugin),
      ],
    }
  },
  chainWebpack(config) {
    config.resolve.symlinks(true)
    config.module.rule('svg').exclude.add(resolve('src/icon/remixIcon')).end()

    config.module
      .rule('remixIcon')
      .test(/\.svg$/)
      .include.add(resolve('src/icon/remixIcon'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'remix-icon-[name]',
      })
      .end()

    config.when(process.env.NODE_ENV === 'development', (config) => {
      config.devtool('source-map')
    })

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config.performance.set('hints', false)
      config.devtool('none')
      config.optimization
        .splitChunks({
          chunks: 'all',
          cacheGroups: {
            libs: {
              name: 'vue-admin-beautiful-libs',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial',
            },
          },
        })
        //   config
        //     .plugin('banner')
        //     .use(Webpack.BannerPlugin, [`${webpackBanner}${time}`])
        //     .end()
        //   config.module
        //     .rule('images')
        //     .use('image-webpack-loader')
        //     .loader('image-webpack-loader')
        //     .options({
        //       bypassOnDebug: true,
        //     })
        .end()
    })

    if (build7z) {
      config.when(process.env.NODE_ENV === 'production', (config) => {
        config
          .plugin('fileManager')
          .use(FileManagerPlugin, [
            {
              onEnd: {
                delete: [`./${outputDir}/video`, `./${outputDir}/data`],
                archive: [
                  {
                    source: `./${outputDir}`,
                    destination: `./${outputDir}/${abbreviation}_${outputDir}_${date}.7z`,
                  },
                ],
              },
            },
          ])
          .end()
      })
    }
  },
  runtimeCompiler: true,
  productionSourceMap: false,
  css: {
    requireModuleExtension: true,
    sourceMap: true,
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
          modifyVars: {
            'vab-color-blue': '#1890ff',
            'vab-margin': '20px',
            'vab-padding': '20px',
            'vab-header-height': '65px',
          },
        },
      },
    },
  },
}
