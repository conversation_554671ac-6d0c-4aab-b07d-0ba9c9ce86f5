<div align="center"><img width="200" src="https://gitee.com/chu1204505056/vue-admin-beautiful/raw/master/src/colorfulIcon/svg/vab.svg"/>
<h1> vue-admin-beautiful（ant-design-vue） </h1>
</div>

[![Website](<https://img.shields.io/badge/ good luck - vue admin beautiful -blue?style=flat-square>)](https://vue-admin-beautiful.com)
[![stars](https://img.shields.io/github/stars/chuzhixin/vue-admin-beautiful?style=flat-square&logo=GitHub)](https://github.com/chuzhixin/vue-admin-beautiful)
[![star](https://gitee.com/chu1204505056/vue-admin-beautiful/badge/star.svg?theme=gray)](https://gitee.com/chu1204505056/vue-admin-beautiful)
[![license](https://img.shields.io/github/license/chuzhixin/vue-admin-beautiful?style=flat-square)](https://en.wikipedia.org/wiki/MIT_License)

## 地址

- [🎉 vue2.x + element-ui（免费商用，支持 PC、平板、手机）](http://vue-admin-beautiful.com/vue-admin-beautiful-element/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [⚡️ vue3.x + element-plus（alpha 版本，免费商用，支持 PC、平板、手机）](http://vue-admin-beautiful.com/vue-admin-beautiful-element-plus/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [⚡️ vue3.x + ant-design-vue（beta 版本，免费商用，支持 PC、平板、手机）](http://vue-admin-beautiful.com/vue-admin-beautiful-antdv/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [🚀 admin pro 演示地址（付费版本，支持 PC、平板、手机）](http://vue-admin-beautiful.com/admin-pro/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [🚀 admin plus 演示地址（vue3.x 付费版本，支持 PC、平板、手机）](http://vue-admin-beautiful.com/admin-plus/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [📌 pro 及 plus 购买地址 authorization](http://vue-admin-beautiful.com/authorization/)

- [🌐 github 仓库地址](https://github.com/chuzhixin/vue-admin-beautiful?utm_source=gold_browser_extension)

- [🌐 码云仓库地址](https://gitee.com/chu1204505056/vue-admin-beautiful?_from=gitee_search)

- 近期 vab 官网频繁遭到 ddos 攻击，我们已采取相关防范措施，如网站无法访问请访问备份地址

## 备份地址

- [🚀 admin pro 演示地址（付费版本，支持 PC、平板、手机）](https://chu1204505056.gitee.io/admin-pro/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

- [🚀 admin plus 演示地址（vue3.x 付费版本，支持 PC、平板、手机）](https://chu1204505056.gitee.io/admin-plus/?hmsr=github&hmpl=&hmcu=&hmkw=&hmci=)

## vue-admin-beautiful 前端讨论 QQ 群

- 请我们喝杯咖啡，支付后联系 QQ 783963206 邀请您进入讨论群（由于用户数较多，如果您打赏后未通过好友请求，请在支付宝支付页面选择联系商家），不管您请还是不请，您都可以享受到开源的代码，感谢您的支持和信任，群内提供 vue-admin-beautifu 基础版本、开发工具自动配置教程及项目开发文档。

<table>
<tr>
<td>
<img width="200px" src="https://gitee.com/chu1204505056/image/raw/master/zfb_kf.jpg">
</td>
<td>
<img width="200px" src="https://gitee.com/chu1204505056/image/raw/master/qq_group/vab-2.jpg">
</td>
<td>
<img width="200px" src="https://gitee.com/chu1204505056/image/raw/master/qq_group/vab-3.jpg">
</td>
</tr>
</table>

## 🌱 vue3.x vue3.0-antdv 分支（ant-design-vue）[点击切换分支](https://github.com/chuzhixin/vue-admin-beautiful-pro/tree/vue3.0-antdv)

```bash
# 克隆项目
git clone -b vue3.0-antdv https://github.com/chuzhixin/vue-admin-beautiful.git
# 进入项目目录
cd vue-admin-beautiful-pro
# 安装依赖
npm i
# 本地开发 启动项目
npm run serve
```

## 🌱vue2.x master 分支（element-ui）[点击切换分支](https://github.com/chuzhixin/vue-admin-beautiful-pro/tree/master)

```bash
# 克隆项目
git clone -b master https://github.com/chuzhixin/vue-admin-beautiful.git
# 进入项目目录
cd vue-admin-beautiful-pro
# 安装依赖
npm i
# 本地开发 启动项目
npm run serve
```

## 友情链接

- [uView 文档（超棒的移动跨端框架，文档详细，上手容易）](https://uviewui.com/)

- [uView 开源地址（uni-app 生态优秀的 UI 框架，全面的组件和便捷的工具会让您信手拈来，如鱼得水）](https://github.com/YanxinNet/uView/)

- [Element UI 表单设计及代码生成器（可视化表单设计器，一键生成 element 表单）](https://github.com/JakHuang/form-generator/)

- [luch-request（基于 Promise 开发的 uni-app 跨平台、项目级别的请求库）](https://www.quanzhan.co/luch-request/)

## 我们承诺将定期赞助的开源项目（感谢巨人）

<a title="vue" href="https://cn.vuejs.org/" target="_blank">
<img width="64px" src="https://gitee.com/chu1204505056/image/raw/master/vue.png"/>
</a>

<a title="ant-design-vue" href="https://github.com/vueComponent/ant-design-vue#backers" target="_blank">
<img width="64px" src="https://gitee.com/chu1204505056/image/raw/master/antdv.svg"/>
</a>

<a title="element-plus" href="https://opencollective.com/element-plus" target="_blank">
<img width="64px" src="https://gitee.com/chu1204505056/image/raw/master/element-plus.png"/>
</a>

## 鸣谢

| Project                                                          |
| ---------------------------------------------------------------- |
| [vue](https://github.com/vuejs/vue)                              |
| [element-ui](https://github.com/ElemeFE/element)                 |
| [element-plus](https://github.com/element-plus/element-plus)     |
| [ant-design-vue](https://github.com/vueComponent/ant-design-vue) |
| [mock](https://github.com/nuysoft/Mock)                          |
| [axios](https://github.com/axios/axios)                          |

## 框架杰出贡献者（排名不分先后）

<a href="https://github.com/buuing" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/36689704?s=50"/>
</a>
<a href="https://github.com/hipi" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/22478003?s=50"/>
</a>
<a href="https://github.com/fwfmiao" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/29328241?s=50"/>
</a>
<a href="https://github.com/hdtopku" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/14859466?s=50"/>
</a>
<a href="https://github.com/shaonialife" target="_blank">
<img width="50px" style="border-radius:999px" src="https://avatars.githubusercontent.com/u/16135960?s=50"/>
</a>

## 优势及注意事项

```
vue-admin-beautiful-pro 对比其他开源 admin 框架有如下优势:
1. 支持前端控制路由权限 intelligence、后端控制路由权限 all 模式
2. 已知开源 vue admin 框架中首家支持 mock 自动生成自动导出功能
3. 提供 50 余项全局精细化配置
4. 支持 scss 自动排序，eslint 自动修复
5. axios 精细化封装，支持多数据源、多成功 code 数组，支持 application/json;charset=UTF-8、application/x-www-form-urlencoded;charset=UTF-8 多种传参方式
6. 支持登录RSA加密
7. 支持打包自动生成7Z压缩包
8. 支持errorlog错误拦截
9. 支持多主题、多布局切换

vue-admin-beautiful-pro 使用注意事项:
1. 项目默认使用lf换行符而非crlf换行符，新建文件时请注意选择文件换行符
2. 项目默认使用的最严格的eslint校验规范（plugin:vue/recommended），使用之前建议配置开发工具实现自动修复（建议使用vscode开发）
3. 项目使用的是要求最宽泛的MIT开源协议，保留MIT开源协议即可免费商用

```

## 适合人群

- 正在以及想使用 element-ui/element-plus 开发，前端开发经验 1 年+。
- 熟悉 Vue.js 技术栈，使用它开发过几个实际项目。
- 对原理技术感兴趣，想进阶和提升的同学。

## 功能地图：

![img](https://gitee.com/chu1204505056/image/raw/master/vip/flow.drawio.png)

## 特性：

- 支持 PC、手机端、平板；
- 提供超过 50 余项全局精细化配置；
- 支持后端渲染动态路由
- 拥有完整的登录鉴权和前后端多种配置的动态路由流程
- 支持前端控制路由权限 intelligence、后端控制路由权限 all 模式
- 支持 mock 自动生成自动导出功能
- 支持 scss 自动排序，eslint 自动修复
- 支持登录 RSA 加密
- 支持打包自动生成 7Z 压缩包以及自动化部署
- 支持 errorlog 错误拦截
- 支持多主题、多布局切换

## 效果图

以下是截取的是 pro 版的效果图展示：

<table>
<tr>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/2.png">
</td>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/6.png">
</td>
</tr>
<tr>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/8.png">
</td>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/9.png">
</td>
</tr>
<tr>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/3.png">
</td>
<td>
<img src="https://gitee.com/chu1204505056/image/raw/master/5.png">
</td>
</tr>
</table>

## 商用注意事项

此项目可免费用于商业用途，请遵守 MIT 协议并保留作者技术支持声明，如需自定义版权信息请联系客服 QQ 783963206。

<!-- ## 严正声明

近期发现不少游手好闲之人有组织有预谋的利用码云、知乎、掘金等网站可用国外非法网站提供的匿名手机号注册的账号 bug 冒充 vab 去攻击 vue-element-admin，iview-admin，若依，d2-admin，ant-design-vue 的行为，恶意制造对立，试图让其他开源作者卷入其中，对各位开源作者造成的影响我们深表歉意，我们欢迎 vab 的用户去体验其他更优秀的框架，vue-admin-beautiful 走到今天实属不易，被人冒充，被人发帖诋毁，被人故意发布错误言论假装发帖表扬实则为我们招骂，无意动任何人的奶酪，从 2020 年至今坚持全职维护已过一年时间，说实在的我们靠技术生存并不丢人吧，一年来感谢 vab 的用户对我们不离不弃，也希望大家越来越好，加油！ -->
