const { app, BrowserWindow } = require('electron')
const { createProxyMiddleware } = require('http-proxy-middleware');
const express = require('express');
const http = require('http');

// 保持对window对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭

// 配置代理
const proxy = createProxyMiddleware({
  target: 'http://beta.smartxdc.com:22289', // 目标服务器地址
  changeOrigin: true, // 允许跨域
  pathRewrite: {
    '^/api': '' // 将 /api 路径重写为空
  }
});

let win

function createWindow() {
  // 创建浏览器窗口。
  win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
  })

  // 加载index.html文件
  win.loadFile('index.html')

  // 打开开发者工具
  // win.webContents.openDevTools()

  // 当 window 被关闭，这个事件会被触发。
  win.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    win = null
  })
}

// Electron 会在初始化后并准备
// 创建浏览器窗口时，调用这个函数。
// 部分 API 在 ready 事件触发后才能使用。
app.on('ready', createWindow)

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (win === null) {
    createWindow()
  }
})

// 在 Electron 的主进程中设置代理
app.on('ready', () => {
  const app = express();

  // 设置代理
  app.use('/api', proxy);

  // 启动代理服务器
  const server = http.createServer(app);
  server.listen(48081, () => {
    console.log('Proxy server is running on port 48081');
  });
});