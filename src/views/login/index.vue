<template>
  <div class="login-container">
    <a-row>
      <a-col :xs="0" :md="0" :sm="12" :lg="14" :xl="16"></a-col>
      <a-col :xs="24" :sm="24" :md="12" :lg="10" :xl="6">
        <div class="login-container-form">
          <div class="login-container-hello">您好!</div>
          <div class="login-container-title">欢迎使用 {{ title }}</div>
          <!-- <div>{{comeBack}}</div> -->
          <a-form :model="form" @submit.prevent>
            <a-form-item>
              <a-input v-model:value="form.username" placeholder="Username">
                <template v-slot:prefix>
                  <UserOutlined style="color: rgba(0, 0, 0, 0.25)" />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input
                v-model:value="form.password"
                type="password"
                placeholder="Password"
              >
                <template v-slot:prefix>
                  <LockOutlined style="color: rgba(0, 0, 0, 0.25)" />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                ref="onlineLoginBtn"
                @click="handleSubmit(true)"
                :disabled="form.username === '' || form.password === ''"
              >
                线上登录
              </a-button>
            </a-form-item>
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                ref="offlineLoginBtn"
                @click="handleSubmit(false)"
              >
                离线模式
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </a-col>
    </a-row>
    <div class="login-container-tips">
      版权归于 龙坤（无锡）智慧科技有限公司
    </div>
  </div>
</template>
<script>
  import { dependencies, devDependencies } from '../../../package.json'
  import { mapActions, mapGetters } from 'vuex'
  import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'

  export default {
    name: 'Login',
    components: {
      UserOutlined,
      LockOutlined,
    },
    data() {
      return {
        form: {
          username: '',
          password: '',
        },
        comeBack: '',
        redirect: undefined,
        dependencies: dependencies,
        devDependencies: devDependencies,
      }
    },
    computed: {
      ...mapGetters({
        logo: 'settings/logo',
        title: 'settings/title',
      }),
    },
    watch: {
      $route: {
        handler(route) {
          this.redirect = (route.query && route.query.redirect) || '/'
        },
        immediate: true,
      },
    },
    mounted() {
      this.form.username = ''
      this.form.password = ''
    },
    methods: {
      ...mapActions({
        login: 'user/login',
      }),
      handleRoute() {
        return this.redirect === '/404' || this.redirect === '/403'
          ? '/'
          : this.redirect
      },
      async handleSubmit(online) {
        let offline = !online
        if (offline) {
          console.log('离线模式')
          this.comeBack = await this.login(this.form)
          await this.$router.push('/index')
        } else {
          console.log('在线模式')
          await this.login(this.form)
          await this.$router.push('/')
        }
      },
      async handleLogin() {
        try {
          const res = await login(loginForm)
          if (res.token) {
            await this.$store.commit('user/setAccessToken', res.token)
            this.$router.push('/')
          }
        } catch (error) {
          console.error('登录失败:', error)
        }
      }
    },
  }
</script>
<style lang="less">
  .login-container {
    width: 100%;
    height: 100vh;
    background: url('../../assets/login_images/login_background.png');
    background-size: cover;
    &-form {
      width: calc(100% - 40px);
      height: 450px;
      padding: 4vh;
      margin-top: calc((100vh - 380px) / 2);
      margin-right: 20px;
      margin-left: 20px;
      background: url('../../assets/login_images/login_form.png');
      background-size: 100% 100%;
      border-radius: 10px;
      box-shadow: 0 2px 8px 0 rgba(7, 17, 27, 0.06);
    }
    &-hello {
      font-size: 32px;
      color: #fff;
    }
    &-title {
      margin-bottom: 30px;
      font-size: 20px;
      color: #fff;
    }
    &-tips {
      position: fixed;
      bottom: @vab-margin;
      width: 100%;
      height: 40px;
      color: rgba(255, 255, 255, 0.856);
      text-align: center;
    }
    .ant-col {
      width: 100%;
      padding: 0 10px 0 10px;
    }
    .ant-input {
      height: 35px;
    }
    .ant-btn {
      width: 100%;
      height: 45px;
      border-radius: 99px;
    }
  }
</style>
