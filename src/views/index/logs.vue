<template>
    <div>
        <div>
            用户名称
            <a-input v-model:value="value" style="width: 200px" allowClear />
            <a-button type="primary" style="margin-left: 10px" @click="getListByName">
                查询
            </a-button>
        </div>
        <div style="margin-top: 20px">
            <a-table :columns="columns" :data-source="dataSource" bordered :pagination="false"
                :scroll="{ y: scrollHight }" :size="'small'" rowKey="id">
                <template #bodyCell="{ column, text, record, index }">
                    <template v-if="column.dataIndex === 'index'">
                        {{ index + 1 }}
                    </template>
                    <template v-else>
                        {{ text }}
                    </template>
                </template>
            </a-table>
        </div>
        <div class="pagination">
            <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange"
                @pageChange="pageChange" />
        </div>
    </div>
</template>
<script>
import Pagination from '../../components/pagination'
import { getLogs } from '@/api/logs'
import {
    defineComponent,
    ref,
    onMounted,
} from 'vue'
const columnsDefault = [
    {
        title: '序号',
        dataIndex: 'index',
        width: '100px',
    },
    {
        title: '用户名称',
        dataIndex: 'username',
    },
    {
        title: '类目',
        dataIndex: 'tableName',
    },
    {
        title: '操作',
        dataIndex: 'action',
    },
    {
        title: '操作时间',
        dataIndex: 'createdAt',
    },
]
export default defineComponent({
    name: 'logs',
    components: { Pagination },
    setup() {
        const page = ref(1)
        const size = ref(10)
        const total = ref(0)
        const pageChange = (Page) => {
            page.value = Page
            startGetList()
        }
        const pageSizeChange = (Page, pageSize) => {
            page.value = Page
            size.value = pageSize
        }
        const dataSource = ref()
        const value = ref('')
        const scrollHight = ref(0)
        onMounted(() => {
            let o = document.getElementById('vab-content')
            let h = o.clientHeight || o.offsetHeight
            scrollHight.value = h - 250
            startGetList()
        })
        const columns = ref(columnsDefault)
        const getListByName = () => {
            page.value = 1
            startGetList()
        }
        const startGetList = () => {
            let username = ''
            if (value.value) {
                username = value.value
            }
            getLogs({
                page: page.value,
                limit: size.value,
                username,
            }).then((res) => {
                let { data, pagination } = res
                total.value = pagination.total
                dataSource.value = data
            })
        }
        return {
            startGetList,
            page,
            size,
            total,
            pageChange,
            pageSizeChange,
            value,
            getListByName,
            scrollHight,
            columns,
            dataSource,
        }
    },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
    margin-right: 8px;
}

.modalBtn {
    text-align: right;
    margin-top: 30px;

    .ant-btn+.ant-btn {
        margin-left: 10px;
    }
}
</style>