<template>
  <div>
    <statistics style="margin-top: 50px"></statistics>
  </div>
</template>

<script>
import statistics from '../statistics/index.vue'

var XLSX = require('xlsx')

export default {
  components: {
    statistics,
  },
  data() {
    return {
      workbook: [],
      systemTypeMap: {},
      deviceTypeMap: {},
      brandMap: {},
      productMap: {},
      systemTypeSheet: undefined,
      deviceTypeSheet: undefined,
      brandSheet: undefined,
      modelSheet: undefined,
    }
  },
  mounted() {},
  methods: {
    CCCCC() {
      editComSetting().then(async (res) => {
        for (const iterator of res.results) {
          let { id, attributes } = iterator
          let comSetting = attributes.comSetting
          if (comSetting.indexOf('/dev/ttyWCH') != -1) {
            let ll = comSetting.split(',')
            let ss = ll[0].replace('/dev/ttyWCH', '')
            let portNo = parseInt(ss) - 1
            ll[0] = '/dev/ttyWCH' + portNo
            comSetting = ll.join(',')
            await saveComSetting(id, comSetting)
          }
        }
      })
    },
    import_data() {
      let input = document.createElement('input')
      input.type = 'file'
      input.click()
      input.onchange = ($event) => {
        this.parse_imported_data($event)
      }
    },
    parse_imported_data(e) {
      let that = this

      // 读取表格文件
      let fileName = ''
      const files = e.target.files
      if (files.length <= 0) {
        return false
      } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
        console.log('上传格式不正确，请上传xls或者xlsx格式')
        return false
      } else {
        // 更新获取文件名
        fileName = files[0].name
      }

      console.log('fileName', fileName)

      const fileReader = new FileReader()
      fileReader.onload = async (ev) => {
        try {
          that.workbook = XLSX.read(ev.target.result, {
            type: 'binary',
          })
          that.systemTypeSheet = XLSX.utils.sheet_to_json(
            that.workbook.Sheets['系统类型'],
            {}
          ) //生成json表格内容
          await that.process_systemTypeSheet()

          that.deviceTypeSheet = XLSX.utils.sheet_to_json(
            that.workbook.Sheets['设备类型'],
            {}
          ) //生成json表格内容
          await that.process_deviceTypeSheet()

          that.brandSheet = XLSX.utils.sheet_to_json(
            that.workbook.Sheets['品牌'],
            {}
          ) //生成json表格内容
          await that.process_brandSheet()

          that.modelSheet = XLSX.utils.sheet_to_json(
            that.workbook.Sheets['型号'],
            {}
          ) //生成json表格内容
          await that.process_modelSheet()

          await that.process_productSheet(that.workbook)
        } catch (error) {
          console.log(error)
        }
      }
      fileReader.readAsBinaryString(files[0])
    },
    // 处理系统类型数据
    async process_systemTypeSheet() {
      console.log('导入 系统定义')
      for (let i = 0; i < this.systemTypeSheet.length; i++) {
        this.systemTypeMap[this.systemTypeSheet[i]['系统类型ID']] =
          this.systemTypeSheet[i]['系统类型名称']
        const isExisted = await isSystemExisted(
          this.systemTypeSheet[i]['系统类型ID']
        )
        if (isExisted) {
          // 跳过
        } else {
          // 不存在
          createSystem({
            code: this.systemTypeSheet[i]['系统类型ID'],
            name: this.systemTypeSheet[i]['系统类型名称'],
            parent: this.systemTypeSheet[i]['父类型编号'],
          })
        }
      }
    },
    // 处理设备类型数据
    async process_deviceTypeSheet() {
      console.log('导入 设备类型定义')
      for (let i = 0; i < this.deviceTypeSheet.length; i++) {
        const isExisted = await isDeviceTypeExisted(
          this.deviceTypeSheet[i]['设备类型编号']
        )
        let k = this.deviceTypeSheet[i]['设备类型编号'].split('-')[2]
        if (k.startsWith('0')) {
          k = k.replace('0', '').replace('0', '')
        }
        this.deviceTypeMap[k] = this.deviceTypeSheet[i]['设备类型名称']
        if (isExisted) {
          // 跳过
        } else {
          // 不存在
          let system =
            this.systemTypeMap[this.deviceTypeSheet[i]['所属系统类型ID']]
          await createDeviceType({
            code: this.deviceTypeSheet[i]['设备类型编号'],
            name: this.deviceTypeSheet[i]['设备类型名称'],
            system: system,
            parent: this.deviceTypeSheet[i]['父类型编号'],
            typePic: '',
            statPics: {},
          })
        }
      }
      // console.log('this.deviceTypeMap', this.deviceTypeMap)
    },
    // 处理品牌数据
    async process_brandSheet() {
      console.log('导入 品牌定义')
      for (let i = 0; i < this.brandSheet.length; i++) {
        console.log(
          this.brandSheet[i]['名称'],
          this.brandSheet[i]['ID'],
          this.brandSheet[i]['备注'].split('\\')
        )
        this.brandMap['code_' + this.brandSheet[i]['ID']] = {
          name: this.brandSheet[i]['名称'],
          code: this.brandSheet[i]['ID'] + '',
          deviceType: this.brandSheet[i]['备注'].split('\\'),
        }
        const isExisted = await isBrandExisted(this.brandSheet[i]['ID'] + '')
        if (isExisted) {
          // 跳过
          // console.log('跳过', this.brandSheet[i])
        } else {
          // 不存在
          console.log('创建', this.brandSheet[i])
          let brand = {
            name: this.brandSheet[i]['名称'],
            code: this.brandSheet[i]['ID'] + '',
            deviceType: this.brandSheet[i]['备注'].split('\\'),
          }
          await createBrand(brand)
        }
      }
    },
    // 处理型号数据
    async process_modelSheet() {
      console.log('导入 型号数据')
      return
      for (let i = 0; i < this.modelSheet.length; i++) {
        const isExisted = await isModelExisted(this.modelSheet[i]['ID'] + '')
        if (isExisted) {
          // 跳过
        } else {
          // 不存在
          console.log('创建', this.modelSheet[i])
          let k = this.modelSheet[i]['型号'].split('_')[0].trim()
          let deviceType = this.deviceTypeMap[k]
          createModel({
            name: this.modelSheet[i]['型号'],
            code: this.modelSheet[i]['ID'] + '',
            deviceType: deviceType,
            brand: this.modelSheet[i]['品牌描述'],
            memo: '',
          })
        }
      }
    },

    async process_productSheet(workbook) {
      this.productMap = {}

      let deviceTypeKeys = Object.keys(this.deviceTypeMap)
      for (let i = 0; i < deviceTypeKeys.length; i++) {
        let sheetName = this.deviceTypeMap[deviceTypeKeys[i]]
        console.log('处理', sheetName)
        let ws = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {})
        if (ws) {
        } else {
          console.log(sheetName, '不存在！！！！！！')
        }

        for (let j = 0; j < ws.length; j++) {
          let w = ws[j]
          let productKey = w['品牌ID'] + '_' + w['型号ID']
          let product = this.productMap[productKey]
          if (product === undefined) {
            let systemKey =
              '00000000-' +
              w['设备类型ID'].split('-')[1] +
              '-0000-0000-000000000000'
            console.log(systemKey)
            product = { rules: [] }
            // product.deviceTypeCode = w['设备类型ID']
            product.deviceType = w['设备类型名称']
            // product.brandCode = w['品牌ID']
            product.brand = this.brandMap['code_' + w['品牌ID']].name
            // product.modelCode = w['型号ID']
            product.system = this.systemTypeMap[systemKey]
            product.model = w['型号名称']
            product.name = product.brand + ' ' + product.model
            product.protocolType = ''
            product.commSetting = {}
            this.productMap[productKey] = product
          }

          let devvou = {
            paramCode: w['参数ID\r\nID'],
            paramName: w['参数名称\r\nch'],
            paramDataType: w['参数类型\r\nDataChar'] || null,
            needRec: w['是否启用记录历史\r\nRecHis'],
            needRpt: w['是否启用记录报表\r\nReport'],
            workState: w['是否是工作状态\r\nWrkState'] ? 0 : 1,
            tx: w['发送串\r\nTx'] || null,
            gatherInterval: w['采集间隔(毫秒)\r\nRxTime']
              ? parseInt(w['采集间隔(毫秒)\r\nRxTime'])
              : 500,
            defValue: w['默认值\r\nDefValue']
              ? parseInt(w['默认值\r\nDefValue'])
              : null,
            refParamCode: w['引用属性的id\r\nMasterId'] || null,
            dataUnit: w['单位\r\nUnit'],
            dataStartPos: w['数据截取的起始位置\r\nBGPos']
              ? parseInt(w['数据截取的起始位置\r\nBGPos'])
              : null,
            dataValidLen: w['数据截取的长度\r\nLen']
              ? parseInt(w['数据截取的长度\r\nLen'])
              : null,
            dataCalib: w['计算公式\r\nCalib'],
            dataCrcType: w['采集值的校验类型\r\nCrcType'],
            alarmEnable: w['是否产生报警事件\r\nEventAlarm'],
            alarmDebounce: w['报警忽略次数\r\nNoEvent']
              ? parseInt(w['报警忽略次数\r\nNoEvent'])
              : 1,
            alarmThrottle: w['报警最大确认次数\r\nEmaxTimes']
              ? parseInt(w['报警最大确认次数\r\nEmaxTimes'])
              : 1,
            alarmRule: [],
          }
          product.rules.push(devvou)
          // console.log(product)
        }
      }
      let keys = Object.keys(this.productMap)
      console.log('keys', keys)
      for (let k = 0; k < keys.length; k++) {
        let p = this.productMap[keys[k]]
        let existed = await isProductExisted(p.name)
        if (!existed) {
          console.log('创建product', p.name)
          createProduct(p)
        }
      }
    },
  },
}
</script>
