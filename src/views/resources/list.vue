<template>
  <div>
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <div>
        资源名称
        <a-input v-model:value="resourceName" style="width: 200px" allowClear />
        <a-button type="primary" style="margin-left: 10px" @click="getListByName">查询</a-button>
      </div>
      <div>
        <a-button type="primary" @click="showUploadModal">上传资源</a-button>
      </div>
    </div>
    <div style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        bordered
        :pagination="false"
        :scroll="{ y: scrollHight }"
        :size="'small'"
        rowKey="id"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a @click="handleDownload(record)">下载</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除这个资源吗?"
              ok-text="是"
              cancel-text="否"
              @confirm="handleDelete(record)"
            >
              <a>删除</a>
            </a-popconfirm>
          </template>
          <template v-else>{{ text }}</template>
        </template>
      </a-table>
    </div>
    <div class="pagination">
      <Pagination
        :page="page"
        :size="size"
        :total="total"
        @pageSizeChange="pageSizeChange"
        @pageChange="pageChange"
      />
    </div>

    <!-- 上传资源对话框 -->
    <!-- eslint-disable vue/valid-v-model -->
    <a-modal
      v-model:visible="uploadModalVisible"
      title="上传资源"
      @ok="addResourcesMth"
      @cancel="cancelUpload"
      :confirmLoading="confirmLoading"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="资源名称" required>
          <a-input v-model:value="uploadForm.resourceName" placeholder="请输入资源名称" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model:value="uploadForm.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
        <a-form-item label="资源文件">
          <!-- eslint-disable vue/valid-v-model -->
          <a-upload-dragger
            v-model:fileList="fileList"
            :before-upload="beforeUpload"
            :multiple="false"
          >
            <p class="ant-upload-drag-icon">
              <Cloud-upload-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">仅支持单个上传，严禁上传公司内部文件或其他违禁文件</p>
          </a-upload-dragger>
          <a-button type="primary" @click="handleUpload">上传</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script>
/* eslint-disable vue/valid-v-model */
import Pagination from '../../components/pagination'
import {
  getResources,
  deleteResource,
  downloadResource,
  addResources,
} from '@/api/resources'
import { uploadArchive } from '@/api/upload'
import { defineComponent, ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

import { CloudUploadOutlined } from '@ant-design/icons-vue'
const columnsDefault = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '60px',
  },
  {
    title: '资源名称',
    dataIndex: 'resourceName',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '上传日期',
    dataIndex: 'createdAt',
  },
  {
    title: '上传人',
    dataIndex: 'uploader',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '150px',
    scopedSlots: { customRender: 'operation' },
  },
]
export default defineComponent({
  name: 'resources',
  components: { Pagination, CloudUploadOutlined },
  setup() {
    const page = ref(1)
    const size = ref(10)
    const total = ref(0)
    const pageChange = (Page) => {
      page.value = Page
      startGetList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
    }
    const dataSource = ref()
    const resourceName = ref('')
    const scrollHight = ref(0)
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 250
      startGetList()
    })
    const columns = ref(columnsDefault)
    const startGetList = () => {
      let where = null
      if (resourceName.value) {
        where = {
          resourceName: resourceName.value,
        }
      }
      getResources({
        page: page.value,
        limit: size.value,
        where,
      }).then((res) => {
        let { data, pagination } = res
        total.value = pagination.total
        dataSource.value = data
      })
    }
    const getListByName = () => {
      page.value = 1
      startGetList()
    }
    const uploadModalVisible = ref(false)
    const uploadForm = ref({
      resourceName: '',
      resourceUrl: '',
      remark: '',
      fileId: '',
    })
    const fileList = ref([])
    const confirmLoading = ref(false)
    const showUploadModal = () => {
      uploadModalVisible.value = true
      uploadForm.value = {
        resourceName: '',
        resourceUrl: '',
        remark: '',
      }
      fileList.value = []
    }
    const beforeUpload = (file) => {
      const isLt100M = file.size / 1024 / 1024 < 100
      if (!isLt100M) {
        message.error('文件必须小于100MB!')
      }

      fileList.value = [file]

      return false
    }
    const addResourcesMth = () => {
      if (!uploadForm.value.resourceName) {
        message.error('请输入资源名称')
        return
      }

      if (fileList.value.length === 0) {
        message.error('请选择要上传的文件')
        return
      }
      addResources(uploadForm.value).then(() => {
        uploadModalVisible.value = false
        startGetList()
      })
    }

    const handleUpload = () => {
      uploadArchive(fileList.value[0].originFileObj).then((res) => {
        uploadForm.value.resourceUrl = res.url
        uploadForm.value.fileId = res.fileId
        message.success('上传成功')
      })
    }
    const cancelUpload = () => {
      uploadModalVisible.value = false
    }
    const handleDownload = (record) => {
      downloadResource(record.fileId)
        .then((response) => {
          // 添加调试信息
          console.log('下载响应:', response)
          console.log('响应数据类型:', typeof response.data)
          console.log('响应数据大小:', response.data.size)

          // 创建Blob对象，指定MIME类型为zip压缩包
          const blob = new Blob([response.data], { type: 'application/zip' })
          console.log('创建的Blob大小:', blob.size)

          // 创建下载链接
          const link = document.createElement('a')
          link.href = window.URL.createObjectURL(blob)

          // 设置下载文件名，确保有.zip扩展名
          let fileName = record.resourceName || 'download'
          if (!fileName.toLowerCase().endsWith('.zip')) {
            fileName += '.zip'
          }
          link.download = fileName

          // 模拟点击下载
          document.body.appendChild(link)
          link.click()

          // 清理
          window.URL.revokeObjectURL(link.href)
          document.body.removeChild(link)

          message.success('下载成功')
        })
        .catch((error) => {
          console.error('下载失败:', error)
          if (error && error.message) {
            message.error(`下载失败: ${error.message}`)
          } else {
            message.error('下载失败，请稍后重试')
          }
        })
    }
    const handleDelete = (record) => {
      deleteResource(record.id)
        .then(() => {
          message.success('删除成功')
          startGetList() // 刷新列表
        })
        .catch(() => {
          message.error('删除失败')
        })
    }
    return {
      startGetList,
      page,
      size,
      total,
      pageChange,
      pageSizeChange,
      resourceName,
      getListByName,
      scrollHight,
      columns,
      dataSource,
      uploadModalVisible,
      uploadForm,
      fileList,
      confirmLoading,
      showUploadModal,
      handleUpload,
      cancelUpload,
      handleDownload,
      handleDelete,
      beforeUpload,
      addResources,
      addResourcesMth
    }
  },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
  margin-right: 8px;
}

.modalBtn {
  text-align: right;
  margin-top: 30px;

  .ant-btn + .ant-btn {
    margin-left: 10px;
  }
}
</style>