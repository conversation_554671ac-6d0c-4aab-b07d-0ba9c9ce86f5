<template>
	<div>
		<div class="welcome-message">
			<h1 class="dashboard-title">欢迎使用工程配置工具</h1>
			<p class="current-time">{{ currentTime }}</p>
		</div>
		<div class="outBox">
			<div class="outItem" v-for="(item, index) in list" :key="index">
				<a-card :title="item" style="width: 300px" :headStyle="headStyle" :bodyStyle="bodyStyle">
					<p class="count-text" v-if="index == 0">总数：{{ countObj.DeviceTypecount }}</p>
					<p class="count-text" v-if="index == 1">总数：{{ countObj.Brandcount }}</p>
					<p class="count-text" v-if="index == 2">总数：{{ countObj.Modelcount }}</p>
					<p class="count-text" v-if="index == 3">总数：{{ countObj.Projectcount }}</p>
				</a-card>
			</div>
		</div>
	</div>
</template>

<script>
	import { deviceStatistics } from '@/api/products'
	import {
		defineComponent,
		reactive,
		onMounted,
		ref
	} from 'vue';
	export default defineComponent({
		setup() {
			const list = [
				'设备类型','设备品牌','设备型号','工程数量'
			]
			const headStyle = {
				textAlign: 'center',
				backgroundColor: '#0067E6',
				color: 'white'
			}
			const bodyStyle = {
				fontSize: '20px'
			}
			const countObj = reactive({
				Brandcount: 0,
				DeviceTypecount: 0,
				Modelcount: 0,
				Projectcount: 0
			})
			const currentTime = ref(new Date().toLocaleTimeString());
			onMounted(() => {
				setInterval(() => {
					currentTime.value = new Date().toLocaleTimeString();
				}, 1000);
				getDeviceStatistics();
			})
			const getDeviceStatistics = async () => {
				const res = await deviceStatistics();
				countObj.Brandcount = res.brandCount;
				countObj.DeviceTypecount = res.typeCount;
				countObj.Modelcount = res.modelCount;
				countObj.Projectcount = res.projectCount;
			}
			return {
				getDeviceStatistics,
				countObj,
				headStyle,
				bodyStyle,
				list,
				currentTime
			};
		},
	});
</script>

<style>
	.welcome-message {
		text-align: center;
		margin-bottom: 30px;
		padding: 20px 0;
	}

	.dashboard-title {
		color: #2c3e50;
		font-size: 2.2em;
		font-weight: 600;
		margin-bottom: 10px;
		text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
	}

	.current-time {
		color: #6c757d;
		font-size: 24px;
		font-weight: 500;
	}

	.count-text {
		text-align: center;
		font-size: 1.2em;
		color: #2c3e50;
		font-weight: 500;
		margin: 10px 0;
	}

	.outBox {
		display: flex;
		justify-content: space-around;
		flex-wrap: wrap;
		padding: 20px;
		position: relative;
		z-index: 1;
	}

	.outItem {
		margin: 20px;
		transition: all 0.3s ease;
		position: relative;
		z-index: 2;
	}

	.outItem:hover {
		transform: translateY(-8px);
		box-shadow: 0 12px 20px rgba(0, 0, 0, 0.08);
	}

	.dd{
		text-align: center;
		font-size: 20px;
		background-color: #096DD9;
		color: white;
		padding: 10px;
		border-radius: 4px;
	}
</style>
