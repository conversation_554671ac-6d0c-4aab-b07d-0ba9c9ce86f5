<template>
  <div class="userManagement-container">
    <div>
      <a-button type="primary" @click="handleEdit('')">添加</a-button>
    </div>
    <div style="margin: 20px 0">
      <a-form layout="inline" :model="queryForm">
        <a-form-item>
          <a-input v-model:value.trim="queryForm.username" placeholder="请输入姓名" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-select v-model:value.trim="queryForm.role" placeholder="请选择角色" style="width: 150px" allow-clear>
            <a-select-option v-for="item in roleList" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="queryData">查询</a-button>
        </a-form-item>
      </a-form>
    </div>

    <a-table :dataSource="list" :columns="columns" :pagination="false" :scroll="{ y: 500 }" :size="'small'" bordered
      :loading="listLoading" :row-key="'id'">
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex === 'serialNumber'">
          {{ index + 1 }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" @click="handleDelete(record)" style="color: red">删除</a-button>
        </template>
      </template>
    </a-table>
    <div class="pagination">
      <Pagination :page="queryForm.pageNo" :size="queryForm.pageSize" :total="total" @pageSizeChange="handleSizeChange"
        @pageChange="handleCurrentChange" />
    </div>
    <edit ref="edit" @fetch-data="fetchData" :roleList="roleList"></edit>
  </div>
</template>

<script>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import Edit from './components/UserManagementEdit'
import Pagination from '@/components/pagination'
import { getUserList, deleteUser } from '../../../api/user'
import { getlist } from '../../../api/dataDeal'
export default {
  name: 'UserManagement',
  components: { Edit, Pagination },
  data() {
    return {
      roleList: [],
      list: null,
      total: 0,
      listLoading: true,
      layout: 'total, sizes, prev, pager, next, jumper',
      selectRows: '',
      elementLoadingText: '正在加载...',
      queryForm: {
        pageNo: 1,
        pageSize: 10,
        username: '',
        role: null,
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'serialNumber',
          key: 'serialNumber',
          width: '50px'
        },
        {
          title: '姓名',
          dataIndex: 'username',
          key: 'username',
        },
        {
          title: '角色',
          dataIndex: 'role_name',
          key: 'role_name',
        },
        {
          title: '手机',
          dataIndex: 'phone',
          key: 'phone',
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
        },
      ],
    }
  },
  created() {
    this.fetchData()
    this.fetchRoleList()
  },
  methods: {
    async fetchRoleList() {
      const { data } = await getlist('roles', { page: 1, limit: 1000 })
      this.roleList = data
    },
    onSelectChange(_selectedRowKeys, selectedRows) {
      this.selectRows = selectedRows
    },
    handleEdit(row) {
      this.$refs['edit'].showEdit(row)
    },
    handleDelete(row) {
      let _this = this
      if (row.id) {
        Modal.confirm({
          title: '你确定要删除该用户吗?',
          icon: createVNode(ExclamationCircleOutlined),
          content: '',
          okText: '确认',
          okType: 'danger',
          cancelText: '取消',
          async onOk() {
            await deleteUser(row.id)
            message.success('删除成功')
            _this.fetchData()
          },
          onCancel() {
            console.log('删除取消')
          },
        })
      }
    },
    handleSizeChange(pageNo, pageSize) {
      this.queryForm.pageNo = pageNo
      this.queryForm.pageSize = pageSize
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val
      this.fetchData()
    },
    queryData() {
      this.queryForm.pageNo = 1
      this.fetchData()
    },
    async fetchData() {
      this.listLoading = true
      let params = {
        page: this.queryForm.pageNo,
        limit: this.queryForm.pageSize,
      }
      if (this.queryForm.username) {
        params.username = this.queryForm.username
      }
      if (this.queryForm.role) {
        params.role = this.queryForm.role
      }
      const { data, pagination} = await getUserList(params)
      this.list = data
      this.total = pagination.total
      this.listLoading = false
    },
  },
}
</script>
