<template>
  <a-modal
    :title="title"
    v-model:visible="dialogFormVisible"
    width="500px"
    @cancel="close"
  >
    <a-form ref="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="用户名" v-bind="validateInfos.username">
        <a-input
          v-model:value.trim="modelRef.username"
          autocomplete="off"
          v-if="title === '添加'"
        ></a-input>
        <span v-else>{{ modelRef.username }}</span>
      </a-form-item>
      <a-form-item label="密码" v-bind="validateInfos.password">
        <a-input
          v-model:value.trim="modelRef.password"  
          type="password"
          autocomplete="off"
        ></a-input>
      </a-form-item>
      <a-form-item label="手机号" v-bind="validateInfos.phone">
        <a-input
          v-model:value.trim="modelRef.phone"
          autocomplete="off"
          maxlength="11"
        ></a-input>
      </a-form-item>
      <a-form-item label="角色" v-bind="validateInfos.role">
        <a-select v-model:value.trim="modelRef.role" placeholder="请选择角色" style="width: 100%" v-if="title === '添加'">
          <a-select-option v-for="item in roleList" :value="item.id" :key="item.id">{{ item.name }}</a-select-option>
        </a-select>
        <span v-else>{{ modelRef.role }}</span>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button key="back" @click="close">取 消</a-button>
      <a-button key="submit" type="primary" @click="save">确 定</a-button>
    </template>
  </a-modal>
</template>

<script>
import { addUser } from '../../../../api/user'
import { defineComponent, reactive, toRaw, ref } from 'vue'
import { Form } from 'ant-design-vue'
import { doEdit } from '../../../../api/dataDeal'
const useForm = Form.useForm
export default defineComponent({
  name: 'UserManagementEdit',
  props: {
    roleList: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const modelRef = reactive({
      username: '',
      password: '',
      phone: '',
      role: '',
    })
    const rulesRef = reactive({
      username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
      password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
      phone: [{ required: true, trigger: 'blur', message: '请输入手机号' }],
      role: [{ required: true, trigger: 'blur', message: '请选择角色' }],
    })
    const title = ref('添加')
    const dialogFormVisible = ref(false)
    const { resetFields, validate, validateInfos } = useForm(modelRef, rulesRef)

    const showEdit = (row) => {
      console.log(row)
      if (!row) {
        title.value = '添加'
        setTimeout(() => {  
          resetFields()
        }, 100)
      } else {
        title.value = '编辑'
        modelRef.username = row.username
        modelRef.password = row.password
        modelRef.phone = row.phone
        modelRef.role = row.role_name
        modelRef.id = row.id
      }
      dialogFormVisible.value = true
    }
    // 保存
    const save = () => {
      validate()
        .then(() => {
          if (title.value === '添加') {
            addUser(toRaw(modelRef)).then(() => {
              close();
              emit('fetch-data')
            })
          } else {
            doEdit('users', modelRef.id, {
              password: modelRef.password,
              phone: modelRef.phone,
            }).then(() => {
              close();
              emit('fetch-data')
            })
          }
        })
        .catch((err) => {
          console.log('error', err)
        })
    }
    // 关闭
    const close = () => {
      resetFields()
      dialogFormVisible.value = false
    }

    return {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 14,
      },
      validateInfos,
      modelRef,
      rulesRef,
      title,
      dialogFormVisible,
      validate,
      resetFields,
      save,
      close,
      showEdit,
    }
  },
})
</script>
