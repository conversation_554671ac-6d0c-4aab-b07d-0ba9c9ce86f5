<template>
  <div>
    <div>项目名称：{{ gw.projectName }}&emsp;项目编号：{{ gw.projectCode }}&emsp;主机名称：{{ gw.name }}&emsp;主机ip1：{{ gw.ip1 }}&emsp;端口类型：<span style="color: red;">{{ gw.serialPortType }}</span> <a
        @click="comeBack">返回</a></div>
    <a-tabs v-model:activeKey="activeKey" :tabBarGutter="10">
      <a-tab-pane v-for="item in getModelType(gw.model)" :key="`${item}`" :tab="`串口${item}`">
        <capture_mgmnt_485 :gw="gw" :portNo="item" v-if="gw.projectId && activeKey == item" />
      </a-tab-pane>
      <a-tab-pane key="17" tab="网口01">
        <capture_mgmnt_485 :gw="gw" :portNo="17" v-if="gw.projectId && activeKey == 17" />
      </a-tab-pane>
      <a-tab-pane key="18" tab="网口02">
        <capture_mgmnt_485 :gw="gw" :portNo="18" v-if="gw.projectId && activeKey == 18" />
      </a-tab-pane>
      <a-tab-pane key="19" tab="DI">
        <capture_mgmnt_485 :gw="gw" :portNo="19" v-if="gw.projectId && activeKey == 19" />
      </a-tab-pane>
      <a-tab-pane key="20" tab="DO">
        <capture_mgmnt_485 :gw="gw" :portNo="20" v-if="gw.projectId && activeKey == 20" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import capture_mgmnt_485 from './capture_mgmnt_485.vue'
import { getGatewayInfo } from '../../../api/projects.js'
export default {

  components: {
    capture_mgmnt_485: capture_mgmnt_485,
  },
  data() {
    return {
      activeKey: '1',
      project: '',
      code: '',
      gw: {},
    }
  },
  created() {
    this.getProjectInfo()
  },
  methods: {
    getModelType(model) {
      let res = ''
      if (model == '16') {
        res = 16
      } else if (model == '12') {
        res = 12
      } else {
        res = 8
      }
      return res;
    },
    getProjectInfo() {
      const gatewayId = this.$route.query.gatewayId
      getGatewayInfo(gatewayId).then((res) => {
        this.gw = res
      })
    },
    comeBack() {
      const key = this.gw.projectId;
      this.$router.push(`/project/edit?key=${key}&activeKey=4`)
    }
  },
}
</script>
