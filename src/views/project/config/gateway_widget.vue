<template>
  <div style="padding: 10px; border: 1px solid #cad0db">
    <a-row>
      <a-col :span="6" @click="checkOnline">{{ status }}</a-col>
      <a-col :span="12" style="text-align: center; font-weight: 600">
        名称/地点：{{ gw.name }}/{{ gw.deviceLocationName }}
      </a-col>
      <a-col :span="6" style="text-align: right; font-weight: 600">
        主机编码:{{ gw.code }}
      </a-col>
    </a-row>
    <a-divider />
    <div style="margin-top: 20px">
      <div style="padding: 5px" class="itemBox">
        <template v-for="(item, index) in listTop" :key="index">
          <div
            style="width: 60px; border: 1px dashed #dbdfe6; border-radius: 5px"
            class="item"
          >
            <div
              style="
                line-height: 45px;
                height: 45x;
                width: 100%;
                text-align: center;
                background: #f5f7fe;
              "
            >
              {{ gw.model == '16口' ? 16 : 12 }}
            </div>
            <template v-if="item == '网口1'">
              <div
                style="
                  line-height: 20px;
                  height: 20x;
                  text-align: center;
                  background: #e9c0a5;
                  color: #ef854d;
                "
              >
                {{ item }}
              </div>
            </template>
            <template v-else>
              <div
                style="
                  line-height: 20px;
                  height: 20x;
                  text-align: center;
                  background: #dbdfe6;
                "
              >
                {{ item }}
              </div>
            </template>
          </div>
        </template>
      </div>
      <div style="padding: 5px" class="itemBox">
        <template v-for="(item, index) in listBottom" :key="index">
          <div
            style="width: 60px; border: 1px dashed #dbdfe6; border-radius: 5px"
            class="item"
          >
            <div
              style="
                line-height: 45px;
                height: 45x;
                width: 100%;
                text-align: center;
                background: #f5f7fe;
              "
            >
              {{ gw.model == '16口' ? 16 : 12 }}
            </div>
            <template v-if="item == '网口2'">
              <div
                style="
                  line-height: 20px;
                  height: 20x;
                  text-align: center;
                  background: #e9c0a5;
                  color: #ef854d;
                "
              >
                {{ item }}
              </div>
            </template>
            <template v-else>
              <div
                style="
                  line-height: 20px;
                  height: 20x;
                  text-align: center;
                  background: #dbdfe6;
                "
              >
                {{ item }}
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>
    <div class="abc" style="width: 100%">
      <div style="width: 50%; padding: 5px">
        <div
          style="
            height: 60px;
            padding: 5px;
            background: #f5f7fe;
            font-weight: 600;
          "
        >
          <div style="text-align: center">IP地址: {{ gw.ip1 }}</div>
          <div style="text-align: center">网口1: {{ gw.mac1 }}</div>
        </div>
      </div>
      <div style="width: 50%; padding: 5px">
        <div
          style="
            height: 60px;
            padding: 5px;
            background: #f5f7fe;
            font-weight: 600;
          "
        >
          <div style="text-align: center">IP地址: {{ gw.ip2 }}</div>
          <div style="text-align: center">网口2: {{ gw.mac2 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    props: ['gw'],
    data() {
      const httpapi = axios.create({
        baseURL: 'http://' + this.gw.ip1 + ':8888',
      })

      return {
        httpapi: httpapi,
        status: '检查中',
        listTop: [],
        listBottom: [],
        list1: [2, 4, 6, 8, 10, 12, 14, 16, '网口1'],
        list2: [1, 3, 5, 7, 9, 11, 13, 15, '网口2'],
        list3: [2, 4, 6, 8, 10, 12, '网口1'],
        list4: [1, 3, 5, 7, 9, 11, '网口2'],
      }
    },
    mounted() {
      if (this.gw.model == '16口') {
        this.listTop = this.list1
        this.listBottom = this.list2
      } else {
        this.listTop = this.list3
        this.listBottom = this.list4
      }

      this.checkOnline()
    },
    methods: {
      async checkOnline() {
        console.log('检查是否在线')
        console.log('http://' + this.gw.ip1 + ':' + this.gw.port1)
        try {
          if (window.location.href.indexOf('localhost') !== -1) {
            // let lastPart = this.gw.ip1.split('.')[3]
            // let baseURL = 'http://*************:' + '180' + lastPart
            // let baseURL = `${this.gw.ip1}:8888` 
            // console.log('baseURL', baseURL)
            // const httpapi = axios.create({
            //   baseURL: baseURL,
            // })
            const resp = await this.httpapi.get('/api/gw/status')
            console.log(this.httpapi)
            this.status = '在线'
          } else {
            const resp = await this.httpapi.get('/api/gw/status')
            console.log(this.httpapi)
            this.status = '在线'
          }
        } catch (error) {
          console.log(error)
          this.status = '离线'
        }
      },
    },
  }
</script>

<style scoped>
  .abc,
  .itemBox {
    display: flex;
  }
  .item + .item {
    margin-left: 10px;
  }
  .abcd {
    display: flex;
    justify-content: space-around;
  }
</style>
