<template>
  <div>
    <a-button @click="doSync" v-if="offlineMode">本地->云端 同步</a-button>
    <a-button @click="doSync" v-else>云端->本地 同步</a-button>
    <div
      style="height: 70vh; overflow: scroll; background: white; padding: 5px"
      id="log"
    >
      <div
        v-for="(log, idx) in logs"
        v-bind:key="'log_' + idx"
        style="color: black"
      >
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    data() {
      return {
        offlineMode: window.offlineMode,
        scanTimeout: 5,
        scanTimeoutCount: 0,
        scannedGateways: [],
        logs: [],
      }
    },
    mounted() {},
    methods: {
      appendLog(log) {
        this.logs.push(log)
        const elem = document.getElementById('log')
        elem.scrollTop = elem.scrollHeight
      },
      async getProject() {
        let project = await viewProject(this.$route.query.key)
        project = { ...project.attributes }
        return project
      },
      async getAddressList() {
        let addresList = await getProjectAddress({
          code: this.$route.query.code,
        })
        return addresList.results
      },
      async getDeviceList() {
        let deviceList = await getProjectDeviceByCode1(this.$route.query.code)
        return deviceList.results
      },

      async getProductList() {
        let productList = await getProjectProductList({
          projectCode: this.$route.query.code,
        })
        return productList.results
      },
      async doSync() {
        console.log('doSync')
        let baseURL = window.syncURL
        // 'http://beta.smartxdc.com:42337/engineering_platform'
        const httpapi = axios.create({
          baseURL: baseURL,
          headers: {
            'X-Parse-Application-Id': 'com.smartxdc.engineering_platform',
            'X-Parse-REST-API-Key': 'LKSoft@123Rest',
            'X-Parse-Master-Key': 'LKSoft@123',
            'Content-Type': 'application/json',
          },
        })

        let project = await this.getProject()
        this.appendLog('同步项目信息: ' + project.name)
        await httpapi.post('/functions/syncProjectData', {
          classType: 'Project',
          projectCode: this.$route.query.code,
          rows: [project],
        })
        this.appendLog('同步项目信息成功')

        let addressList = await this.getAddressList()
        let addRessRows = []
        for (let i = 0; i < addressList.length; i++) {
          let address = addressList[i].attributes
          addRessRows.push(address)
          this.appendLog('同步地址信息: ' + address.title)
        }
        await httpapi.post('/functions/syncProjectData', {
          classType: 'ProjectAddress',
          projectCode: this.$route.query.code,
          rows: addRessRows,
        })
        this.appendLog('同步地址信息成功')

        if (false) {
          let deviceList = await this.getDeviceList()
          let deviceRows = []
          for (let i = 0; i < deviceList.length; i++) {
            let device = deviceList[i].attributes
            deviceRows.push(device)
            this.appendLog('同步设备信息: ' + device.deviceName)
          }
          await httpapi.post('/functions/syncProjectData', {
            classType: 'ProjectDevice',
            projectCode: this.$route.query.code,
            rows: deviceRows,
          })
          this.appendLog('同步设备信息成功')
        }

        let productList = await this.getProductList()
        let productRows = []
        for (let i = 0; i < productList.length; i++) {
          let product = productList[i].attributes
          productRows.push(product)
          this.appendLog('同步产品信息: ' + product.name)
        }
        await httpapi.post('/functions/syncProjectData', {
          classType: 'ProjectProduct',
          projectCode: this.$route.query.code,
          rows: productRows,
        })
        this.appendLog('同步产品成功')
      },
    },
  }
</script>
