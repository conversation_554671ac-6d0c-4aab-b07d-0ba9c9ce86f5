<template>
  <div>
    <a-row :gutter="20">
      <a-col class="text-center">
        <a-input
          v-model:value.trim="mainName"
          placeholder="请输入主机名称"
          style="width: 200px"
          allow-clear
        />
        <a-input
          v-model:value.trim="mainCode"
          placeholder="请输入主机编码"
          style="width: 200px; margin-left: 10px"
          allow-clear
        />
        <a-button
          type="primary"
          @click="searchGateways"
          style="margin-left: 10px"
        >
          搜索
        </a-button>
        <a-button
          type="primary"
          @click="showCreateDlg"
          style="margin-left: 10px"
        >
          手动新建
        </a-button>
        <a-button
          type="primary"
          @click="discoverGateways"
          style="margin-left: 10px"
        >
          搜索相同网段内采集主机
        </a-button>
      </a-col>
    </a-row>

    <a-divider />
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="false"
      :scroll="{ y: scrollHight }"
      :size="'small'"
    >
      <template #bodyCell="{ column, index, record, text }">
        <template v-if="column.key === 'index'">
          {{ index + 1 }}
        </template>
        <template v-else-if="column.key === 'status'">
          <span>
            <template v-if="text == '离线'">
              <a-tag color="red">{{ text }}</a-tag>
            </template>
            <template v-else-if="text == '在线'">
              <a-tag color="green">{{ text }}</a-tag>
            </template>
            <template v-else>
              <a-button
                type="primary"
                @click="checkOnline(record)"
                size="small"
              >
                检测
              </a-button>
            </template>
          </span>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a @click="showCaptureDlg(record)">采集管理</a>
            <a-divider type="vertical" />
            <a @click="openSyncModal(record)">同步</a>
            <a-divider type="vertical" />
            <a @click="showEditDlg(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="confirmToDel(record)">删除</a>
          </span>
        </template>
      </template>
    </a-table>
    <div style="margin-top: 10px">
      <Pagination
        :page="page"
        :size="size"
        :total="total"
        @pageSizeChange="pageSizeChange"
        @pageChange="pageChange"
      />
    </div>
  </div>
  <a-modal v-model:visible="deleteModel" title="提示">
    <p>确定要删除吗？</p>
    <template #footer>
      <a-button key="back" @click="deleteModel = false">取消</a-button>
      <a-button key="submit" type="primary" @click="handleOk">确认</a-button>
    </template>
  </a-modal>
  <a-modal
    v-model:visible="scanDlgVisible"
    width="40vw"
    title="扫描中(3秒后自动关闭)"
  >
    <div>使用该功能，请提前关闭Windows防火墙</div>
    <div v-for="(foundGW, idx) in scannedGateways" v-bind:key="'g_' + idx">
      {{ idx + 1 }}:
      {{
        foundGW.mac1 +
        '(' +
        foundGW.ip1 +
        ')' +
        ' - ' +
        foundGW.mac2 +
        '(' +
        foundGW.ip2 +
        ')'
      }}
    </div>
  </a-modal>

  <a-modal
    v-model:visible="projectDlgVisible"
    width="1000px"
    :title="inEditMode ? '编辑嵌入式' : '新增嵌入式'"
    okText="保存"
    @ok="doSave"
  >
    <a-row :gutter="10">
      <a-col :span="3" style="line-height: 28px; height: 28px">主机类型:</a-col>
      <a-col :span="4">
        <a-select v-model:value="newGateway.model" style="width: 200px">
          <a-select-option value="16">16口</a-select-option>
          <a-select-option value="12">12口</a-select-option>
          <a-select-option value="6">6口</a-select-option>
        </a-select>
      </a-col>
    </a-row>

    <a-row :gutter="10" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">主机名称:</a-col>
      <a-col :span="4">
        <a-input v-model:value="newGateway.name" style="width: 200px" />
      </a-col>
      <a-col
        :span="4"
        style="
          margin-left: 20px;
          line-height: 28px;
          height: 28px;
          text-align: right;
        "
      >
        主机编码:
      </a-col>
      <a-col>
        <a-input v-model:value="newGateway.code" @change="onChange" />
      </a-col>
    </a-row>

    <a-row :gutter="10" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">所属地点:</a-col>
      <a-col :span="21">
        <a-tree-select
          v-model:value="newGateway.deviceLocationId"
          style="width: 100%"
          placeholder="请选择地址"
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          treeLine
          :field-names="{
            children: 'children',
            label: 'addressName',
            key: 'addressCode',
            value: 'addressCode',
          }"
        ></a-tree-select>
      </a-col>
    </a-row>

    <a-row :gutter="10" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">串口类别:</a-col>
      <a-col :span="4">
        <a-select
          v-model:value="newGateway.serialPortType"
          style="width: 200px"
        >
          <a-select-option value="ttyWCH">ttyWCH</a-select-option>
          <a-select-option value="ttyX">ttyX</a-select-option>
        </a-select>
      </a-col>
      <a-col
        :span="10"
        style="line-height: 28px; height: 28px; margin-left: 100px; color: red"
      >
        注:ttyWCH是英康士采集主机，ttyX是科云思采集主机
      </a-col>
    </a-row>
    <a-row :gutter="10" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">
        绑定主机MAC:
      </a-col>
      <a-col :span="21">
        <a-select
          v-model:value="newGateway.macSelected"
          style="width: 100%"
          @change="onMacSelected"
        >
          <a-select-option
            v-for="(foundGW, idx2) in scannedGateways"
            v-bind:key="'foundGW_' + idx2"
            :value="foundGW.mac1"
          >
            {{
              foundGW.mac1 +
              '(' +
              foundGW.ip1 +
              ')' +
              ' - ' +
              foundGW.mac2 +
              '(' +
              foundGW.ip2 +
              ')'
            }}
          </a-select-option>
        </a-select>
        <!-- <a-input v-model:value="newGateway.mac" style="width: 200px" /> -->
      </a-col>
    </a-row>

    <a-divider>网络配置</a-divider>

    <a-row :gutter="12" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">主机IP-1:</a-col>
      <a-col>
        <a-input v-model:value="newGateway.ip1" style="width: 200px" />
      </a-col>
      <a-col :span="3" style="line-height: 28px; height: 28px">IP-1端口:</a-col>
      <a-col>
        <a-input v-model:value="newGateway.port1" style="width: 200px" />
      </a-col>
    </a-row>

    <a-row :gutter="12" style="margin-top: 5px">
      <a-col :span="3" style="line-height: 28px; height: 28px">主机IP-2:</a-col>
      <a-col>
        <a-input v-model:value="newGateway.ip2" style="width: 200px" />
      </a-col>
      <a-col :span="3" style="line-height: 28px; height: 28px">IP-2端口:</a-col>
      <a-col>
        <a-input v-model:value="newGateway.port2" style="width: 200px" />
      </a-col>
    </a-row>

    <a-row :gutter="12" style="margin-top: 5px">
      <a-col
        :span="3"
        style="line-height: 28px; height: 28px"
      >
        主机MAC-1:
      </a-col>
      <a-col>
        <a-input v-model:value="newGateway.mac1" style="width: 200px" />
      </a-col>
      <a-col
        :span="3"
        style="line-height: 28px; height: 28px"
      >
        主机MAC-2:
      </a-col>
      <a-col>
        <a-input v-model:value="newGateway.mac2" style="width: 200px" />
      </a-col>
    </a-row>

    <div v-if="false">
      <a-divider>数据库配置</a-divider>
      <a-row :gutter="10" style="margin-top: 5px">
        <a-col :span="3" style="line-height: 28px; height: 28px">类型:</a-col>
        <a-col :span="4">
          <a-select
            v-model:value="newGateway.databaseType"
            style="width: 200px"
          >
            <a-select-option value="MySQL">MySQL</a-select-option>
            <!-- <a-select-option value="SQLServer">SQLServer</a-select-option> -->
          </a-select>
        </a-col>
        <a-col
          :span="4"
          style="
            margin-left: 20px;
            line-height: 28px;
            height: 28px;
            text-align: right;
          "
        >
          库名称:
        </a-col>
        <a-col><a-input v-model:value="newGateway.databaseName" /></a-col>
      </a-row>
      <a-row :gutter="10" style="margin-top: 5px">
        <a-col :span="3" style="line-height: 28px; height: 28px">IP:</a-col>
        <a-col :span="4">
          <a-input v-model:value="newGateway.databaseIp" style="width: 200px" />
        </a-col>
        <a-col
          :span="4"
          style="
            margin-left: 20px;
            line-height: 28px;
            height: 28px;
            text-align: right;
          "
        >
          端口:
        </a-col>
        <a-col><a-input v-model:value="newGateway.databasePort" /></a-col>
      </a-row>

      <a-row :gutter="10" style="margin-top: 5px">
        <a-col :span="3" style="line-height: 28px; height: 28px">
          Username:
        </a-col>
        <a-col :span="4">
          <a-input
            v-model:value="newGateway.databaseUsername"
            style="width: 200px"
          />
        </a-col>
        <a-col
          :span="4"
          style="
            margin-left: 20px;
            line-height: 28px;
            height: 28px;
            text-align: right;
          "
        >
          Password:
        </a-col>
        <a-col><a-input v-model:value="newGateway.databasePassword" /></a-col>
      </a-row>
    </div>
  </a-modal>

  <div v-if="captureDlgVisible">
    <a-modal
      v-model:visible="captureDlgVisible"
      width="90vw"
      height="70vh"
      title="采集管理"
      okText="保存"
      :footer="null"
    >
      <capture_mgmnt :gw="captureGateway" />
    </a-modal>
  </div>

  <a-modal
    v-model:visible="newModalVisible"
    title="同步到采集主机"
    width="40vw"
  >
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">
        主机名称:
      </div>
      <div style="font-weight: bold">{{ checkGateway.name }}</div>
    </div>
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">
        主机编码:
      </div>
      <div style="font-weight: bold">{{ checkGateway.code }}</div>
    </div>
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">
        状态:
      </div>
      <div style="font-weight: bold">
        <a-tag color="blue" v-if="checkGateway.status === '在线'">在线</a-tag>
        <a-tag color="red" v-else-if="checkGateway.status === '离线'">
          离线
        </a-tag>
        <a-tag color="orange" v-else>请先检测</a-tag>
      </div>
      <a-button type="primary" @click="checkOnline(checkGateway)" size="small">
        检测
      </a-button>
    </div>
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">IP:</div>
      <div style="font-weight: bold">{{ checkGateway.ip1 }}</div>
    </div>
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">
        同步设备:
      </div>
      <div style="font-weight: bold; color: blue">
        {{ checkGateway.deviceCount }}
      </div>
    </div>
    <div class="flex">
      <div style="width: 100px; text-align: right; margin-right: 10px">
        同步进度:
      </div>
      <div style="font-weight: bold">
        <span style="color: green">
          {{ checkGateway.syncedDeviceCount || 0 }}
        </span>
        /
        <span style="color: blue">{{ checkGateway.deviceCount }}</span>
      </div>
    </div>
    <div class="flex" style="flex-direction: column">
      <div style="font-weight: bold; color: red">同步过程中请勿关闭页面</div>
    </div>
    <div v-if="errorMsg" style="color: red">{{ errorMsg }}</div>
    <div
      class="flex"
      style="flex-direction: column"
      v-if="
        checkGateway.syncedDeviceCount === checkGateway.deviceCount &&
        checkGateway.syncedDeviceCount > 0
      "
    >
      <div style="font-weight: bold; color: green">恭喜您，同步成功</div>
    </div>
    <template #footer>
      <a-button key="back" @click="newModalVisible = false">关闭</a-button>
      <a-button key="submit" type="primary" @click="handleNewModalOk">
        开始同步
      </a-button>
    </template>
  </a-modal>
</template>

<script>
  const makeTree = (animals, parent) => {
    let node = []
    let filters = animals.filter((c) => c.parentId === parent)
    if (filters.length) {
      filters.forEach((c) => {
        node.push(c)
        let n = makeTree(animals, c.key)
        if (n.length > 0) {
          c.children = n
        }
      })
    }
    return node
  }
  import { useRouter } from 'vue-router'
  import gatewayWidget from './gateway_widget.vue'
  import capture_mgmnt from './capture_mgmnt.vue'
  import {
    getDevicesByGatewayId,
    getRulesByDeviceId,
  } from '../../../api/projects.js'

  import axios from 'axios'
  import { message } from 'ant-design-vue'
  import {
    locationsTree,
    addGateway,
    getGatewaysList,
    deleteGateway,
    editGateway,
  } from '../../../api/projects'
  import Pagination from '../../../components/pagination.vue'
  const staticGateway = {
    id: null,
    model: '16',
    name: '',
    code: '',
    deviceLocationId: '',
    macSelected: '',
    mac: '',
    ip1: '',
    port1: '8888',
    mac1: '',
    ip2: '',
    port2: '',
    mac2: '',
    port: 0,
    username: 'admin',
    password: 'xdc_collector123456',
    databaseIp: '',
    databasePort: 3306,
    databaseType: 'MySQL',
    databaseName: 'xdc_collector',
    databaseUsername: 'root',
    databasePassword: 'rooter',
    serialPortType: 'ttyWCH',
  }
  // Throttle function to limit the rate of function calls
  function throttle(func, delay) {
    let lastCall = 0
    return function (...args) {
      const now = new Date().getTime()
      if (now - lastCall < delay) {
        return
      }
      lastCall = now
      return func.apply(this, args)
    }
  }

  export default {
    components: {
      gatewayWidget: gatewayWidget,
      capture_mgmnt: capture_mgmnt,
      Pagination: Pagination,
    },
    props: {
      detailId: String,
      mainInfo: Object,
      code: String,
    },
    data() {
      return {
        errorMsg: '',
        mainName: '',
        mainCode: '',
        page: 1,
        size: 10,
        total: 0,
        delIdx: '',
        delGw: '',
        deleteModel: false,
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 60,
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 160,
          },
          {
            title: '地点',
            dataIndex: 'locationName',
            key: 'locationName',
            width: 160,
          },
          {
            title: '编码',
            dataIndex: 'code',
            key: 'code',
          },
          {
            title: '端口',
            dataIndex: 'serialPortType',
            key: 'serialPortType',
          },
          {
            title: '端口类型',
            dataIndex: 'model',
            key: 'model',
          },
          {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
          },
          {
            title: 'IP地址1',
            dataIndex: 'ip1',
            key: 'ip1',
          },
          {
            title: '网口1',
            dataIndex: 'mac1',
            key: 'mac1',
          },
          {
            title: 'IP地址2',
            dataIndex: 'ip2',
            key: 'ip2',
          },
          {
            title: '网口2',
            dataIndex: 'mac2',
            key: 'mac2',
          },
          {
            title: '设备数量',
            dataIndex: 'deviceCount',
            key: 'deviceCount',
          },
          {
            title: '操作',
            key: 'action',
            width: 280,
          },
        ],
        offlineMode: window.offlineMode,
        syncOptionDlgVisible: false,
        treeData: [],
        project: undefined,
        searchText: '',
        projectDlgVisible: false,
        captureDlgVisible: false,
        inEditMode: false,
        captureGateway: undefined,
        scanDlgVisible: false,
        newGateway: {
          id: null,
          model: '16',
          name: '',
          code: '',
          deviceLocationId: '',
          macSelected: '',
          mac: '',
          ip1: '',
          port1: '8888',
          mac1: '',
          ip2: '',
          port2: '',
          mac2: '',
          username: 'admin',
          password: 'xdc_collector123456',
          databaseIp: '',
          databasePort: 3306,
          databaseType: 'MySQL',
          databaseName: 'xdc_collector',
          databaseUsername: 'root',
          databasePassword: 'rooter',
          serialPortType: 'ttyWCH',
        },
        scanTimeout: 5,
        scanTimeoutCount: 0,
        scannedGateways: [],
        scannedGatewaysExisted: {},
        gateways: [],
        selectedIndex: -1, // 选择的网关
        router: null,
        scrollHight: 0,
        newModalVisible: false,
        checkGateway: null,
      }
    },

    mounted() {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      this.scrollHight = h - 280
      this.router = useRouter()
      this.getProject()
    },
    methods: {
      searchGateways() {
        this.getProject()
      },
      pageChange(page) {
        this.page = page
        this.getProject()
      },
      pageSizeChange(page, size) {
        this.page = page
        this.size = size
      },
      // 只能输入数字
      onChange() {
        this.newGateway.code = this.newGateway.code.replace(/\D/g, '')
      },
      async getDeviceList(gw) {
        let deviceList = await getProjectDeviceByCode1(
          this.$route.query.code,
          gw.code
        )
        return deviceList
      },
      // async syncToCloud(gw, idx) {
      //   let baseURL = window.syncURL
      //   // 'http://beta.smartxdc.com:42337/engineering_platform'
      //   const httpapi = axios.create({
      //     baseURL: baseURL,
      //     headers: {
      //       'X-Parse-Application-Id': 'com.smartxdc.engineering_platform',
      //       'X-Parse-REST-API-Key': 'LKSoft@123Rest',
      //       'X-Parse-Master-Key': 'LKSoft@123',
      //       'Content-Type': 'application/json',
      //     },
      //   })

      //   let deviceList = await this.getDeviceList(gw)
      //   let deviceRows = []
      //   for (let i = 0; i < deviceList.length; i++) {
      //     let device = deviceList[i].attributes
      //     if (device.agentCode === gw.code) {
      //       deviceRows.push(device)
      //     }
      //   }
      //   await httpapi.post('/functions/syncProjectData', {
      //     classType: 'ProjectDevice',
      //     projectCode: this.$route.query.code,
      //     agentCode: gw.code,
      //     rows: deviceRows,
      //   })
      //   message.success('同步设备信息成功')
      // },
      openSyncModal(gw) {
        this.checkGateway = gw
        this.checkGateway.syncedDeviceCount = 0
        this.newModalVisible = true
      },
      // 同步到采集主机
      async syncToGW(gw) {
        try {
          // 同步到网关
          let baseURL = 'http://' + gw.ip1 + `:${gw.port1}`
          const httpapi = axios.create({
            baseURL: baseURL,
          })
          await httpapi.post('/api/gw/clearDatabase')
          let devices = await getDevicesByGatewayId(gw.id)
          let syncedDeviceCount = 0
          for (let i = 0; i < devices.length; i++) {
            let q = { ...devices[i] }
            q.projectCode = this.mainInfo.code
            q.agentCode = gw.code
            q.comAddress = q.originalAddress
            q.deviceId = q.deviceCode
            q.deviceState = 1
            await httpapi.post('/api/devices/create', q)
            let deviceParams = []
            // 同步时验证采集主机是否有告警规则存在
            let rulesList = await getRulesByDeviceId(q.id)
            if (rulesList.length > 0) {
              let getRulesList = await httpapi.post(
                '/api/deviceParams/getRules'
              )
              let onlineRulesList = getRulesList.data.deviceParams
              if (onlineRulesList.length > 0) {
                for (const iterator of onlineRulesList) {
                  if (iterator.dataValidLen == '' || !iterator.dataValidLen) {
                    iterator.dataValidLen = null
                  }
                }
              }
              for (let j = 0; j < rulesList.length; j++) {
                let deviceParam = rulesList[j]
                deviceParam.projectCode = this.mainInfo.code
                deviceParam.agentCode = gw.code
                deviceParam.deviceId = q.deviceCode
                deviceParam.deviceName = q.deviceName
                let alarmLevel = ''
                //判断是否存在orig
                if (deviceParam.origParams) {
                  let alarmRule = []
                  let args = {}
                  if (deviceParam.origParams['alarmEnable']) {
                    let DataChar = deviceParam.origParams['参数类型DataChar']
                    if (deviceParam.origParams['上限告警事件MaxEvent']) {
                      alarmLevel = parseInt(
                        deviceParam.origParams['上限告警事件MaxEvent']
                      )
                      if (alarmLevel > 6) {
                        alarmLevel = 3
                      }
                    } else {
                      alarmLevel = undefined
                    }
                    // Analogy是模拟量 CommStatus Digital  状态量
                    //模拟量
                    if (DataChar == 'Analogy') {
                      args = {
                        alarmContentColor: '#FF6900',
                        maxValue:
                          Number(
                            deviceParam.origParams['上限一级告警阈值MaxAlarm']
                          ) ?? undefined,
                        minValue:
                          Number(
                            deviceParam.origParams['下限一级告警阈值MinAlarm']
                          ) ?? undefined,
                        alarmContentHigh:
                          deviceParam.origParams['上限告警内容MaxContentDo'] ??
                          undefined,
                        disalarmHighContent:
                          deviceParam.origParams[
                            '上限告警解除内容MaxContentUnDo'
                          ] ?? undefined,
                        alarmContentLow:
                          deviceParam.origParams['下限告警内容MinContentDo'] ??
                          undefined,
                        disalarmLowContent:
                          deviceParam.origParams[
                            '下限告警解除内容MinContentUnDo'
                          ] ?? undefined,
                        alarmLevel,
                      }
                      alarmRule.push({ args, js: 'xdcAlarmCheckAnalogyV2' })
                    } //状态量
                    else {
                      args = {
                        alarmContentColor: '#FF6900',
                        normalValue:
                          parseInt(
                            deviceParam.origParams['上限一级告警阈值MaxAlarm']
                          ) ?? undefined,
                        alarmContent:
                          deviceParam.origParams['上限告警内容MaxContentDo'] ??
                          undefined,
                        disalarmContent:
                          deviceParam.origParams[
                            '上限告警解除内容MaxContentUnDo'
                          ] ?? undefined,
                        alarmLevel,
                      }
                      alarmRule.push({ args, js: 'xdcAlarmCheckEnumV2' })
                    }
                    //alarmRule.push({ args, "js": "xdcAlarmCheckAnalogyV2" })
                  } else {
                    alarmRule = []
                  }
                  deviceParam.State0 =
                    deviceParam.origParams['状态0State0'] ?? ''
                  deviceParam.State1 =
                    deviceParam.origParams['状态1State1'] ?? ''
                  deviceParam.alarmRule = JSON.stringify(alarmRule) ?? []
                }
                //不存在orig读取普通数据
                else {
                  let alarmRule = []
                  let args = {}
                  if (deviceParam.alarmEnable) {
                    let DataChar = deviceParam.paramDataType
                    if (deviceParam.MaxEvent) {
                      alarmLevel = parseInt(deviceParam.MaxEvent)
                      if (alarmLevel > 6) {
                        alarmLevel = 3
                      }
                    } else {
                      alarmLevel = undefined
                    }
                    // Analogy是模拟量 CommStatus Digital  状态量
                    //模拟量
                    if (DataChar == 'Analogy') {
                      args = {
                        alarmContentColor: '#FF6900',
                        maxValue: Number(deviceParam.MaxAlarm) ?? undefined,
                        minValue: Number(deviceParam.MinAlarm) ?? undefined,
                        alarmContentHigh: deviceParam.MaxContentDo ?? undefined,
                        disalarmHighContent:
                          deviceParam.MaxContentUnDo ?? undefined,
                        alarmContentLow: deviceParam.MinContentDo ?? undefined,
                        disalarmLowContent:
                          deviceParam.MinContentUnDo ?? undefined,
                        alarmLevel,
                      }
                      alarmRule.push({ args, js: 'xdcAlarmCheckAnalogyV2' })
                    } //状态量
                    else {
                      args = {
                        alarmContentColor: '#FF6900',
                        normalValue:
                          parseInt(deviceParam.MaxAlarm) ?? undefined,
                        alarmContent: deviceParam.MaxContentDo ?? undefined,
                        disalarmContent:
                          deviceParam.MaxContentUnDo ?? undefined,
                        alarmLevel,
                      }
                      alarmRule.push({ args, js: 'xdcAlarmCheckEnumV2' })
                    }
                    //alarmRule.push({ args, "js": "xdcAlarmCheckAnalogyV2" })
                  } else {
                    alarmRule = []
                  }
                  deviceParam.State0 = deviceParam.State0 ?? ''
                  deviceParam.State1 = deviceParam.State1 ?? ''
                  deviceParam.alarmRule = JSON.stringify(alarmRule) ?? []
                }

                deviceParam.paramDefValue = deviceParam.defValue
                deviceParam.paramState = 1
                deviceParam.paramType = deviceParam.dataType
                deviceParam.dataUnit = deviceParam.dataUnit || ''
                deviceParam.dataStartPos = deviceParam.dataStartPos || 0
                deviceParam.dataExtra = deviceParam.dataExtra || '{}'
                deviceParam.paramValue = deviceParam.paramDefValue
                if (typeof deviceParam.needRec === 'string') {
                  deviceParam.needRec = deviceParam.needRec === 'True'
                }
                if (typeof deviceParam.needRpt === 'string') {
                  deviceParam.needRpt = deviceParam.needRpt === 'True'
                }
                if (typeof deviceParam.alarmEnable === 'string') {
                  deviceParam.alarmEnable = deviceParam.alarmEnable === 'True'
                }
                if (
                  deviceParam.dataValidLen == '' ||
                  !deviceParam.dataValidLen
                ) {
                  deviceParam.dataValidLen = null
                }
                let onlineRules = onlineRulesList.filter(
                  (item) =>
                    item.paramCode == deviceParam.paramCode &&
                    item.deviceId == q.deviceCode
                )
                if (onlineRules.length > 0) {
                  for (const iterator of onlineRules) {
                    if (iterator.paramCode == deviceParam.paramCode) {
                      deviceParam.alarmDebounce = iterator.alarmDebounce
                      deviceParam.alarmEnable = iterator.alarmEnable
                      deviceParam.alarmThrottle = iterator.alarmThrottle
                      if (iterator.alarmRule !== []) {
                        deviceParam.alarmRule = iterator.alarmRule
                      }
                    }
                  }
                }
                deviceParams.push(deviceParam)
              }
            }
            await httpapi.post('/api/deviceParams/createInBatch', {
              deviceParams: deviceParams,
            })
            syncedDeviceCount += 1
            this.checkGateway.syncedDeviceCount = syncedDeviceCount
          }
          message.success('同步成功, 采集程序重启中')
          setTimeout(() => {
            httpapi.post('/api/devices/progExit', {})
          }, 2000)
        } catch (error) {
          this.errorMsg = error.message
          message.error('同步失败')
        }
      },
      // 获取地址数据
      async getLocations() {
        let key = this.$route.query.key
        let res = await locationsTree(key)
        this.treeData = res
      },
      // 获取网关列表
      async getProject() {
        let projectId = this.$route.query.key
        let search = null
        if (this.mainName) {
          search = { name: this.mainName }
        }
        if (this.mainCode) {
          search = { code: this.mainCode }
        }
        let project = await getGatewaysList(projectId, {
          page: this.page,
          limit: this.size,
          search,
        }) // TODO
        const { data, pagination } = project
        this.dataSource = data
        this.total = pagination.total
      },
      // 检测网关是否在线
      async checkOnline(record) {
        const httpapi = axios.create({
          baseURL: 'http://' + record.ip1 + `:${record.port1}`,
          timeout: 1000,
        })
        try {
          await httpapi.get('/api/gw/status')
          record.status = '在线'
        } catch (error) {
          record.status = '离线'
        }
      },
      // 新建
      showCreateDlg() {
        this.getLocations()
        if (this.treeData) {
          this.newGateway = { ...staticGateway }
          this.inEditMode = false
          this.projectDlgVisible = true
        }
      },
      // 编辑网关
      showEditDlg(gw) {
        this.getLocations()
        if (this.treeData) {
          this.newGateway = { ...gw }
          this.inEditMode = true
          this.projectDlgVisible = true
        }
      },
      // 删除网关
      confirmToDel(gw) {
        this.deleteModel = true
        this.delGw = gw
      },
      // 删除网关
      async handleOk() {
        await deleteGateway(this.delGw.id)
        this.deleteModel = false
        this.getProject()
      },
      // 新增和编辑网关
      async doSave() {
        this.newGateway.projectId = this.$route.query.key
        if (this.inEditMode) {
          await editGateway(this.newGateway.id, this.newGateway)
          message.success('编辑成功')
        } else {
          await addGateway(this.newGateway)
          message.success('新增成功')
        }
        this.projectDlgVisible = false
        this.getProject()
      },
      // 采集管理
      showCaptureDlg(record) {
        let id = record.id
        this.router.push({
          name: 'collectionManage',
          query: { gatewayId: id },
        })
      },
      // 绑定主机MAC
      onMacSelected() {
        this.scannedGateways.forEach((e) => {
          if (e.mac1 === this.newGateway.macSelected) {
            this.newGateway.mac1 = e.mac1
            this.newGateway.ip1 = e.ip1
            this.newGateway.mac2 = e.mac2
            this.newGateway.ip2 = e.ip2
            this.newGateway.port1 = e.port1
            this.newGateway.port2 = e.port2
            this.newGateway.databaseIp = e.ip1
          }
        })
      },
      // 扫描网关
      discoverGateways() {
        this.scanDlgVisible = true
        let dgram = window.require('dgram')

        this.scannedGatewaysExisted = {}
        this.scannedGateways = []

        // 设备上的组播接收服务器
        let deviceServerIP = '*********'
        let deviceServerPort = 20000

        // 扫描工具上的组播接收服务器
        let scanServerIP = '*********'
        let scanServerPort = 11000
        let scanServer

        scanServer = dgram.createSocket('udp4')

        scanServer.bind(scanServerPort, function () {
          scanServer.addMembership(scanServerIP)
          scanServer.addMembership(scanServerIP, '127.0.0.1')
        })

        scanServer.on('message', (data, rinfo) => {
          console.log('RECV==>', data.toString())

          if (data.toString().startsWith('SCAN_REPLY')) {
            // SCAN_REPLY|en0,f0:18:98:aa:65:a2,*************,fe80::141b:2fbe:6dab:98f1,8888
            let mm = data.toString().split('|')
            for (let i = 1; i < mm.length; i++) {
              let ll = data.toString().split(',')
              let key = ll[1]
              if (this.scannedGatewaysExisted[key] === undefined) {
                this.scannedGateways.push({
                  mac1: ll[1],
                  ip1: ll[2],
                  port1: parseInt(ll[4]),
                  mac2: ll[7] || '未知',
                  ip2: ll[8] || '未知',
                  port2: parseInt(ll[4]),
                })
                this.scannedGatewaysExisted[key] = true
              }
            }
          }
        })
        let clientSocket = dgram.createSocket({
          type: 'udp4',
          reuseAddr: true,
        })
        clientSocket.send('SCAN', deviceServerPort, deviceServerIP)

        let count = 3
        let timer = setInterval(() => {
          clientSocket.send('SCAN', deviceServerPort, deviceServerIP)
          count = count - 1
          if (count === 0) {
            clearInterval(timer)
            this.scanDlgVisible = false
            if (this.scannedGateways.length > 0) {
              this.showCreateDlg()
            } else {
              message.error('未发现相同网段内的采集主机')
            }
          }
        }, 1000)
      },
      handleNewModalOk: throttle(function () {
        if (this.checkGateway.status !== '在线') {
          message.error('网关离线，请先检测')
          return
        }
        this.syncToGW(this.checkGateway)
      }, 2000), // Throttle with a 2-second delay
    },
  }
</script>
<style scoped>
  .gatewayBox {
    display: flex;
    flex-wrap: wrap;
    height: calc(100vh - 320px);
    overflow: scroll;
  }

  .gatewayItem {
    margin-bottom: 20px;
    margin-right: 20px;
  }

  .flex {
    display: flex;
    align-items: center;
  }
</style>
