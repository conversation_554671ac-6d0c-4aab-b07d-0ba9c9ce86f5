<template>
  <div>
    <a-row>
      <a-col>
        <a-button type="primary" @click="clearLocationsMethod">
          清空地点
        </a-button>
        <a-button type="primary" style="margin-left: 20px" @click="import_data">
          导入
        </a-button>
        <a-button type="primary" style="margin-left: 20px" @click="downLoad">
          下载模板
        </a-button>
        <a-button type="primary" style="margin-left: 20px" @click="getList">
          刷新
        </a-button>
      </a-col>
      <a-col
        style="
          line-height: 28px;
          height: 28px;
          margin-left: 10px;
          font-weight: 200;
          font-size: 18px;
        "
      >
        固定为十级地点信息: 国家 / 省 / 市县区 / 园区 / 楼栋 / 楼层 / 机房 /
        行列号 / 机架 / U位
      </a-col>
    </a-row>
    <div class="treeBox">
      <a-tree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :showLine="true"
        :show-icon="true"
        :autoExpandParent="autoExpandParent"
        defaultExpandAll
        :fieldNames="{
          title: 'addressName',
          key: 'addressCode',
          children: 'children',
          id: 'addressCode',
          dataRef: 'dataRef',
        }"
      >
        <template
          #title="{
            key: treeKey,
            addressName,
            children,
            levelName,
            dataRef,
            data,
          }"
        >
          <span style="color: red">({{ treeKey }}{{ levelName }})</span>
          <span>{{ addressName }}</span>
          <span class="but_type">
            <a-button
              v-if="dataRef.addressType !== '9'"
              size="small"
              type="link"
              @click="showModal(dataRef, data)"
            >
              新增
            </a-button>
            <a-button size="small" type="link" @click="edit(dataRef, data)">
              编辑
            </a-button>
            <a-button size="small" type="link" danger @click="remove(dataRef)">
              删除
            </a-button>
          </span>
        </template>
      </a-tree>
    </div>
    <a-modal v-model:visible="visible" :title="addTitle" @ok="handleOk">
      <a-form
        name="custom-validation"
        ref="formRef"
        :model="formState"
        :rules="rules"
        v-bind="layout"
      >
        <a-form-item has-feedback label="地点名称" name="title">
          <a-input v-model:value="formState.title" autocomplete="off" />
        </a-form-item>
        <a-form-item has-feedback label="地点图片" name="adsPic">
          <Upload
            :imageObj="formState.adsPic"
            @imageUpChange="adsPicChange"
          ></Upload>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script>
  var XLSX = require('xlsx')
  import Upload from '../../../components/upload.vue'
  import { defineComponent, ref, reactive, onMounted } from 'vue'
  import { message } from 'ant-design-vue'
  import {
    addLocations,
    locationsTree,
    clearLocations,
    locationEdit,
    locationsClear,
  } from '../../../api/projects'
  import { uuid } from '../../../utils'
  import axios from 'axios'
  export default defineComponent({
    components: { Upload },
    props: {
      detailId: String,
      code: String,
    },
    setup(props, ctx) {
      const autoExpandParent = ref(true)
      const import_data = () => {
        let input = document.createElement('input')
        input.type = 'file'
        input.click()
        input.onchange = ($event) => {
          parse_imported_data($event)
        }
      }
      const downLoad = () => {
        axios
          .get('static/address.xlsx', {
            //静态资源文件夹public
            responseType: 'blob',
          })
          .then((response) => {
            const url = window.URL.createObjectURL(new Blob([response.data]))
            const link = document.createElement('a')
            let fname = '地点模板.xlsx'
            link.href = url
            link.setAttribute('download', fname)
            document.body.appendChild(link)
            link.click()
          })
          .catch((error) => {
            console.log('error:' + JSON.stringify(error))
          })
      }
      const layout = {
        labelCol: {
          span: 4,
        },
        wrapperCol: {
          span: 14,
        },
      }
      const clearLocationsMethod = () => {
        locationsClear(props.detailId).then((res) => {
          treeData.value = []
        })
      }
      const parse_imported_data = (e) => {
        // 读取表格文件
        let fileName = ''
        const files = e.target.files
        if (files.length <= 0) {
          return false
        } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
          message.error('上传格式不正确，请上传xls或者xlsx格式')
          return false
        } else {
          // 更新获取文件名
          fileName = files[0].name
        }
        const fileReader = new FileReader()
        fileReader.onload = async (ev) => {
          try {
            let workbook = XLSX.read(ev.target.result, {
              type: 'array',
            })
            let ws = XLSX.utils.sheet_to_json(workbook.Sheets.Sheet1, {})
            await addLocations({ projectId: props.detailId, locations: ws })
            message.success('导入成功')
            getList()
          } catch (error) {
            console.log(error)
          }
        }
        fileReader.readAsArrayBuffer(files[0])
      }
      // 添加导入的地址信息
      const addAddressItem = async (name, parentId, type = 1) => {
        if (name) {
          let ret = ''
          let isExisted = await isAddressExisted(name, parentId, props.code)
          if (isExisted.count == 0) {
            let key = `WZ${uuid()}`
            const params = {
              title: name,
              key: key,
              children: [],
              parentId,
              projectCode: props.code,
              type,
            }
            await createAddress(params)
            ret = key
          } else {
            ret = isExisted.results[0].attributes.key
          }
          return ret
        }
      }
      const expandedKeys = ref([])
      const treeData = ref([])
      // 获取地址一览
      const getList = () => {
        locationsTree(props.detailId).then((res) => {
          treeData.value = res
        })
      }
      onMounted(() => {
        getList()
      })
      const adsPicChange = (obj) => {
        formState.adsPic = obj
      }
      const setModelName = (dataRef, showFlag = false) => {
        let treeType = null
        if (showFlag) {
          treeType = dataRef.type - 1
        } else {
          treeType = dataRef.type
        }
        let retName = ''
        switch (treeType) {
          case 1:
            retName = '省'
            break
          case 2:
            retName = '市县区'
            break
          case 3:
            retName = '园区'
            break
          case 4:
            retName = '楼栋'
            break
          case 5:
            retName = '楼层'
            break
          case 6:
            retName = '机房'
            break
          case 7:
            retName = '行列号'
            break
          case 8:
            retName = '机架'
            break
          case 9:
            retName = 'U位'
            break
          default:
            retName = '国家'
            break
        }
        return retName
      }
      const modelLnfo = ref({})
      const level = ref()
      const changed_treeData = ref()
      const addTitle = ref()
      const addTyle = ref(1)
      const rules = {
        title: [
          {
            required: true,
            message: '请输入地点名称',
            trigger: 'blur',
          },
        ],
      }
      const formRef = ref()
      const formState = reactive({
        title: '',
        adsPic: {},
      })
      const visible = ref(false)
      const showModal = (dataRef = '', data) => {
        addTitle.value = `新增${dataRef.levelName}`
        addTyle.value = 2
        formState.title = ''
        formState.adsPic = {}
        if (data) {
          modelLnfo.value = data
        }
        visible.value = true
      }

      const handleOk = (e) => {
        formRef.value
          .validate()
          .then(() => {
            let params = {}
            if (addTyle.value === 1) {
              addTop()
            } else if (addTyle.value === 2) {
              let addressType = parseInt(modelLnfo.value.addressType) + 1
              params = {
                remarks: '新增',
                addressName: formState.title,
                parentCode: modelLnfo.value.addressCode,
                addressType: `${addressType}`,
                adsPic: formState.adsPic,
              }
              locationEdit(props.detailId, params).then((res) => {
                getList()
                resetForm()
              })
            } else {
              params = {
                addressCode: changed_treeData.value.addressCode,
                remarks: '编辑',
                addressName: formState.title,
                adsPic: formState.adsPic,
              }
              console.log(params)
              locationEdit(props.detailId, params).then((res) => {
                getList()
                resetForm()
              })
            }
            autoExpandParent.value = true
            visible.value = false
          })
          .catch((error) => {})
      }
      const resetForm = () => {
        formRef.value.resetFields()
      }
      const addTop = () => {
        let key = `WZ${uuid()}`
        const params = {
          title: formState.title,
          key: key,
          children: [],
          parentId: '0',
          projectCode: props.code,
          type: 1,
          adsPic: formState.adsPic,
        }
        createAddress(params).then((res) => {
          getList()
          resetForm()
        })
        //添加
      }
      const remove = (dataRef) => {
        clearLocations(props.detailId, {
          addressCode: dataRef.addressCode,
        }).then((res) => {
          getList()
        })
      }
      const edit = (dataRef, data) => {
        changed_treeData.value = dataRef
        addTitle.value = '编辑' + dataRef.levelName
        addTyle.value = 3
        formState.title = dataRef.addressName
        formState.adsPic = dataRef.adsPic ? dataRef.adsPic : {}
        visible.value = true
      }
      return {
        modelLnfo,
        level,
        changed_treeData,
        getList,
        addTyle,
        resetForm,
        rules,
        formState,
        formRef,
        visible,
        showModal,
        handleOk,
        treeData,
        expandedKeys,
        remove,
        edit,
        addTop,
        addTitle,
        setModelName,
        import_data,
        parse_imported_data,
        addAddressItem,
        adsPicChange,
        downLoad,
        autoExpandParent,
        layout,
        clearLocationsMethod,
      }
    },
  })
</script>
<style scoped lang="less">
  .treeBox {
    height: calc(100vh - 290px);
    overflow: scroll;
  }
</style>
