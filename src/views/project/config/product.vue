<template>
  <div class="gutter-example">
    <a-button type="primary" style="margin-right: 20px; margin-bottom: 10px" @click="addProduct">
      添加产品
    </a-button>
    <a-button type="primary" style="margin-right: 20px; margin-bottom: 10px" @click="getProjectProductListMth">
      刷新
    </a-button>
    <div>
      <a-table :columns="columns" :data-source="dataSource" bordered :pagination="false" :scroll="{ y: scrollHight }"
        :size="'small'" row-key="projectProductId">
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'serialNumber'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <!-- <span><a @click="showModal(record)">选择详情页</a></span> -->
              <a style="color: #ff7875" @click="onDelete(record)">删除</a>
              <a @click="syncDeviceParam(record)">同步</a>
              <a @click="updateProduct(record)">更新产品</a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange"
          @pageChange="pageChange" />
      </div>
      <a-modal v-model:visible="visible" title="选择详情页" @ok="handleOk">
        详情页名称：
        <a-select ref="select" v-model:value="detailPage" style="width: 100%" @focus="focus" @change="handleChange">
          <template v-for="(item, index) in selectList" :key="index">
            <a-select-option :value="item.keyId">
              {{ item.title }}
            </a-select-option>
          </template>
        </a-select>
      </a-modal>
      <!-- 更新产品的记录 -->
      <a-modal v-model:visible="showLogs" title="产品信息更新" :footer="null">
        <div style="
            height: 50vh;
            overflow: scroll;
            background: white;
            padding: 5px;
          " id="log">
          <template v-for="(item, index) in logs" :key="index">
            <p>{{ item }}</p>
          </template>
        </div>
      </a-modal>
      <a-modal v-model:visible="addProductVisible" title="添加产品" @ok="handleAddProduct" width="1200px">
        <a-form :model="searchForm" layout="inline">
          <a-form-item label="系统名称">
            <a-select v-model:value="searchForm.system" :options="systemOptions" @change="onSystemChange"
              :fieldNames="{ label: 'name', value: 'code' }" style="width: 200px" allowClear></a-select>
          </a-form-item>
          <a-form-item label="设备类型">
            <a-input v-model:value="searchForm.deviceType" style="width: 200px" allowClear></a-input>
          </a-form-item>
          <a-form-item label="品牌">
            <a-input v-model:value="searchForm.brand" style="width: 200px" allowClear></a-input>
          </a-form-item>
          <a-form-item label="型号">
            <a-input v-model:value="searchForm.model" style="width: 200px" allowClear></a-input>
          </a-form-item>
          <a-button type="primary" @click="onSearch">查询</a-button>
        </a-form>
        <a-table :row-selection="rowSelection" :columns="productColumns" :data-source="filteredProducts" row-key="id"
          style="margin-top: 20px" :scroll="{ y: 500 }" :pagination="false"></a-table>
        <div class="pagination">
          <Pagination :page="modelPage" :size="modelSize" :total="modelTotal" @pageSizeChange="modelPageSizeChange"
            @pageChange="modelPageChange" />
        </div>
      </a-modal>
      <a-modal v-model:visible="selectDeviceVisible" title="选择设备" @ok="handleSelectDevice" width="50%" okText="同步"
        cancelText="关闭">
        <p style="color: #ff7875">
          Tip: 选择设备后，点击同步按钮，即可同步设备参数，会根据参数Code匹配，没有就新增，有就忽略
        </p>
        <a-form :model="deviceSearchForm" layout="inline" style="margin-bottom: 16px">
          <a-form-item label="网关编号">
            <a-input v-model:value="deviceSearchForm.gatewayCode" placeholder="请输入网关编号" allowClear />
          </a-form-item>
          <a-form-item label="设备编号">
            <a-input v-model:value="deviceSearchForm.deviceCode" placeholder="请输入设备编号" allowClear />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="searchDevices">查询</a-button>
            <a-button type="primary" @click="onOnekeyUpdate" style="margin-left: 10px">根据设备型号一键更新</a-button>
          </a-form-item>
        </a-form>

        <a-table :columns="deviceColumns" :data-source="deviceList" :pagination="false" row-key="id"
          :scroll="{ y: 400 }" :row-selection="{
            type: 'radio',
            selectedRowKeys: selectedDeviceKeys,
            onChange: onDeviceSelect,
          }">
          <template #bodyCell="{ column, text, record, index }">
            <template v-if="column.dataIndex === 'serialNumber'">
              {{ index + 1 }}
            </template>
          </template>
        </a-table>

        <div class="pagination">
          <Pagination :page="devicePage" :size="deviceSize" :total="deviceTotal" @pageSizeChange="devicePageSizeChange"
            @pageChange="devicePageChange" />
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import Pagination from '../../../components/pagination.vue'
import { message, Modal, notification } from 'ant-design-vue'
import { defineComponent, reactive, ref, onMounted, watch } from 'vue'
import { getlist, doBatch } from '../../../api/dataDeal.js'
import {
  addProjectProducts,
  getProjectProducts,
  getAttributesByProjectProductId,
  syncProductAttributes,
  updateProjectProducts,
  syncProductAttributesByModelName
} from '../../../api/projects.js'
const columns = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
    width: '50px',
  },
  {
    title: '所属系统',
    dataIndex: 'sysName',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceTypeName',
  },
  {
    title: '所属品牌',
    dataIndex: 'brandName',
  },
  {
    title: '型号名称',
    dataIndex: 'modelName',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
  },
  {
    title: '已绑详情页名称',
    dataIndex: 'detailPageName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '180px',
  },
]
export default defineComponent({
  props: {
    detailId: String,
    code: String,
  },
  components: { Pagination },
  setup(props, ctx) {
    const rowSelection = ref({
      selectedRowKeys: [],
      onChange: (selectedRowKeys, selectedRows) => {
        rowSelection.value.selectedRowKeys = selectedRowKeys
        console.log('selectedRowKeys', rowSelection.value.selectedRowKeys)
      },
    })
    const addProductVisible = ref(false)
    const addProduct = () => {
      onSearch()
      addProductVisible.value = true
    }
    const modelPage = ref(1)
    const modelSize = ref(10)
    const modelTotal = ref(0)
    const modelPageSizeChange = (page, size) => {
      modelPage.value = page
      modelSize.value = size
    }
    const modelPageChange = (page) => {
      console.log(page)
      modelPage.value = page
      onSearch()
    }



    const dataSource = ref()




    const syncDeviceParam = (record) => {
      currentProductId.value = record.projectProductId
      productId.value = record.productId
      modelName.value = record.modelName
      deviceSearchForm.gatewayCode = ''
      deviceSearchForm.deviceCode = ''
      searchDevices()
      selectedDeviceKeys.value = []
      selectedDevice.value = null
      selectDeviceVisible.value = true
    }
    const onDelete = (record) => {
      console.log(record)
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除该产品吗？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          doBatch('project_products', {
            ids: [record.projectProductId],
          }).then((res) => {
            message.success('删除成功')
            getProjectProductListMth()
          })
        },
      })
    }

    const systemOptions = ref()
    const scrollHight = ref(0)
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 320
      getlist('systems', { page: 1, limit: 10000 }).then((res) => {
        systemOptions.value = res.data
      })
      getProjectProductListMth()
    })
    const deviceTypeOptions = ref()
    const systemSelected = (value) => {
      getDeviceType({
        system: value,
      }).then((res) => {
        let data = []
        let list = res
        for (let item of list) {
          data.push({
            label: item.attributes.name,
            value: item.attributes.name,
          })
        }
        deviceTypeOptions.value = data
      })
    }
    const brandOptions = ref()
    const deviceTypeSelected = (value) => {
      getBrandList({
        deviceType: value,
      }).then((res) => {
        let data = []
        let list = res
        for (let item of list) {
          data.push({
            label: item.attributes.name,
            value: item.attributes.name,
          })
        }
        brandOptions.value = data
      })
    }
    const modelOptions = ref()
    const productOptions = ref()
    const productInfo = ref({})
    const updateProduct = (record) => {
      updateProjectProducts(record.projectProductId).then((res) => {
        if (res.data) {
          getProjectProductListMth()
        }
      })
    }
    const brandSelected = (value) => {
      getModelList({ brand: value, deviceType: formState.deviceType }).then(
        (res) => {
          let data = []
          let list = res
          for (let item of list) {
            data.push({
              label: item.attributes.name,
              value: item.attributes.name,
            })
          }
          modelOptions.value = data
        }
      )
    }
    const productSelected = (value) => {
      for (const item of productOptions.value) {
        if (item.value == value) {
          formState.name = item.label
          formState.rules = item.rules
        }
      }
    }
    const total = ref(0)
    const formState = reactive({
      system: '',
      deviceType: '',
      brand: '',
      model: '',
      name: '',
      protocolType: 'Modbus_RTU',
      rules: [],
    })
    const protocolTypeOptions = ref([
      {
        value: 'Modbus_RTU',
      },
      {
        value: 'Modbus_TCP',
      },
      {
        value: 'Http',
      },
      {
        value: 'BacNel',
      },
      {
        value: 'Snmp',
      },
    ])
    const rules = {
      name: [
        {
          required: true,
          message: '请输入产品名称',
          trigger: 'blur',
        },
      ],
      system: [
        {
          required: true,
          message: '请选择所属系统',
          trigger: 'change',
        },
      ],
      brand: [
        {
          required: true,
          message: '请选择所属品牌',
          trigger: 'change',
        },
      ],
      deviceType: [
        {
          required: true,
          message: '请选择设备类型',
          trigger: 'change',
        },
      ],
      model: [
        {
          required: true,
          message: '请选择设备型号',
          trigger: 'change',
        },
      ],
    }
    const getProjectProductListMth = () => {
      getProjectProducts(props.detailId, {
        limit: size.value,
        page: page.value,
      }).then((res) => {
        const { data, pagination } = res
        dataSource.value = data
        total.value = pagination.total
      })
    }
    const page = ref(1)
    const size = ref(10)
    const pageChange = (Page) => {
      page.value = Page
      getProjectProductListMth()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
    }


    const searchForm = reactive({
      system: '',
      deviceType: '',
      brand: '',
      model: '',
    })
    const filteredProducts = ref([])
    const productColumns = [
      { title: '系统名称', dataIndex: 'sysName' },
      { title: '设备类型', dataIndex: 'deviceTypeName' },
      { title: '品牌', dataIndex: 'brandName' },
      { title: '型号', dataIndex: 'modelName' },
    ]
    const onOnekeyUpdate = () => {
      syncProductAttributesByModelName({
        productId: productId.value,
        modelName: modelName.value,
        projectId: props.detailId
      }).then((res) => {
        console.log(res)
        notification.success({
          message: '同步成功',
          description:
            res.message,
          placement: 'topRight',
          duration: null
        });
        if (res.data) {
          getProjectProductListMth()
        }
      }).catch((err) => {
        notification.error({
          message: '同步失败',
          description:
            err.message,
          placement: 'topRight',
          duration: null
        });
      })
    }

    const onSearch = () => {
      let where = {}
      if (searchForm.system) {
        where.sysCode = searchForm.system
      }
      if (searchForm.deviceType) {
        where.deviceTypeName = searchForm.deviceType
      }
      if (searchForm.brand) {
        where.brandName = searchForm.brand
      }
      if (searchForm.model) {
        where.modelName = searchForm.model
      }
      getlist('products', {
        page: modelPage.value,
        limit: modelSize.value,
        where,
      }).then((res) => {
        filteredProducts.value = res.data
        modelTotal.value = res.pagination.total
      })
    }

    const handleAddProduct = () => {
      if (rowSelection.value.selectedRowKeys.length === 0) {
        message.warning('请选择产品')
      } else {
        addProjectProducts({
          projectId: props.detailId,
          productIds: rowSelection.value.selectedRowKeys,
        }).then((res) => {
          rowSelection.value.selectedRowKeys = []
          addProductVisible.value = false
          getProjectProductListMth()
        })
      }
    }



    // 添加新的响应式变量
    const selectDeviceVisible = ref(false)
    const deviceList = ref([])
    const selectedDeviceKeys = ref([])
    const selectedDevice = ref(null)

    // 添加设备列表的列定义
    const deviceColumns = [
      {
        title: '序号',
        dataIndex: 'serialNumber',
        width: '80px',
      },
      {
        title: '网关编码',
        dataIndex: 'gatewayCode',
        width: '100px',
      },
      {
        title: '设备编码',
        dataIndex: 'deviceCode',
        width: '160px',
      },
      {
        title: '设备名称',
        dataIndex: 'deviceName',
      },
    ]

    // 处理设备选择
    const onDeviceSelect = (selectedRowKeys, selectedRows) => {
      selectedDeviceKeys.value = selectedRowKeys
      selectedDevice.value = selectedRows[0]
      console.log('selectedDevice', selectedDevice.value)
    }

    // 处理确认选择
    const handleSelectDevice = () => {
      if (!selectedDevice.value) {
        message.warning('请选择设备')
        return
      }
      syncProductAttributes({
        productId: productId.value,
        deviceId: selectedDevice.value.id,
      }).then((res) => {
        message.success('同步成功')
        selectDeviceVisible.value = false
      })
    }

    // 添加设备搜索相关的响应式变量
    const deviceSearchForm = reactive({
      gatewayCode: '',
      deviceCode: ''
    })

    const devicePage = ref(1)
    const deviceSize = ref(10)
    const deviceTotal = ref(0)

    // 搜索设备
    const searchDevices = () => {
      getAttributesByProjectProductId(currentProductId.value, {
        page: devicePage.value,
        limit: deviceSize.value,
        gatewayCode: deviceSearchForm.gatewayCode,
        deviceCode: deviceSearchForm.deviceCode
      }).then((res) => {
        const { data, pagination } = res
        deviceList.value = data
        deviceTotal.value = pagination.total
      })
    }

    // 设备分页改变
    const devicePageChange = (page) => {
      devicePage.value = page
      searchDevices()
    }

    // 设备每页条数改变
    const devicePageSizeChange = (page, size) => {
      devicePage.value = page
      deviceSize.value = size
    }

    // 修改 syncDeviceParam 函数
    const currentProductId = ref(null)
    const productId = ref(null)
    const modelName = ref(null)
    return {
      productId,
      modelName,
      updateProduct,
      syncDeviceParam,
      onDelete,
      formState,
      rules,
      protocolTypeOptions,
      systemOptions,
      deviceTypeOptions,
      systemSelected,
      brandOptions,
      deviceTypeSelected,
      modelOptions,
      brandSelected,
      dataSource,
      total,
      pageChange,
      pageSizeChange,
      columns,
      scrollHight,
      onOnekeyUpdate,
      addProduct,
      addProductVisible,
      searchForm,
      filteredProducts,
      productColumns,
      onSearch,
      handleAddProduct,
      modelPage,
      modelSize,
      modelTotal,
      modelPageSizeChange,
      modelPageChange,
      rowSelection,
      getProjectProductListMth,
      selectDeviceVisible,
      deviceList,
      deviceColumns,
      selectedDeviceKeys,
      handleSelectDevice,
      onDeviceSelect,
      deviceSearchForm,
      searchDevices,
      devicePage,
      deviceSize,
      deviceTotal,
      devicePageChange,
      devicePageSizeChange,
    }
  },
})
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}
</style>
