<template>
  <div>
    <div class="outerLayer">
      <a-form ref="formRef"  :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol"
        @finish="save">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item ref="name" label="工程名称:" name="name">
              <a-input v-model:value="formState.name" placeholder="请输入工程名称" :maxlength="10" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item ref="name" label="工程代码:" name="code">
              <a-input v-model:value="formState.code" :disabled="true" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="所属客户:" name="customer">
              <a-input v-model:value="formState.customer" placeholder="请输入所属客户" :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目规格:" name="scale">
              <a-select v-model:value="formState.scale" :options="options"></a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="通讯地址:" name="commAddress">
              <a-textarea v-model:value="formState.commAddress" showCount :maxlength="100" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="参与人员:" name="workerIds">
              <a-select v-model:value="formState.workerIds" mode="multiple" placeholder="请输入参与人员"
                style="width: 100%" :options="data" :fieldNames="{ label: 'username', value: 'id' }"></a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="设备总数量:" name="deviceCount">
              <span style="color: red;font-weight: bold;font-size: 30px;line-height: 30px;">{{ formState.deviceCount }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="设备完成数量:" name="completionDevice">
              <span style="color: green;font-weight: bold;font-size: 30px;line-height: 30px;">{{ formState.completionDevice }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="采集主机数量:" name="gatewaysLen">
              <span style="color: darkcyan;font-weight: bold;font-size: 30px;line-height: 30px;">{{ formState.gatewaysLen }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
          <a-button style="margin-right: 50px" @click="getInfo">刷新</a-button>
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
import { getProjectInfo, editProject, getDeviceStats } from '../../../api/projects'
import { getlist } from '../../../api/dataDeal'
import { defineComponent, ref, reactive, onMounted, toRefs } from 'vue'
const rules = {
  name: [
    {
      required: true,
      message: '请输入工程名称',
      trigger: 'blur',
    },
  ],
}
const options = ref([
  {
    value: '一级',
    label: '一级',
  },
  {
    value: '二级',
    label: '二级',
  },
  {
    value: '三级',
    label: '三级',
  },
  {
    value: '四级',
    label: '四级',
  },
  {
    value: '五级',
    label: '五级',
  },
])
import { message } from 'ant-design-vue'
export default defineComponent({
  props: {
    detailId: String,
    mainInfo: Object
  },
  setup(props, ctx) {
    const formState = reactive({
      name: '',
      customer: '',
      scale: '',
      workerIds: [],
      commAddress: '',
      code: '',
      deviceCount: 0
    })
    const state = reactive({
      data: []
    })
    onMounted(() => {
      fetchUser()
    })
    const getDeviceNum = () => {
      getDeviceStats({ projectId:props.detailId }).then((res) => {
        const { data } = res
        formState.deviceCount = data.totalDevices
        formState.completionDevice = data.completedDevices
        formState.gatewaysLen = data.collectionHosts
      })
    }


    const fetchUser = async (value) => {
      state.data = []
      const { data } = await getlist('users', { page: 1, limit: 1000 })
      state.data = data
      formState.code = props.mainInfo.code
      formState.name = props.mainInfo.name
      formState.customer = props.mainInfo.customer
      formState.scale = props.mainInfo.scale
      formState.commAddress = props.mainInfo.commAddress
      formState.workerIds = props.mainInfo.workerIds
      getDeviceNum()
    }
    const getInfo = () => {
      getProjectInfo(props.detailId).then((res) => {
        for (let keyPar in formState) {
          formState[keyPar] = res[keyPar]
        }
        message.success('刷新成功')
        getDeviceNum()
      })
    }
    const save = () => {
      editProject(props.detailId, formState).then((res) => {
        message.success('修改成功')
      })
    }
    return {
      formState,
      rules,
      labelCol: {
        style: { width: '100px' },
      },
      wrapperCol: {
        span: 15,
      },
      options,
      save,
      getInfo,
      ...toRefs(state),
      fetchUser
    }
  },
})
</script>

<style scoped>
.outerLayer {
  margin: auto;
  max-width: 1000px;
  margin-top: 60px;
}
</style>
