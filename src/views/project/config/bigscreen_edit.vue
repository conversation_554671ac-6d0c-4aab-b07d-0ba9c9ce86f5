<template>
  <div>
    <a-button type="primary" @click="toAddScreen">添加大屏</a-button>
    <a-checkbox-group
      v-model:value="selectedScreenIds"
      @change="onSelectedScreenChange"
    >
      <a-row :gutter="10">
        <a-col
          :span="6"
          :key="'screen_' + idx"
          v-for="(screen, idx) in screenOptions"
        >
          <div @click="toEditScreen(screen.id)">
            <img
              :src="screen.backgroundUrl"
              style="width: 100%"
              title="点击编辑大屏"
            />
          </div>
          <div style="text-align: center">
            <a-checkbox :value="screen.id">
              {{ screen.title }}
            </a-checkbox>
          </div>
        </a-col>
      </a-row>
    </a-checkbox-group>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    data() {
      const httpapi = axios.create({
        baseURL: 'http://*************:8050',
      })

      return {
        httpapi: httpapi,
        project: undefined,
        categoryOptions: [],
        screenOptions: [],
        selectedScreenIds: [],
      }
    },
    async mounted() {
      await this.getProject()
      await this.loadCategory()
    },
    methods: {
      async getProject() {
        let projectId = this.$route.query.key
        let project = await viewProject(projectId) // TODO
        this.project = { ...project.attributes }
        console.log(this.project.screens)
        this.selectedScreenIds = this.project.screens
      },
      async loadCategory() {
        const resp = await this.httpapi.get('/blade-visual/category/list')
        for (let i = 0; i < resp.data.data.length; i++) {
          if (resp.data.data[i].categoryKey === this.project.name) {
            this.loadScreens(resp.data.data[i].categoryValue)
            return
          }
        }
      },
      async loadScreens(category) {
        const resp = await this.httpapi.get(
          `/blade-visual/visual/list?category=${category}&current=1&size=100`
        )
        console.log(resp.data.data)
        this.screenOptions = resp.data.data.records
      },
      onSelectedScreenChange() {
        console.log('onSelectedScreenChange')
        this.project.screens = this.selectedScreenIds
        updateProject(this.$route.query.key, this.project)
      },
      toEditScreen(screenId) {
        let url = `http://*************:8099/build/${screenId}`
        if (window.require) {
          const shell = window.require('electron').shell
          shell.openExternal(url)
        } else {
          window.open(url, '_blank')
        }
      },
      toAddScreen() {
        let url = `http://*************:8099`
        if (window.require) {
          const shell = window.require('electron').shell
          shell.openExternal(url)
        } else {
          window.open(url, '_blank')
        }
      },
    },
  }
</script>
