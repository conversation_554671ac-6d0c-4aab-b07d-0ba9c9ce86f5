<template>
  <div>
    <div>
      项目名称: {{ mainInfo?.name || '' }}
      项目代码: {{ mainInfo?.code || '' }}
    </div>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane v-for="item in list" :key="item.key" :tab="item.name">
        <template v-if="item.key == 1">
          <basic v-if="mainInfo" :detailId="detailId" :mainInfo="mainInfo"></basic>
        </template>
        <template v-else-if="item.key == 2">
          <myAddress :detailId="detailId"></myAddress>
        </template>
        <template v-else-if="item.key == 3">
          <product :detailId="detailId"></product>
        </template>
        <template v-else-if="item.key == 4">
          <gateway_mgmnt :detailId="detailId" :mainInfo="mainInfo"/>
        </template>
        <!-- <template v-else-if="item.key == 5">
          <info_sync />
        </template> -->
        <template v-else-if="item.key == 6">
          <bigscreen_edit />
        </template>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
  import basic from './config/basic.vue'
  import product from './config/product.vue'
  import myAddress from './config/address.vue'
  import gateway_mgmnt from './config/gateway_mgmnt.vue'
  import info_sync from './config/info_sync.vue'
  import bigscreen_edit from './config/bigscreen_edit.vue'
  import { getProjectInfo } from '../../api/projects'
  export default {
    components: {
      basic,
      product,
      myAddress,
      info_sync,
      gateway_mgmnt,
      bigscreen_edit,
    },
    created() {
      let key = this.$route.query.key
      this.detailId = key
      const activeKey = this.$route.query.activeKey
      if(activeKey) {
        this.activeKey = activeKey
      }
      this.projectInfo()
    },
    data() {
      return {
        code: '',
        detailId: '',
        mainInfo: null,
        activeKey: '1',
        list: [
          {
            name: '基础信息',
            key: '1',
          },
          {
            name: '地点管理',
            key: '2',
          },
          {
            name: '产品信息',
            key: '3',
          },
          {
            name: '采集主机管理',
            key: '4',
          },
          // {
          //   name: '同步工程',
          //   key: '5',
          // },
          {
            name: '大屏编辑',
            key: '6',
          },
        ],
      }
    },
    methods: {
      async projectInfo() {
        let res = await getProjectInfo(this.detailId)
        this.mainInfo = res
      }
    }
  }
</script>
