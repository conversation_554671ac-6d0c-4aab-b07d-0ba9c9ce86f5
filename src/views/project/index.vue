<template>
  <div>
    <div>
      工程名称：
      <a-input v-model:value.trim="value" style="width: 200px" allowClear />
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <a-button
        type="primary"
        style="margin: 0 10px 0 50px"
        @click="showModal(1, '')"
      >
        新建
      </a-button>
      <a-button type="primary" danger @click="showDeleteConfirm('')">
        删除
      </a-button>
    </div>
    <div style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        bordered
        :row-selection="rowSelection"
        :pagination="false"
        :scroll="{ y: 550 }"
        :size="'small'"
        row-key="id"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <a type="link" @click="openPage(1, record)">编辑</a>
              <a type="link" @click="openPage(2, record)">调试</a>
              <a
                type="link"
                style="color: #ff4d4f"
                danger
                @click="showDeleteConfirm(record)"
              >
                删除
              </a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination
          :page="page"
          :size="size"
          :total="total"
          @pageSizeChange="pageSizeChange"
          @pageChange="pageChange"
        />
      </div>
      <a-modal
        v-model:visible="visible"
        :footer="null"
        title="新建工程"
        @cancel="cancel"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-form-item ref="name" label="工程名称:" name="name">
            <a-input
              v-model:value="formState.name"
              placeholder="请输入工程名称"
              :maxlength="10"
            />
          </a-form-item>
          <a-form-item ref="name" label="工程代码:" name="code">
            <a-input
              v-model:value="formState.code"
              placeholder="请输入工程代码"
              :maxlength="10"
              @change="codeChange"
            />
          </a-form-item>
          <a-form-item label="所属客户:" name="customer">
            <a-input
              v-model:value="formState.customer"
              placeholder="请输入所属客户"
              :maxlength="50"
            />
          </a-form-item>
          <a-form-item label="参与人员:" name="workerIds">
            <a-select
              v-model:value="formState.workerIds"
              mode="multiple"
              :fieldNames="{ label: 'username', value: 'id' }"
              placeholder="请输入参与人员"
              style="width: 100%"
              :options="data"
            ></a-select>
          </a-form-item>
          <a-form-item label="项目规格:" name="scale">
            <a-select
              v-model:value="formState.scale"
              :options="options"
            ></a-select>
          </a-form-item>
          <a-form-item label="通讯地址:" name="commAddress">
            <a-textarea
              v-model:value="formState.commAddress"
              showCount
              :maxlength="100"
            />
          </a-form-item>
        </a-form>
        <div class="modalBtn">
          <template v-if="modalType === 1">
            <a-button type="primary" @click="onSubmit(1)">保存并新建</a-button>
          </template>
          <a-button type="primary" @click="onSubmit(2)">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { getProjectList, projectAdd, deleteProject } from '../../api/projects'
import { getlist } from '../../api/dataDeal'
import Pagination from '../../components/pagination'
import {
  defineComponent,
  ref,
  reactive,
  createVNode,
  onMounted,
  toRefs,
} from 'vue'
import { Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
const columns = [
  {
    title: '工程名称',
    dataIndex: 'name',
    width: '20%',
  },
  {
    title: '工程代码',
    dataIndex: 'code',
  },

  {
    title: '所属客户',
    dataIndex: 'customer',
  },
  {
    title: '参与人员',
    dataIndex: 'workerNames',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
  },
]
const formRef = ref()

const checkedCode = async (_rule, value) => {
  let reg = /^[A-Za-z]+$/
  if (value === '') {
    return Promise.reject('请输入工程代码')
  } else if (!reg.test(value)) {
    return Promise.reject('工程代码必须是字母')
  } else {
    return Promise.resolve()
  }
}
const rules = {
  name: [
    {
      required: true,
      message: '请输入工程名称',
      trigger: 'blur',
    },
  ],
  customer: [
    {
      required: true,
      message: '请输入所属客户',
      trigger: 'blur',
    },
  ],
  workerIds: [
    {
      required: true,
      message: '请输入参与人员',
      trigger: 'change',
    },
  ],
  commAddress: [
    {
      required: true,
      message: '请输入通讯地址',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      validator: checkedCode,
      trigger: 'blur',
    },
  ],
}
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (RowKeys, selectedRows) => {
    console.log(RowKeys, selectedRows)
    selectedRowKeys.value = RowKeys
  },
  onSelect: (record, selected, selectedRows) => {},
  onSelectAll: (selected, selectedRows, changeRows) => {},
}
export default defineComponent({
  components: { Pagination },
  setup() {
    const store = useStore()
    const formState = reactive({
      name: '',
      customer: '',
      workerIds: [],
      scale: '一级',
      commAddress: '',
    })
    const state = reactive({
      data: [],
    })
    const fetchUser = async (value) => {
      state.data = []
      const { data } = await getlist('users', { page: 1, limit: 1000 })
      state.data = data
    }
    const router = useRouter()
    const options = ref([
      {
        value: '一级',
        label: '一级',
      },
      {
        value: '二级',
        label: '二级',
      },
      {
        value: '三级',
        label: '三级',
      },
      {
        value: '四级',
        label: '四级',
      },
      {
        value: '五级',
        label: '五级',
      },
    ])
    // 工程code自动大写
    const codeChange = (_e) => {
      formState.code = formState.code.toUpperCase()
    }
    const visible = ref(false)
    const id = ref('')
    const total = ref(0)
    const dataSource = ref([])
    const value = ref('')
    const page = ref(1)
    const size = ref(10)
    let modalType = ref(1)
    onMounted(() => {
      startGetList()
      fetchUser()
    })
    const getListByName = () => {
      page.value = 1
      startGetList()
    }
    const startDel = () => {
      deleteProject({ projectIds: selectedRowKeys.value }).then((res) => {
        selectedRowKeys.value = []
        startGetList()
      })
    }
    const dateFormat = (date) => {
      //日期格式化
      var d = new Date(date)
      let year = d.getFullYear()
      let month = d.getMonth() + 1
      month = '0' + month
      month = month.substring(month.length - 2)
      let day = '0' + d.getDate()
      day = day.substring(day.length - 2)
      let hour = '0' + d.getHours()
      hour = hour.substring(hour.length - 2)
      let m = '0' + d.getMinutes()
      m = m.substring(m.length - 2)
      let s = '0' + d.getSeconds()
      s = s.substring(s.length - 2)
      let format =
        year + '-' + month + '-' + day + ' ' + hour + ':' + m + ':' + s
      return format
    }
    const startGetList = () => {
      getProjectList({
        page: page.value,
        limit: size.value,
        search: value.value,
      }).then((res) => {
        const { data, pagination } = res
        dataSource.value = data
        total.value = pagination.total
      })
    }
    const startUpdateSystem = () => {
      updateSystem(id.value, formState).then((res) => {
        visible.value = false
        startGetList()
      })
    }
    const pageChange = (Page) => {
      page.value = Page
      startGetList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
      startGetList()
    }
    const showModal = (type, data) => {
      modalType.value = type
      if (type === 2) {
        formState.name = data.name
        formState.code = data.code
        id.value = data.key
      } else {
        formState.name = ''
        formState.code = ''
        formState.workerIds = []
        formState.scale = '一级'
        formState.commAddress = ''
      }
      visible.value = true
    }
    const handleOk = (e) => {
      visible.value = false
    }
    const cancel = () => {
      formRef.value.resetFields()
      visible.value = false
    }
    const onSubmit = (type) => {
      formRef.value
        .validate()
        .then(() => {
          projectAdd(formState).then((res) => {
            if (type == 2) {
              visible.value = false
            }
            formRef.value.resetFields()
            startGetList()
          })
        })
        .catch((error) => {})
    }
    const showDeleteConfirm = (data) => {
      if (data) {
        let idlist = []
        idlist.push(data.id)
        selectedRowKeys.value = idlist
      }
      Modal.confirm({
        title: '删除系统类型',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定删除选择的内容吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk() {
          startDel()
        },
        onCancel() {},
      })
    }
    const openPage = (type, data) => {
      let name = ''
      let query = {}
      switch (type) {
        case 1:
          name = 'ProjectEdit'
          query = {
            key: data.id
          }
          break
        case 2:
          name = 'Debugging'
          query = {
            key: data.key,
            code: data.code,
          }
          break
        default:
          break
      }
      router.push({ name, query })
    }
    return {
      selectedRowKeys,
      dataSource,
      columns,
      editingKey: '',
      value,
      rowSelection,
      visible,
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
      formRef,
      formState,
      rules,
      modalType,
      page,
      size,
      total,
      id,
      options,
      router,
      fetchUser,
      codeChange,
      openPage,
      cancel,
      showModal,
      handleOk,
      onSubmit,
      showDeleteConfirm,
      pageChange,
      pageSizeChange,
      startGetList,
      startUpdateSystem,
      startDel,
      getListByName,
      dateFormat,
      ...toRefs(state),
    }
  },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
  margin-right: 8px;
}
.modalBtn {
  text-align: right;
  margin-top: 30px;
  .ant-btn + .ant-btn {
    margin-left: 10px;
  }
}
/deep/.ant-input-search {
  max-width: 100%;
}
</style>
