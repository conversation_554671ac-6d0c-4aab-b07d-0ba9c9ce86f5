const dayjs = require('dayjs')
const lkutils = require('./lkutils.js')

let lkfuncs = {}

let vStr = ''
let vBuf = ''
let calcStartPos = 0
let calcLen = 0
let gDevice = undefined
let X

function _COMPARE_3_(e, x, y) {
  return e ? x : y
}

function _X_() {
  let pStr =
    calcLen > 0
      ? vStr.substr(calcStartPos - 1, calcLen)
      : vStr.substr(calcStartPos - 1)
  // console.log("_X_", calcStartPos, calcLen, pStr);

  let v = parseInt(pStr, 16)
  return v
}

function _SUBSTR_HEX_(x, y) {
  if (calcLen === 0) {
    calcLen = vStr.length
  }
  let pStr =
    calcLen > 0
      ? vStr.substr(calcStartPos - 1, calcLen)
      : vStr.substr(calcStartPos - 1)
  // console.log('vStr', vStr, 'pStr', pStr, pStr.substr(x - 1, y))
  return parseInt(pStr.substr(x - 1, y), 16)
}

function _SUBSTR_BIN_(x, y) {
  if (calcLen === 0) {
    calcLen = vStr.length
  }
  let pStr =
    calcLen > 0
      ? vStr.substr(calcStartPos - 1, calcLen)
      : vStr.substr(calcStartPos - 1)
  let bStr = lkutils.hexStringToBinaryString(pStr)
  return parseInt(bStr.substr(x - 1, y * 2), 2)
}

function _RAND_(min, max) {
  const r = Math.random() * (max - min) + min
  return r
}

function _GETVAR_(varName) {
  return gDevice.paramCodeValueMap[varName]
}

function _ROUND_(v, prec) {
  return (Math.round(v * Math.pow(10, prec)) * 1.0) / Math.pow(10, prec)
}

function _POW_(base, exponent) {
  return Math.pow(base, exponent)
}

function _FLOAT_(v) {
  var buffer = new ArrayBuffer(4)
  var bytes = new Uint8Array(buffer)
  bytes[0] = (v >> 0) & 0xff
  bytes[1] = (v >> 8) & 0xff
  bytes[2] = (v >> 16) & 0xff
  bytes[3] = (v >> 24) & 0xff
  let view = new DataView(buffer)
  return view.getFloat32(0, true)
}

function _FLOAT0_(v) {
  var buffer = new ArrayBuffer(4)
  var bytes = new Int8Array(buffer)
  bytes[0] = (v >> 24) & 0xff
  bytes[1] = (v >> 16) & 0xff
  bytes[2] = (v >> 8) & 0xff
  bytes[3] = v & 0xff
  let view = new DataView(buffer)
  return view.getFloat32(0, true)
}

function _FLOAT1_(v) {
  var buffer = new ArrayBuffer(4)
  var bytes = new Int8Array(buffer)
  bytes[0] = (v >> 8) & 0xff
  bytes[1] = v & 0xff
  bytes[2] = (v >> 24) & 0xff
  bytes[3] = (v >> 16) & 0xff
  let view = new DataView(buffer)
  return view.getFloat32(0, true)
}

function _FLOAT_DOLLAR_(v) {
  var buffer = new ArrayBuffer(4)
  var bytes = new Int8Array(buffer)
  bytes[0] = (v >> 16) & 0xff
  bytes[1] = (v >> 24) & 0xff
  bytes[2] = v & 0xff
  bytes[3] = (v >> 8) & 0xff
  let view = new DataView(buffer)
  return view.getFloat32(0, true)
}

function convertCalc(calc, buf, device) {
  calc = calc.toUpperCase()
  calc = calc.replace(/=/g, '===')
  calc = calc.replace(/LENGTH/g, buf.length)
  calc = calc.replace(/IF/g, '_COMPARE_3_')
  calc = calc.replace(/RAND/g, '_RAND_')
  calc = calc.replace(/A\$/g, '_SUBSTR_HEX_')
  calc = calc.replace(/AB\$/g, '_SUBSTR_BIN_')
  // calc = calc.replace(/AB\$/g, '_SUBSTR_BIN_')
  calc = calc.replace(/ROUND\(/g, '_ROUND_(')
  calc = calc.replace(/EYE\(/g, '_POW_(')
  calc = calc.replace(/FLOAT0\(/g, '_FLOAT0_(')
  calc = calc.replace(/FLOAT1\(/g, '_FLOAT1_(')
  calc = calc.replace(/FLOAT\$\(/g, '_FLOAT_DOLLAR_(')
  calc = calc.replace(/FLOAT\(/g, '_FLOAT_(')
  calc = calc.replace(/FOLAT\(/g, '_FLOAT_(')

  if (calc.indexOf('{') >= 0) {
    calc = calc.replace(/{/g, "_GETVAR_('")
    calc = calc.replace(/}/g, "')")
  }

  return calc
}

lkfuncs.parse = function (log, device, rules, nowRule, buf) {
  gDevice = device
  console.log('lkfuncs.parsexxxxx', device.protocolType, buf, vStr)
  // console.log(JSON.stringify(nowRule))
  if (vStr) {
    delete vStr
  }

  if (device.protocolType === 'SNMP' && nowRule.tx === buf.tx) {
    let X
    if (buf.type === 'NUMBER') {
      X = buf.value
      let v = eval(nowRule.dataCalib)
      for (let i = 0; i < rules.length; i++) {
        let rule = rules[i]
        if (rule.paramCode === nowRule.paramCode) {
          rule.calcResultValue = v
          return
        }
      }
    }
  }

  if (nowRule.paramDataType === 'CommStatus') {
    calcStartPos = nowRule.dataStartPos
    vStr = lkutils.bytesToHex(buf)
    vBuf = buf
    // log(vStr)

    let calc = convertCalc(nowRule.dataCalib || '', buf, device)
    nowRule.dataCalibJS = calc
    log(device.name, nowRule.paramCode, 'Exp:', nowRule.dataCalib)
    log(device.name, nowRule.paramCode, 'Calc:', nowRule.dataCalibJS)

    try {
      let v = eval(calc)
      device.paramCodeValueMap[nowRule.paramCode] = v
      if (v === 0) {
        console.log('CommStatus OK')
        nowRule.calcResultValue = '通讯正常'
      } else {
        console.log('CommStatus NOT OK')
        nowRule.calcResultValue = '通讯失败'
        return
      }
    } catch (e) {
      log(e)
    }
  } else {
    calcStartPos = nowRule.dataStartPos
    vStr = lkutils.bytesToHex(buf)
    vBuf = buf

    const calc = convertCalc(nowRule.dataCalib || '', buf, device)
    nowRule.dataCalibJS = calc
    console.log(
      device.deviceName +
        ' ' +
        nowRule.paramCode +
        ' ' +
        nowRule.dataCalib +
        ' ' +
        nowRule.dataCalibJS
    )

    try {
      let v = eval(calc)
      device.paramCodeValueMap[nowRule.paramCode] = v
      nowRule.calcResultValue = v
      console.log(
        device.deviceName +
          ' ' +
          nowRule.paramCode +
          ' ' +
          nowRule.dataCalib +
          ' ' +
          nowRule.dataCalibJS +
          '    =' +
          v
      )
    } catch (e) {
      console.log(e)
    }
  }

  {
    for (let i = 0; i < rules.length; i++) {
      let rule = rules[i]
      if (rule.paramCode === nowRule.paramCode) {
        //
        rule.calcResultValue = nowRule.calcResultValue
      } else if (rule.refParamCode === nowRule.paramCode) {
        log(device.name, rule.paramCode, 'Exp:', rule.dataCalib)

        calcStartPos = rule.dataStartPos || 1
        calcLen = rule.dataValidLen || 0
        X = _X_()

        let calc = convertCalc(rule.dataCalib || '', buf, device)
        rule.dataCalibJS = calc
        log(device.name, rule.paramCode, 'Calc:', calc)

        try {
          let v = eval(calc)
          if (rule.paramDataType === 'Analogy') {
            v = parseFloat(v.toFixed(2))
          } else {
            v = parseInt(v)
          }
          if (isNaN(v)) {
            delete rdata.data
            return
          } else {
            log(device.name, rule.paramCode, 'Value:', v)
            device.paramCodeValueMap[rule.paramCode] = v
            rule.calcResultValue = v
          }
        } catch (error) {
          log(error)
        }
      }
    }
  }
}

module.exports = lkfuncs
