<template>
  <div>
    <div
      style="
        font-size: 20px;
        font-weight: 400;
        padding-top: 0px;
        padding-bottom: 20px;
      "
    >
      {{ project.name }} /
      <a-select
        v-model:value="productValue"
        style="width: 300px"
        :options="productOptions"
        @change="onProductValueChanged"
      ></a-select>

      <a-select
        v-model:value="deviceValue"
        style="margin-left: 10px; width: 300px"
        :options="deviceOptions"
        @change="onDeviceValueChanged"
      ></a-select>
    </div>
    <a-row :gutter="[16, 24]">
      <a-col :span="8">
        <div class="customInp">
          <div style="width: 100px">协议类型:</div>
          <a-select
            @change="onProtocolTypeChanged"
            v-model:value="device.protocolType"
            style="width: 200px"
            :options="protocolTypeOptions"
          ></a-select>
        </div>
      </a-col>
      <a-col
        v-if="
          device.protocolType === 'Modbus-RTU' ||
          device.protocolType === 'Modbus-TCP' ||
          device.protocolType === 'UDP'
        "
        :span="8"
      >
        <div class="customInp">
          <div style="width: 100px">数据类型:</div>
          <a-select
            v-model:value="comm.dataType"
            style="width: 200px"
            :options="dataTypeOptions"
          ></a-select>
        </div>
      </a-col>
      <a-col
        v-if="
          device.protocolType === 'Modbus-RTU' ||
          device.protocolType === 'Modbus-TCP' ||
          device.protocolType === 'UDP'
        "
        :span="8"
      >
        <div class="customInp">
          <div style="width: 100px">结束符:</div>
          <a-select
            v-model:value="comm.endType"
            style="width: 200px"
            :options="endTypeOptions"
          ></a-select>
        </div>
      </a-col>
    </a-row>
    <div
      v-if="
        device.protocolType === 'Modbus-RTU' ||
        device.protocolType === 'Modbus-ASCII' ||
        device.protocolType === 'Modbus-TCP'
      "
    >
      <a-row :gutter="[16, 24]">
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">选择串口:</div>
            <a-select
              v-model:value="comm.serialPort"
              :options="serialPortOptions"
            />
            <div style="padding-left: 10px">
              <a-button shape="circle" @click="scanSerialPortExisting">
                <template #icon><SearchOutlined /></template>
              </a-button>
            </div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">通讯格式:</div>
            <a-input
              v-model:value="comm.serialPortSetting"
              placeholder="示例: 115200,n,8,1"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">物理地址:</div>
            <a-input
              v-model:value="comm.serialPortAddress"
              placeholder="示例: 十进制12"
            />
          </div>
        </a-col>
      </a-row>
    </div>
    <div v-else-if="device.protocolType === 'SNMP'">
      <a-row :gutter="[16, 24]">
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备IP地址:</div>
            <a-input
              v-model:value="comm.snmpAgentIP"
              placeholder="示例: *************"
            />
          </div>
        </a-col>
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备端口:</div>
            <a-input
              v-model:value="comm.snmpAgentPort"
              placeholder="示例: 161"
            />
          </div>
        </a-col>

        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">团体名称:</div>
            <a-select
              v-model:value="comm.snmpAgentGroup"
              style="width: 200px"
              :options="snmpGroupOptions"
            ></a-select>
          </div>
        </a-col>
      </a-row>
    </div>
    <div v-if="device.protocolType === 'Modbus-TCP'">
      <a-row :gutter="[16, 24]">
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备IP地址:</div>
            <a-input v-model:value="comm.tcpIP" style="width: 200px"></a-input>
          </div>
        </a-col>

        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备端口:</div>
            <a-input
              v-model:value="comm.tcpPort"
              style="width: 200px"
            ></a-input>
          </div>
        </a-col>
      </a-row>
    </div>
    <div v-else-if="device.protocolType === 'UDP'">
      <a-row :gutter="[16, 24]">
        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备IP地址:</div>
            <a-input
              v-model:value="comm.udpAddress"
              style="width: 200px"
            ></a-input>
          </div>
        </a-col>

        <a-col :span="8">
          <div class="customInp">
            <div style="width: 100px">设备端口:</div>
            <a-input
              v-model:value="comm.udpPort"
              style="width: 200px"
            ></a-input>
          </div>
        </a-col>
      </a-row>
    </div>
    <a-row :gutter="[16, 24]">
      <!-- <a-col :span="8">
        <div class="customInp">
          <div style="width: 100px">驱动文件:</div>
        </div>
      </a-col> -->
      <!-- <a-col :span="4">
        <a-button type="primary">编辑采集规则</a-button>
      </a-col>
      <a-col :span="4">
        <a-button type="primary">刷新采集规则</a-button>
      </a-col> -->
    </a-row>
    <a-row>
      <a-col :span="3">
        <a-button
          v-if="isDeviceConnected === false"
          type="primary"
          @click="connectToDevice"
        >
          连接设备
        </a-button>
        <a-button v-else danger @click="connectToDevice">断开连接</a-button>
      </a-col>
      <a-col v-if="device.protocolType === 'SNMP'" :span="3">
        <a-button type="primary" @click="doSNMPAutoTest">
          SNMP自动发送测试
        </a-button>
      </a-col>
      <a-col :span="3">
        <a-button type="primary" @click="addProductPoolMth">
          添加到产品池
        </a-button>
      </a-col>
      <!-- <a-col :span="3">
        <a-checkbox :checked="enableLog" @click="enableLog = !enableLog">
          生成日志
        </a-checkbox>
      </a-col> -->
    </a-row>

    <div v-if="false">
      <file-selector v-model="files">
        <dropzone v-slot="{ hovered }">
          <div
            class="
              block
              w-full
              h-64
              rounded-lg
              border-4 border-dashed border-gray-400
              transition-colors
              duration-150
              flex flex-col
              space-y-4
              justify-center
              items-center
            "
            :class="{ 'border-blue-200': hovered }"
          >
            <!-- <ul>
              <li v-for="file in files" :key="file.name">
                {{ file.name }}
              </li>
            </ul> -->
            <dialog-button class="bg-indigo-400 rounded text-white px-2 py-1">
              Add files...
            </dialog-button>
          </div>
        </dropzone>
      </file-selector>
    </div>
    <a-button v-if="false" @click="importXLSXRules">导入</a-button>
    <a-table
      :dataSource="rules"
      :columns="columns"
      :pagination="false"
      style="margin-top: 10px"
      rowKey="paramCode"
      :scroll="{ y: scrollHight }"
      :size="'small'"
    >
      <template #bodyCell="{ column, text, record }">
        <template
          v-if="
            [
              'paramName',
              'tx',
              'dataCalib',
              'dataStartPos',
              'dataValidLen',
              'dataUnit',
            ].includes(column.dataIndex)
          "
        >
          <div>
            <a-input
              v-if="editableData[record.paramCode]"
              v-model:value="editableData[record.paramCode][column.dataIndex]"
              style="margin: -5px 0"
            />
            <template v-else>
              {{ text }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div class="editable-row-operations">
            <a
              v-if="record.tx && record.tx.trim().length !== 0"
              type="link"
              @click="sendTx(record)"
            >
              发送
            </a>
            <span v-if="editableData[record.paramCode]">
              <a-typography-link @click="save(record.paramCode)">
                保存
              </a-typography-link>
              <a-popconfirm
                title="确认取消?"
                @confirm="cancel(record.paramCode)"
              >
                <a>取消</a>
              </a-popconfirm>
            </span>
            <span v-else>
              <a @click="edit(record.paramCode)">编辑</a>
            </span>
          </div>
        </template>
        <template v-else>
          {{ text }}
        </template>
      </template>
    </a-table>
  </div>
</template>
<script>
import { FileSelector, Dropzone, DialogButton } from 'vue3-file-selector'

import mitt from 'mitt'
import { SearchOutlined } from '@ant-design/icons-vue'

import lkfuncs from './lkfuncs.js'

import SNMPHandle from './protocols/SNMP'
import ModbusRTUHandle from './protocols/ModbusRTU'
import ModbusTCPHandle from './protocols/ModbusTCP'
import ModbusASCIIHandle from './protocols/ModbusASCII'

import { project } from './samples/project1.js'

import debuggingTableDef from './debuggingTableDef.js'
import { cloneDeep } from 'lodash-es'
import { message } from 'ant-design-vue'

const emitter = mitt()
let lkutils = require('./lkutils.js')

let log = console.log

export default {
  components: {
    SearchOutlined,
    FileSelector,
    Dropzone,
    DialogButton,
  },
  data() {
    let device = project.users[0].devices[0]

    return {
      txTimer: undefined,
      files: [],
      productValue: '',
      project: project,
      device: device,
      comm: device.comm,
      rules: device.rules,
      productOptions: [],
      deviceValue: '',
      protocolTypeOptions: [
        { value: 'Modbus-RTU' },
        { value: 'Modbus-TCP' },
        { value: 'Modbus-ASCII' },
        { value: 'SNMP' },
        { value: 'HTTP 暂不支持' },
        { value: 'BacNet 暂不支持' },
      ],
      deviceOptions: [],
      dataTypeOptions: [{ value: 'ASCII' }, { value: 'HEX' }],
      endTypeOptions: [
        { value: '新行+回车' },
        { value: '新行' },
        { value: '回车' },
        { value: '无行结尾' },
      ],
      snmpGroupOptions: [{ value: 'public' }, { value: 'community' }],
      serialPortOptions: [],
      isDeviceConnected: false,
      enableLog: true,
      nowRule: {},
      columns: debuggingTableDef.columns,
      scrollHight: 0,
      editableData: {},
      deviceName: '',
      _data: [],
      productPoolObj: null,
    }
  },
  async mounted() {
    let o = document.getElementById('vab-content')
    let h = o.clientHeight || o.offsetHeight
    this.scrollHight = h - 290
    emitter.on('EVT_RECV_DATA', (data) => {
      if (this.isDeviceConnected) {
        this.processRecvData(this.device, data)
      }
    })
    this.comm.tcpIP = '*************'
    this.comm.tcpPort = 54322
    await this.getProject()
    await this.getProducts()
  },
  destroyed() {
    emitter.all.clear()
  },
  methods: {
    // 添加产品到产品池
    addProductPoolMth() {
      createProductPool(this.productPoolObj).then((res) => {
        message.success('添加到产品池成功')
      })
    },
    // 编辑数据
    edit(key) {
      this.editableData[key] = cloneDeep(
        this.rules.filter((item) => key === item.paramCode)[0]
      )
    },
    // 写日记
    async diaryInputMth(key) {
      let oldObj = this.rules.filter((item) => key === item.paramCode)[0]
      let newObj = this.editableData[key]
      if (oldObj['paramName'] !== newObj['paramName']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '参数名称',
          paramsField: 'paramName',
          oldValue: oldObj['paramName'],
          newValue: newObj['paramName'],
        }
        await addDiary(obj)
      }
      if (oldObj['dataStartPos'] !== newObj['dataStartPos']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '起始位',
          paramsField: 'dataStartPos',
          oldValue: oldObj['dataStartPos'],
          newValue: newObj['dataStartPos'],
        }
        await addDiary(obj)
      }
      if (oldObj['dataUnit'] !== newObj['dataUnit']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '单位',
          paramsField: 'dataUnit',
          oldValue: oldObj['dataUnit'],
          newValue: newObj['dataUnit'],
        }
        await addDiary(obj)
      }
      if (oldObj['dataValidLen'] !== newObj['dataValidLen']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '截取长度',
          paramsField: 'dataValidLen',
          oldValue: oldObj['dataValidLen'],
          newValue: newObj['dataValidLen'],
        }
        await addDiary(obj)
      }
      if (oldObj['tx'] !== newObj['tx']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '发送串',
          paramsField: 'tx',
          oldValue: oldObj['tx'],
          newValue: newObj['tx'],
        }
        await addDiary(obj)
      }
      if (oldObj['dataCalib'] !== newObj['dataCalib']) {
        let obj = {
          projectCode: this.productPoolObj.projectCode,
          model: this.productPoolObj.model,
          name: this.productPoolObj.name,
          paramsName: '计算公式',
          paramsField: 'dataCalib',
          oldValue: oldObj['dataCalib'],
          newValue: newObj['dataCalib'],
        }
        await addDiary(obj)
      }
    },
    // 保存修改的数据
    async save(key) {
      await this.diaryInputMth(key)
      Object.assign(
        this.rules.filter((item) => key === item.paramCode)[0],
        this.editableData[key]
      )
      delete this.editableData[key]
      if (
        this.productValue.indexOf('LK7053') === -1 &&
        this.productValue.indexOf('LK7063') === -1 &&
        this.productValue.indexOf('LK7017') === -1
      ) {
        await getProjectProductRules(this.productValue, this.rules)
      } else {
        await editProjectDevice(this.deviceValue, this.rules)
      }
    },
    // 取消编辑
    cancel(key) {
      delete this.editableData[key]
    },
    async getProjectDevice(uniKey) {
      const device = await getProjectDeviceByCode1(this.$route.query.code)
      let results = device
      let deviceOptions = []
      for (let i = 0; i < results.length; i++) {
        let r = results[i]
        if (uniKey.indexOf(r.attributes.deviceModel) !== -1) {
          deviceOptions.push({
            value: r.id,
            label: r.attributes.deviceName,
            orig: { ...r.attributes },
          })
        }
      }
      this.deviceOptions = deviceOptions
    },
    async getProject() {
      let projectId = this.$route.query.projectId || this.$route.query.key
      let project = await viewProject(projectId) // TODO
      this.project = { ...project.attributes }
    },
    async getProducts() {
      let r = await getProjectProductList({
        projectCode: this.$route.query.code,
      })
      let products = r.results
      let productOptions = []
      for (let i = 0; i < products.length; i++) {
        productOptions.push({
          value: products[i].id,
          label: products[i].get('name'),
          orig: { ...products[i].attributes },
        })
      }
      if (productOptions.length > 0) {
        this.productOptions = productOptions
        this.productValue = productOptions[0].value
        this.onProductValueChanged(this.productValue)
      }
    },
    importXLSXRules() {
      //         var workbook = XLSX.readFile('Master.xlsx');
      // var sheet_name_list = workbook.SheetNames;
      // var xlData = XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]]);
      // console.log(xlData);
      console.table(this.files)
    },
    onDeviceValueChanged(uniKey) {
      for (let i = 0; i < this.deviceOptions.length; i++) {
        if (this.deviceOptions[i].value === uniKey) {
          if (this.deviceOptions[i].orig.rules) {
            this.rules = this.deviceOptions[i].orig.rules
          }
        }
      }
    },
    onProductValueChanged(uniKey) {
      this.deviceValue = ''
      // if (
      //   uniKey.indexOf('LK7053') === -1 ||
      //   uniKey.indexOf('LK7063') === -1 ||
      //   uniKey.indexOf('LK7017') === -1
      // )
      {
        let specProduct = undefined
        for (let i = 0; i < this.productOptions.length; i++) {
          if (this.productOptions[i].value === uniKey) {
            specProduct = this.productOptions[i].orig
            this.getProjectDevice(this.productOptions[i].label)
          }
        }
        this.rules = specProduct.rules
        specProduct.status = '未审核'
        this.productPoolObj = specProduct
        // this.device.protocolType = specProduct.
      }
    },
    onProtocolTypeChanged(p) {
      if (p === 'Modbus-RTU') {
        this.scanSerialPortExisting()
      }
    },
    async scanSerialPortExisting() {
      console.log('scanSerialPortExisting')
      this.serialPortOptions = []
      let ports = await ModbusRTUHandle.scan(log)
      ports.map((port) => {
        this.serialPortOptions.push({
          value: port.path,
        })
      })
      if (this.serialPortOptions.length > 0) {
        // this.comm.serialPort = this.serialPortOptions[0].value
      }
    },
    connectToDevice() {
      this.isDeviceConnected = !this.isDeviceConnected
      if (this.isDeviceConnected) {
        // 打开设备
        if (this.device.protocolType === 'Modbus-RTU') {
          ModbusRTUHandle.open(
            log,
            this.device,
            (err) => {
              // open callback
              if (err) {
                this.$confirm({
                  title: '错误',
                  content: err.message,
                  onOk() {},
                  onCancel() {},
                })
              }
            },
            () => {
              // sent callback
            },
            (data) => {
              // recv callback
              console.log(
                'ModbusRTU.收到(' + data.length + '):',
                lkutils.bytesToHexSpace(data)
              )
              emitter.emit('EVT_RECV_DATA', data)
              // this.connectToDevice()
            },
            (err) => {
              // error callback
              console.log('err', err)
              // this.$confirm({
              //   title: '错误',
              //   content: err,
              //   onOk() {},
              //   onCancel() {},
              // })
            }
          )
        } else if (this.device.protocolType === 'II') {
          ModbusASCIIHandle.open(
            log,
            this.device,
            (err) => {
              if (err) {
                this.$confirm({
                  title: '错误',
                  content: err.message,
                  onOk() {},
                  onCancel() {},
                })
              }
            },
            () => {},
            (data) => {
              console.log(
                'ModbusASCII.收到(' + data.length + '):',
                lkutils.bytesToHexSpace(data)
              )
              emitter.emit('EVT_RECV_DATA', data)
            },
            (err) => {
              console.log('err', err)
            }
          )
        } else if (this.device.protocolType === 'Modbus-TCP') {
          ModbusTCPHandle.open(
            log,
            this.device,
            (err) => {
              // open callback
              if (err) {
                this.$confirm({
                  title: '错误',
                  content: err.message,
                  onOk() {},
                  onCancel() {},
                })
              }
            },
            () => {
              // sent callback
            },
            (data) => {
              function hex_to_ascii(str1) {
                var hex = str1.toString()
                var str = ''
                for (var n = 0; n < hex.length; n += 2) {
                  str += String.fromCharCode(parseInt(hex.substr(n, 2), 16))
                }
                return str
              }

              // recv callback
              if (this.device.comm.dataType === 'HEX') {
                console.log(
                  'ModbusTCP.收到 HEX(' + data.length + '):',
                  lkutils.bytesToHexSpace(data)
                )
                emitter.emit('EVT_RECV_DATA', data)
              } else if (this.device.comm.dataType === 'ASCII') {
                let s = hex_to_ascii(data)
                console.log('ModbusTCP.收到 ASCII(' + data.length + '):', s)

                this._data = this._data.concat(data)
                console.log('_data', this._data)
                if (this._data[this._data.length - 1] === 13) {
                  this._data.pop()
                  emitter.emit('EVT_RECV_DATA', [...this._data])
                  this._data = []
                }
              }
            },
            (err) => {
              // error callback
              console.log('err', err)
            }
          )
        } else if (this.device.protocolType === 'SNMP') {
          SNMPHandle.open(
            log,
            this.device,
            () => {
              // open callback
            },
            () => {
              // sent callback
            },
            () => {
              // recv callback
            },
            (err) => {
              // error callback
              console.log('err', err)
            }
          )
        }
      } else {
        // 关闭设备
        if (this.device.protocolType === 'Modbus-RTU') {
          ModbusRTUHandle.close(log, this.device)
        } else if (this.device.protocolType === 'SNMP') {
          SNMPHandle.close(log, this.device)
        }
      }
    },
    // SNMP自动化测试，每200毫秒GET一个oid
    doSNMPAutoTest() {
      let interval = 200 // 每200毫秒GET一个oid
      let ruleIdx = 0
      this.snmpAutoTestTimer = setInterval(() => {
        if (ruleIdx === this.rules.length - 1) {
          clearInterval(this.snmpAutoTestTimer)
        }
        if (this.rules[ruleIdx].tx.trim().length === 0) {
          //
        } else {
          this.sendTx(this.rules[ruleIdx])
        }

        ruleIdx = ruleIdx + 1
      }, interval)
    },
    // 处理收到的数据
    processRecvData(device, data) {
      lkfuncs.parse(log, this.device, this.rules, this.nowRule, data)
    },
    sendTx0(rule) {
      this.connectToDevice()
      setTimeout(() => {
        this.sendTx(rule)
      }, 3000)
    },
    sendTx(rule) {
      if (this.device.paramCodeValueMap === undefined) {
        this.device.paramCodeValueMap = {}
      }
      if (this.isDeviceConnected === false) {
        this.$confirm({
          title: '错误',
          content: '请先点击 连接设备 按钮',
          onOk() {},
          onCancel() {},
        })
        return
      }

      this.nowRule = { ...rule }

      if (this.txTimer) {
        clearTimeout(this.txTimer)
      }
      this.txTimer = setTimeout(() => {
        // 最后一次发送完成后，等待2秒，断开设备连接
        this.connectToDevice()
      }, 2000)

      // 清除相关rule的计算后的值
      for (let i = 0; i < this.rules.length; i++) {
        if (
          this.rules[i].paramCode === rule.paramCode ||
          this.rules[i].paramCodeRef === rule.paramCode
        ) {
          this.rules[i].calcResultValue = ''
        }
      }
      // console.log(JSON.stringify(this.nowRule))

      if (this.device.protocolType === 'Modbus-RTU') {
        ModbusRTUHandle.send(log, this.device, rule, (resp) => {
          //
        })
      }
      if (this.device.protocolType === 'Modbus-ASCII') {
        ModbusASCIIHandle.send(log, this.device, rule, (resp) => {
          //
        })
      } else if (this.device.protocolType === 'Modbus-TCP') {
        ModbusTCPHandle.send(log, this.device, rule, (resp) => {
          //
        })
      } else if (this.device.protocolType === 'SNMP') {
        SNMPHandle.send(log, this.device, rule, (resp) => {
          emitter.emit(
            'EVT_RECV_DATA', //
            {
              tx: resp.tx,
              value: resp.value,
              type: 'NUMBER', // 暂时全部都是整型
            }
          )
        })
      }
    },
  },
}
</script>
<style scoped lang="less">
.customInp {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .ant-input,
  .ant-select {
    margin-left: 20px;
    width: 200px;
  }
}
</style>
