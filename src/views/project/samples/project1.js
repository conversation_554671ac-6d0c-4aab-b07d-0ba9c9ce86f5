module.exports = {
  project: {
    name: '示例工程1',
    customer: '龙坤无锡',
    workers: [{ name: '俞琳' }],
    scale: '1000万',
    commAddress: '昌兴国际金融大厦',
    spaces: [],
    users: [
      {
        name: 'user1',
        devices: [
          {
            deviceModel: {
              system: '环境系统',
              name: 'TH802P',
              code: 'TH802P',
              deviceType: '',
              brand: 'BTR',
              memo: '',
            },
            name: '温湿度',
            protocolType: 'Modbus-RTU',
            comm: {
              dataType: 'HEX',
              endType: '新行+回车',
              serialPort: '/dev/tty.usbserial-00000001',
              serialPortSetting: '9600,n,8,1',
              serialPortAddress: '1',
              tcpIP: '',
              tcpPort: '',
              udpAddress: '',
              udpPort: '',
              snmpAgentIP: '',
              snmpAgentPort: '',
              snmpAgentGroup: 'public',
            },
            rules: [
              {
                key: '1',
                name: '通讯状态',
                id: 'Ups_Status_Comm',
                type: 'CommStatus',
                tx: '01040000000271CB',
                refParamCode: '',
                calc: 'if(length=9,0,1)',
                startPos: 1,
                truncLength: 9,
                calcResultValue: undefined,
                calcResultUnit: 'kwh',
              },
              {
                key: '2',
                name: '空气温度',
                id: 'HT_Temperature',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 7,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '℃',
              },
              {
                key: '3',
                name: '空气湿度',
                id: 'HT_Humidity',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 11,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '%',
              },
            ],
          },
          {
            deviceModel: {
              system: '环境系统',
              name: 'TH802P-TCP',
              code: 'TH802P-TCP',
              deviceType: '',
              brand: 'BTR',
              memo: '',
            },
            name: '温湿度',
            protocolType: 'ModbusTCP',
            comm: {
              dataType: 'HEX',
              endType: '新行+回车',
              serialPort: '',
              serialPortSetting: '',
              serialPortAddress: '1',
              tcpIP: '*************',
              tcpPort: '502',
              udpAddress: '',
              udpPort: '',
              snmpAgentIP: '',
              snmpAgentPort: '',
              snmpAgentGroup: 'public',
            },
            rules: [
              {
                key: '1',
                name: '通讯状态',
                id: 'Ups_Status_Comm',
                type: 'CommStatus',
                tx: '000000000006010300000002',
                refParamCode: '',
                calc: 'if(length=13,0,1)',
                startPos: 1,
                truncLength: 13,
                calcResultValue: undefined,
                calcResultUnit: 'kwh',
              },
              {
                key: '2',
                name: '空气温度',
                id: 'HT_Temperature',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 19,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '℃',
              },
              {
                key: '3',
                name: '空气湿度',
                id: 'HT_Humidity',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 23,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '%',
              },
            ],
          },
          {
            deviceModel: {
              system: '其他',
              name: 'Ubuntu Linux 20.04',
              code: 'Linux2004',
              deviceType: '模拟',
              brand: 'Ubuntu',
              memo: '',
            },
            name: '虚拟机1',
            protocolType: 'SNMP',
            comm: {
              dataType: 'ASCII',
              endType: '新行+回车',
              serialPort: '',
              serialPortSetting: '',
              serialPortAddress: '',
              tcpIP: '',
              tcpPort: '',
              udpAddress: '',
              udpPort: '',
              snmpAgentIP: '*************',
              snmpAgentPort: '161',
              snmpAgentGroup: 'public',
            },
            rules: [
              {
                key: '1',
                name: 'CPU使用率',
                id: 'CPU_Usage',
                type: 'Analogy',
                tx: '*******.********.5.0',
                refParamCode: '',
                calc: 'X',
                startPos: undefined,
                truncLength: undefined,
                calcResultValue: undefined,
                calcResultUnit: '',
              },
              {
                key: '2',
                name: '内存使用率',
                id: 'MEM_Usage',
                type: 'Analogy',
                tx: '*******.********.6.0',
                refParamCode: '',
                calc: 'X*1',
                startPos: undefined,
                truncLength: undefined,
                calcResultValue: undefined,
                calcResultUnit: '',
              },
            ],
          },
        ],
      },
      {
        name: 'user2',
        devices: [],
      },
    ],
  },
}
