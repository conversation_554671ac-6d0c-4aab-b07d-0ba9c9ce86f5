module.exports = {
  project: {
    name: '示例工程',
    customer: '龙坤无锡',
    workers: [{ name: '俞琳' }],
    scale: '1000万',
    commAddress: '昌兴国际金融大厦',
    spaces: [],
    products: [],
    users: [
      {
        name: 'user1',
        devices: [
          {
            deviceModel: {
              name: '',
              code: 'TH802P-ModbusRTU',
              deviceType: '',
              brand: '梅兰',
              memo: '',
            },
            name: '发电机',
            protocolType: 'Modbus-RTU',
            comm: {
              dataType: 'HEX',
              endType: '新行+回车',
              serialPort: '/dev/tty.usbserial-00000001',
              serialPortSetting: '9600,n,8,1',
              serialPortAddress: '1',
              tcpIP: '',
              tcpPort: '',
              udpAddress: '',
              udpPort: '',
              snmpAgentIP: '',
              snmpAgentPort: '',
              snmpAgentGroup: 'public',
            },
            rules: [
              {
                key: '1',
                name: '通讯状态',
                id: 'Ups_Status_Comm',
                type: 'CommStatus',
                tx: '01040000000271CB',
                refParamCode: '',
                calc: 'if(length=9,0,1)',
                startPos: 1,
                truncLength: 9,
                calcResultValue: undefined,
                calcResultUnit: 'kwh',
              },
              {
                key: '2',
                name: '空气温度',
                id: 'HT_Temperature',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 7,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '℃',
              },
              {
                key: '3',
                name: '空气湿度',
                id: 'HT_Humidity',
                type: 'Analogy',
                tx: '',
                refParamCode: 'Ups_Status_Comm',
                calc: 'A$(1,4)*0.1',
                startPos: 11,
                truncLength: 4,
                calcResultValue: undefined,
                calcResultUnit: '%',
              },
            ],
          },
        ],
      },
      {
        name: 'user2',
        devices: [],
      },
    ],
  },
}
