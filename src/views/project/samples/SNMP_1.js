module.exports = {
  project: {
    name: '示例工程',
    customer: '龙坤无锡',
    workers: [{ name: '俞琳' }],
    scale: '1000万',
    commAddress: '昌兴国际金融大厦',
    spaces: [],
    users: [
      {
        name: 'user1',
        devices: [
          {
            deviceModel: {
              name: '',
              code: 'TH802P',
              deviceType: '',
              brand: '梅兰',
              memo: '',
            },
            name: '发电机',
            protocolType: 'SNMP',
            comm: {
              dataType: 'ASCII',
              endType: '新行+回车',
              serialPort: '',
              serialPortSetting: '',
              serialPortAddress: '',
              tcpIP: '',
              tcpPort: '',
              udpAddress: '',
              udpPort: '',
              snmpAgentIP: '*************',
              snmpAgentPort: '161',
              snmpAgentGroup: 'public',
            },
            rules: [
              {
                key: '1',
                name: 'CPU使用率',
                id: 'CPU_Usage',
                type: 'Analogy',
                tx: '*******.********.5.0',
                refParamCode: '',
                calc: 'X',
                startPos: undefined,
                truncLength: undefined,
                calcResultValue: undefined,
                calcResultUnit: '',
              },
              {
                key: '2',
                name: '内存使用率',
                id: 'MEM_Usage',
                type: 'Analogy',
                tx: '*******.********.6.0',
                refParamCode: '',
                calc: 'X*1',
                startPos: undefined,
                truncLength: undefined,
                calcResultValue: undefined,
                calcResultUnit: '',
              },
            ],
          },
        ],
      },
      {
        name: 'user2',
        devices: [],
      },
    ],
  },
}
