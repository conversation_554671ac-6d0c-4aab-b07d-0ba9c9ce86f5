const lkutils = require('../lkutils.js')

let Net = undefined

if (window && window.process && window.process.type === 'renderer') {
  Net = window.require('net')
} else {
  Net = require('net')
}

module.exports = {
  client: undefined,
  open: (
    log,
    device,
    openCallback,
    sentCallback,
    recievedCallback,
    errorCallback
  ) => {
    // 打开串口设备
    log('ModbusTCP.open', device.name, JSON.stringify(device.comm))

    this.client = new Net.Socket()

    // Send a connection request to the server.
    this.client.connect(
      { host: device.comm.tcpIP, port: device.comm.tcpPort },
      function (err) {
        console.log(err)
        console.log('ModbusTCP connected')
      }
    )

    this.client.on('data', function (data) {
      let d = []
      for (let i = 0; i < data.length; i++) {
        d.push(data[i])
      }

      if (recievedCallback) {
        recievedCallback(d)
      }
    })

    // this.client.on('end', function () {
    //   console.log('Requested an end to the TCP connection')
    // })
  },
  close: (log, device) => {
    // 关闭串口
    log('ModbusTCP.close', device.name, device.comm)
    this.client.close()
  },
  send: (log, device, rule, cb) => {
    //
    log('ModbusTCP.send', device.name, rule.tx)
    if (device.comm.dataType === 'HEX') {
      let bytes = lkutils.hexToBytes(rule.tx)
      this.client.write(Buffer.from(bytes), (err) => {
        console.log(
          '配置工具 => ' + device.name,
          '发送(' + bytes.length + '):',
          lkutils.bytesToHexSpace(bytes)
        )
      })
    } else if (device.comm.dataType === 'ASCII') {
      console.log('配置工具 => ' + device.name, '发送:', rule.tx)
      let tx = rule.tx
      if (tx.startsWith('~')) {
        let ADDR = ''
        let address = parseInt(device.comm.serialPortAddress)
        if (address >= 16) {
          ADDR = address.toString(16).toUpperCase()
        } else {
          ADDR = '0' + address.toString(16).toUpperCase()
        }
        tx = tx.replace('{ADDR}', ADDR)
        let crc = 0
        for (let k = 1; k < tx.length; k++) {
          crc = tx.charCodeAt(k) + crc
        }
        crc = crc % 65536
        crc = (0xffff ^ crc) + 1
        if (crc >= 16) {
          crc = crc.toString(16).toUpperCase()
        } else {
          crc = '0' + crc.toString(16).toUpperCase()
        }
        tx = tx + crc + '\r'
      }
      console.log('配置工具 => ' + device.name, '发送(' + tx.length + '):', tx)
      this.client.write(tx)
    }
  },
}
