const lkutils = require('../lkutils.js')

let SerialPort = undefined

if (window && window.process && window.process.type === 'renderer') {
  SerialPort = window.require('serialport').SerialPort
} else {
  SerialPort = {
    list: () => {
      return []
    },
  }
}

module.exports = {
  port: undefined,
  timerToParse: undefined,
  scan: async (log) => {
    // 扫描本地串口
    let ports = await SerialPort.list()
    return ports
  },
  open: (
    log,
    device,
    openCallback,
    sentCallback,
    recievedCallback,
    errorCallback
  ) => {
    this.device = device
    this.openCallback = openCallback
    this.sentCallback = sentCallback
    this.recievedCallback = recievedCallback
    this.errorCallback = errorCallback

    // 打开串口设备
    log('ModbusRTU.open', device.name, device.comm)

    const baudRate = parseInt(device.comm.serialPortSetting.split(',')[0])
    const serialPortOption = {
      path: device.comm.serialPort,
      baudRate: baudRate,
    }
    // console.log('serialPortOption', JSON.stringify(serialPortOption))
    this.port = new SerialPort(serialPortOption)
    this.port.on('error', (err) => {
      console.log('Error: ', err.message)
      if (openCallback) {
        openCallback(err)
      }
    })
    this.port.on('data', (data) => {
      // console.log('<==== MobusRTU.Recv:', data)
      for (let i = 0; i < data.length; i++) {
        this.response.push(data[i])
      }
      if (this.timerToParse) {
        clearTimeout(this.timerToParse)
      }
      this.timerToParse = setTimeout(() => {
        if (this.recievedCallback) {
          this.recievedCallback(this.response)
        }
        this.timerToParse = undefined
      }, 100)
    })
  },
  close: (log, device) => {
    // 关闭串口
    log('ModbusRTU.close', device.name)
    this.port.close()
  },
  send: (log, device, rule, cb) => {
    //
    log('ModbusRTU.发送', device.name, rule.tx)

    this.response = []

    if (device.comm.dataType === 'HEX') {
      // 十六进制类型
      let bytes = lkutils.hexToBytes(rule.tx)
      bytes.unshift(parseInt(device.comm.serialPortAddress))
      let crc = lkutils.calcModbusCRC16(bytes)
      bytes.push(crc >> 8)
      bytes.push(crc & 0xff)
      console.log('sending bytes', bytes)
      this.port.write(Buffer.from(bytes), (err) => {
        if (err) {
          console.error('发送错误', err)
        }
      })
    } else if (device.comm.dataType === 'ASCII') {
      // TODO
    }
  },
}
