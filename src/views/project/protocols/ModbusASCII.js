const lkutils = require('../lkutils.js')

let SerialPort = undefined

if (window && window.process && window.process.type === 'renderer') {
  SerialPort = window.require('serialport').SerialPort
} else {
  SerialPort = {
    list: () => {
      return []
    },
  }
}

module.exports = {
  port: undefined,
  timerToParse: undefined,
  scan: async (log) => {
    // 扫描本地串口
    let ports = await SerialPort.list()
    return ports
  },
  open: (
    log,
    device,
    openCallback,
    sentCallback,
    recievedCallback,
    errorCallback
  ) => {
    this.device = device
    this.openCallback = openCallback
    this.sentCallback = sentCallback
    this.recievedCallback = recievedCallback
    this.errorCallback = errorCallback

    // 打开串口设备
    log('ModbusASCII.open', device.name, device.comm)

    const baudRate = parseInt(device.comm.serialPortSetting.split(',')[0])
    const serialPortOption = {
      path: device.comm.serialPort,
      baudRate: baudRate,
    }
    // console.log('serialPortOption', JSON.stringify(serialPortOption))
    this.port = new SerialPort(serialPortOption)
    this.port.on('error', (err) => {
      console.log('Error: ', err.message)
      if (openCallback) {
        openCallback(err)
      }
    })
    this.port.on('data', (data) => {
      // console.log('<==== ModbusASCII.Recv:', data)
      for (let i = 0; i < data.length; i++) {
        this.response.push(data[i])
      }
      if (this.timerToParse) {
        clearTimeout(this.timerToParse)
      }
      this.timerToParse = setTimeout(() => {
        if (this.recievedCallback) {
          this.recievedCallback(this.response)
        }
        this.timerToParse = undefined
      }, 100)
    })
  },
  close: (log, device) => {
    // 关闭串口
    log('ModbusASCII.close', device.name)
    this.port.close()
  },
  send: (log, device, rule, cb) => {
    //
    log('ModbusASCII.发送', device.comm.serialPortAddress, device.name, rule.tx)

    this.response = []

    // ASCII
    let ADDR = ''
    let address = parseInt(device.comm.serialPortAddress)
    if (address >= 16) {
      ADDR = address.toString(16).toUpperCase()
    } else {
      ADDR = '0' + address.toString(16).toUpperCase()
    }
    let newTx = rule.tx.replace('{ADDR}', ADDR) + '\n'
    console.log(newTx)
    // bytes.unshift(parseInt(device.comm.serialPortAddress))
    // let crc = lkutils.calcModbusCRC16(bytes)
    // bytes.push(crc >> 8)
    // bytes.push(crc & 0xff)
    console.log('sending newTx', newTx)
    this.port.write(newTx, (err) => {
      if (err) {
        console.error('发送错误', err)
      }
    })
  },
}
