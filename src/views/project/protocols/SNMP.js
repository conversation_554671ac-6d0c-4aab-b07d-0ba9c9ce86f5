let snmp = undefined

if (window && window.process && window.process.type === 'renderer') {
  snmp = window.require('net-snmp')
} else {
  snmp = require('net-snmp')
}

module.exports = {
  snmp_session: undefined,
  open: (
    log,
    device,
    openCallback,
    sentCallback,
    recievedCallback,
    errorCallback
  ) => {
    this.device = device
    this.openCallback = openCallback
    this.sentCallback = sentCallback
    this.recievedCallback = recievedCallback
    this.errorCallback = errorCallback

    // 打开SNMP设备, *************
    log('SNMP open', device.name, device)

    this.snmp_session = snmp.createSession(
      device.comm.snmpAgentIP,
      device.comm.snmpAgentGroup,
      {
        timeout: 200,
        backwardsGetNexts: true,
      }
    )

    this.snmp_session.on('error', () => {
      console.log(
        'SNMP ERROR',
        device.comm.snmpAgentIP + ':' + device.comm.snmpAgentPort
      )
    })

    this.snmp_session.on('close', () => {})
  },
  close: (log, device) => {
    console.log('SNMP close:', device.name, device.comm)
  },
  send: (log, device, rule, respCallback) => {
    console.log('===> SNMP Req :', 'oid=' + rule.tx)
    this.snmp_session.get([rule.tx], (error, varbinds) => {
      if (error) {
        log('SNMP GET 失败:', error)
        if (this.errorCallback) {
          this.errorCallback('ERR_SNMP_GET')
        }
      } else {
        for (let i = 0; i < varbinds.length; i++) {
          if (snmp.isVarbindError(varbinds[i])) {
            log('无效oid返回值')
            this.errorCallback('ERR_SNMP_GET_INVALID_OID')
          } else {
            let _v = varbinds[i].value.toString()
            let statement = rule.calc.replace('X', '_v')
            let _vv = eval(statement)
            _vv = parseFloat(_vv)
            console.log(
              '<=== SNMP Resp:',
              'oid=' + varbinds[i].oid,
              'value=' + _v,
              'calValue=' + _vv
            )
            if (respCallback) {
              respCallback({
                tx: rule.tx,
                value: _vv,
              })
            }
          }
        }
      }
    })
  },
}
