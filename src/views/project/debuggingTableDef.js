let columnDev = [
  {
    title: '参数名称',
    dataIndex: 'paramName',
  },
  {
    title: '参数ID',
    dataIndex: 'paramCode',
  },
  {
    title: '发送串',
    dataIndex: 'tx',
    key: 'tx',
  },
  {
    title: '引用ID',
    dataIndex: 'refParamCode',
    key: 'refParamCode',
  },
  {
    title: '计算公式',
    dataIndex: 'dataCalib',
    key: 'dataCalib',
  },
  {
    title: '计算公式JS',
    dataIndex: 'dataCalibJS',
    key: 'dataCalibJS',
  },
  {
    title: '起始位',
    dataIndex: 'dataStartPos',
    key: 'dataStartPos',
  },
  {
    title: '截取长度',
    dataIndex: 'dataValidLen',
    key: 'dataValidLen',
  },
  {
    title: '返回值',
    dataIndex: 'calcResultValue',
    key: 'calcResultValue',
  },
  {
    title: '单位',
    dataIndex: 'dataUnit',
    key: 'dataUnit',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '120px',
  },
]

let columnProduct = [
  {
    title: '参数名称',
    dataIndex: 'paramName',
  },
  {
    title: '参数ID',
    dataIndex: 'paramCode',
  },
  {
    title: '发送串',
    dataIndex: 'tx',
    key: 'tx',
  },
  {
    title: '计算公式',
    dataIndex: 'dataCalib',
    key: 'dataCalib',
  },
  {
    title: '起始位',
    dataIndex: 'dataStartPos',
    key: 'dataStartPos',
  },
  {
    title: '截取长度',
    dataIndex: 'dataValidLen',
    key: 'dataValidLen',
  },
  {
    title: '返回值',
    dataIndex: 'calcResultValue',
    key: 'calcResultValue',
  },
  {
    title: '单位',
    dataIndex: 'dataUnit',
    key: 'dataUnit',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '120px',
  },
]
let columns = []
if (window.location.href.startsWith('http')) {
  columns = columnDev
} else {
  columns = columnProduct
}
module.exports = {
  columns: columns,
}
