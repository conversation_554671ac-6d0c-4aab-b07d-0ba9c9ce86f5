<template>
  <div class="editor-main">
    <div class="editor-com">
      <!-- <div class="tool-bar">
      <PlusCircleOutlined
        @click="
          (e) => {
            scaleView(e, 'up')
          }
        "
      />
      <MinusCircleOutlined
        @click="
          (e) => {
            scaleView(e, 'down')
          }
        "
      />
      <DeleteOutlined @click="delNode" />
      <UndoOutlined @click="undo" />
      <RedoOutlined @click="redo" />
    </div> -->
      <a-card class="card-main" style="width: 1624px">
        <template #title>
          <div  :style="showLeftFlag ? 'margin-left: 300px':''">301机房</div>
        </template>
        <template #extra>
          <span :style="showRightFlag ? 'margin-right: 300px':''">
            <a-tag class="tag-xitong" color="#108ee9">全部</a-tag>
            <a-tag class="tag-xitong" color="#108ee9">电力系统</a-tag>
            <a-tag class="tag-xitong" color="#108ee9">环境系统</a-tag>
          </span>
        </template>
        <div id="container" class="x6-graph">
          <!-- <div id="stencil"></div> -->
          <!-- <div
            id="graph-container"
            class="x6-graph"
            tabindex="-1"
            style="height: 755px; background: #fff"
          ></div> -->
        </div>
      </a-card>
      <div class="footer-buttons">
        <a-upload
          v-model:file-list="fileListBg"
          name="file"
          :show-upload-list="false"
          :before-upload="uploadBgImg"
        >
          <a-button type="primary">上传背景图</a-button>
        </a-upload>
        <a-button type="primary" @click="delBgImg" style="margin-left: 10px">
          删除背景图
        </a-button>
        <a-button type="primary" @click="exportJson" style="margin-left: 10px">
          导出JSON
        </a-button>
      </div>
      <!-- <a-button type="primary" @click="uploadBgImg">上传背景图</a-button> -->
      <a-drawer
        width="450"
        placement="right"
        :closable="false"
        :mask="false"
        :visible="showAttrConfig"
        :draw-style="{ position: 'absolute' }"
        @close="onClose"
      >
        <a-form
          class="form-main"
          :model="form"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-form-item label="x轴">
            <a-input v-model:value="form.x" />
          </a-form-item>
          <a-form-item label="y轴">
            <a-input v-model:value="form.y" />
          </a-form-item>
          <a-form-item label="宽">
            <a-input v-model:value="form.width" />
          </a-form-item>
          <a-form-item label="高">
            <a-input v-model:value="form.height" />
          </a-form-item>
          <a-form-item
            label="运行状态"
            v-show="selectedCell.component == 'WaterPump'"
          >
            <a-select
              v-model:value="form.status"
              style="width: 120px"
              @change="changeStatus"
            >
              <a-select-option :value="0">关机</a-select-option>
              <a-select-option :value="1">开机</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="配置数据源">
            <a-input />
          </a-form-item>
          <a-divider />
          <div v-if="selectedCell.component == 'TextComp'">
            <a-form-item label="文字内容">
              <a-input v-model:value="form.text" @change="changeText" />
            </a-form-item>
            <a-form-item label="对齐方式">
              <a-select
                v-model:value="form.alain"
                style="width: 120px"
                @change="changeAlain"
              >
                <a-select-option value="left">左对齐</a-select-option>
                <a-select-option value="right">右对齐</a-select-option>
                <a-select-option value="center">居中</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="文字颜色">
              <MyColorSelect @updatecolor="updatecolor"></MyColorSelect>
            </a-form-item>
            <a-form-item label="背景色">
              <MyColorSelect @updatecolor="updateBGcolor"></MyColorSelect>
            </a-form-item>
            <a-form-item label="行高">
              <a-input
                v-model:value="form.lineHeight"
                @change="changeLineHeight"
              />
            </a-form-item>
            <a-form-item label="大小">
              <a-input v-model:value="form.fontSize" @change="changeFontSize" />
            </a-form-item>
          </div>
          <div v-if="selectedCell.component == 'ImgComp'">
            <a-form-item label="上传图片">
              <a-upload
                v-model:file-list="fileList"
                name="file"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="false"
                action="/api/uploadImg"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <img v-if="form.imgUrl" :src="form.imgUrl" alt="avatar" />
                <div v-else>
                  <loading-outlined v-if="loading"></loading-outlined>
                  <plus-outlined v-else></plus-outlined>
                  <div class="ant-upload-text">上传图片</div>
                </div>
              </a-upload>
            </a-form-item>
          </div>
        </a-form>
        <div class="footer" style="position: absolute; bottom: 30px">
          <span>
            <a-button @click="onClose">取消</a-button>
            <a-button type="primary" @click="confirm" style="margin-left: 20px">
              确定
            </a-button>
          </span>
        </div>
      </a-drawer>
    </div>
  </div>
</template>

<script>
  import { Graph, Shape, Addon } from '@antv/x6'
  import '@antv/x6-vue-shape'
  import {
    PlusCircleOutlined,
    MinusCircleOutlined,
    DeleteOutlined,
    UndoOutlined,
    RedoOutlined,
  } from '@ant-design/icons-vue'
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
  import { uploadImg } from '@/utils'
  export default {
    data() {
      return {
        form: {
          x: '',
          y: '',
          with: '',
          height: '',
          status: 0, //运行状态
          alain: 'left',
          lineHeight: '', //行高
          fontSize: '', // 文字大小
          imgUrl: '',
        },
        labelCol: { span: 7 },
        wrapperCol: { span: 14 },
        graph: null,
        showAttrConfig: false,
        nodeFrmData: {},
        initScale: 1,
        viewTranslate: { graphX: 0, graphY: 0 },
        data: { num: 0, url: 'logo.png' },
        selectedCell: null,
        fileList: [],
        fileListBg: [],
        bgUrl: [],
        loading: false,
        showLeftFlag: false,
        showRightFlag: false,
      }
    },
    components: {
      PlusCircleOutlined,
      MinusCircleOutlined,
      DeleteOutlined,
      UndoOutlined,
      RedoOutlined,
      PlusOutlined,
      LoadingOutlined,
    },
    beforeDestroy() {},
    mounted() {
      // #region 初始化画布
      this.graph = new Graph({
        container: document.getElementById('container'),
        grid: 1,
        history: true,
        // mousewheel: {
        //   enabled: true,
        //   zoomAtMousePosition: true,
        //   modifiers: "ctrl",
        //   minScale: 0.5,
        //   maxScale: 3
        // },
        // connecting: {
        //   router: "manhattan",
        //   connector: {
        //     name: "rounded",
        //     args: {
        //       radius: 8,
        //     },
        //   },
        //   anchor: "center",
        //   connectionPoint: "anchor",
        //   allowBlank: false,
        //   snap: {
        //     radius: 20,
        //   },
        //   createEdge() {
        //     return new Shape.Edge({
        //       attrs: {
        //         line: {
        //           stroke: "#A2B1C3",
        //           strokeWidth: 2,
        //           targetMarker: {
        //             name: "block",
        //             width: 12,
        //             height: 8,
        //           },
        //         },
        //       },
        //       zIndex: 0,
        //     })
        //   },
        //   validateConnection({ targetMagnet }) {
        //     return !!targetMagnet
        //   },
        // },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#5F95FF',
                stroke: '#5F95FF',
              },
            },
          },
        },
        resizing: true,
        rotating: true,
        selecting: {
          enabled: true,
          rubberband: true,
          showNodeSelectionBox: false,
        },
        snapline: true,
        keyboard: true,
        clipboard: true,
      })
      // #endregion
      // #region 快捷键与事件

      this.graph.on('node:selected', ({ cell, options }) => {
        console.log('node:selected==========', cell)
        // code here
        this.selectedCell = cell
        let size = cell.size()
        let position = cell.position()
        if (this.selectedCell.data) {
          this.form.x = position.x
          this.form.y = position.y
          this.form.width = size.width
          this.form.height = size.height
          if (this.selectedCell.component == 'WaterPump') {
            this.form.status = this.selectedCell.data.status
              ? this.selectedCell.data.status
              : 0
          } else if (this.selectedCell.component == 'TextComp') {
            this.form.text = this.selectedCell.data.text
              ? this.selectedCell.data.text
              : '默认文字'
          }
          // this.selectedCell.data = { a: 111 }
          console.log(cell, options, this.selectedCell.data)
          this.showAttrConfig = true
        }
      })

      // copy cut paste
      this.graph.bindKey(['meta+c', 'ctrl+c'], () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.copy(cells)
        }
        return false
      })
      this.graph.bindKey(['meta+x', 'ctrl+x'], () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.cut(cells)
        }
        return false
      })
      this.graph.bindKey(['meta+v', 'ctrl+v'], () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 })
          this.graph.cleanSelection()
          this.graph.select(cells)
        }
        return false
      })

      //undo redo
      this.graph.bindKey(['meta+z', 'ctrl+z'], () => {
        if (this.graph.history.canUndo()) {
          this.graph.history.undo()
        }
        return false
      })
      this.graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
        if (this.graph.history.canRedo()) {
          this.graph.history.redo()
        }
        return false
      })

      // select all
      this.graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = this.graph.getNodes()
        if (nodes) {
          this.graph.select(nodes)
        }
      })

      //delete
      this.graph.bindKey('backspace', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.removeCells(cells)
        }
      })

      // zoom
      this.graph.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = this.graph.zoom()
        if (zoom < 1.5) {
          this.graph.zoom(0.1)
        }
      })
      this.graph.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = this.graph.zoom()
        if (zoom > 0.5) {
          this.graph.zoom(-0.1)
        }
      })

      // #endregion
      // 控制连接桩显示/隐藏
      const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i = i + 1) {
          ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
      }
      this.graph.on('node:mouseenter', () => {
        const container = document.getElementById('graph-container')
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, true)
      })
      this.graph.on('node:mouseleave', () => {
        const container = document.getElementById('graph-container')
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })

      // #region 初始化图形  左边缩略页面
      const ports = {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
        },
        items: [
          {
            group: 'top',
          },
          {
            group: 'right',
          },
          {
            group: 'bottom',
          },
          {
            group: 'left',
          },
        ],
      }
    },
    methods: {
      delNode() {
        // this.graph.deleteCells();
        // this.showAttrConfig = false;
        const cells = this.graph.getSelectedCells()
        console.log(cells)
        if (cells.length) {
          this.graph.removeCells(cells)
        }
      },
      undo() {
        this.graph.undo()
      },
      redo() {
        this.graph.redo()
      },
      dragstart(e) {
        let target = e.target
        let data = {
          nodeType: target.getAttribute('nodeType'),
          name: target.innerText,
        }
        e.dataTransfer.setData('data', JSON.stringify(data))
      },
      drop(e) {
        this.createNode(e)
      },
      createNode(e) {
        let data = JSON.parse(e.dataTransfer.getData('data'))
        const container = this.$refs.x6Editor
        const pos = container.getBoundingClientRect()
        let nodeOp = {}
        let defaultOp = {
          width: 120,
          height: 60,
          data,
        }
        switch (data.nodeType) {
          case '1':
            nodeOp = Object.assign(defaultOp, {
              strokeOpacity: 1,
              shape: 'html',
              html: `<div class="default">
                      <span attr="name">${data.name}</div>
                  </div>`,
            })
            break
          case '2':
            nodeOp = Object.assign(defaultOp, {
              label: false,
              shape: 'html',
              html: `<div class="outer">
                      <span class="inner" attr="name">${data.name}</span>
                  </div>`,
            })
            break
          case '3':
            nodeOp = Object.assign(defaultOp, {
              width: 80,
              height: 80,
              label: false, // 不渲染 label
              // shape: 'ellipse',
              perimeter: 'ellipse',
              strokeOpacity: 0.01,
              // 提供 html 字符串或 HTMLElement 实例
              // label: data.name
              shape: 'html',
              html: `<div class="circular">
                     <span attr="name">${data.name}</span>
                  </div>`,
            })
            break
          default:
            break
        }
        let x = e.x - pos.left
        let y = e.y - pos.top
        if (x < 0 || y < 0) {
          return
        }
        let vt = graph.getView().translate
        let scale = graph.getView().getScale().toFixed(1)
        //scale为视图缩放倍数
        nodeOp.x = x / scale - nodeOp.width / 2 - vt.x
        nodeOp.y = y / scale - nodeOp.height / 2 - vt.y
        console.log(nodeOp)
        const world = this.graph.addNode(nodeOp)
      },
      scaleView(e, flag) {
        e.preventDefault()
        if (flag === 'up') {
          this.initScale += 0.1
        } else {
          this.initScale -= 0.1
        }
        graph.getView().setScale(this.initScale)
      },
      confirm() {
        this.selectedCell.size(this.form.width, this.form.height)
        this.selectedCell.position(this.form.x, this.form.y)
        this.showAttrConfig = false
      },
      delBgImg() {
        this.graph.clearBackground()
      },
      exportJson() {
        let json = this.graph.toJSON()
        console.log(json)
      },
      async uploadBgImg(file) {
        let _this = this
        const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png"
        if (!isJpgOrPng) {
          this.$message.error("支持png，jpg格式的图片")
          return false
        }
        const isLtM = file.size / 1024 / 1024 < 50
        if (!isLtM) {
          this.$message.error(`图片大小不超过${50}M`)
          return false
        }
        let res = await uploadImg(file)
        this.bgUrl = res._url
        this.graph.drawBackground({
          image: this.bgUrl + "",
        })
        this.loading = false
        return false
      },
      onClose() {
        this.showAttrConfig = false
      },
      changeStatus(value) {
        this.selectedCell.setData({
          status: value,
        })
      },
      changeText(e) {
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.text = e.target.value
        this.selectedCell.setData(data)
        console.log('json=========', this.graph.toJSON())
      },
      // 对齐方式
      changeAlain(value) {
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.style.textAlign = value
        this.selectedCell.setData(data)
      },
      // 更新颜色
      updatecolor(value) {
        let color = this.colorRGB2Hex(`rgb(${value.r},${value.g},${value.b})`)
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.style.color = color
        this.selectedCell.setData(data)
      },
      // 更新背景颜色
      updateBGcolor(value) {
        let color = this.colorRGB2Hex(`rgb(${value.r},${value.g},${value.b})`)
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.style.background = color
        this.selectedCell.setData(data)
      },
      // 更新行高
      changeLineHeight(e) {
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.style.lineHeight = e.target.value + 'px'
        this.selectedCell.setData(data)
      },
      // 更新文字大小
      changeFontSize(e) {
        let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
        data.style.fontSize = e.target.value + 'px'
        this.selectedCell.setData(data)
      },
      // 上传图片
      handleChange(info) {
        if (info.file.status === 'uploading') {
          this.loading = true
          return
        }
        if (info.file.status === 'done') {
          // Get this url from response in real world.
          // getBase64(info.file.originFileObj, (base64Url) => {
          //   imageUrl = base64Url
          //   this.loading = false
          // })
          let imageUrl = info.file.response.data.url
          let thisImageUrl = `${imageUrl}`
          this.form.imgUrl = thisImageUrl
          let data = JSON.parse(JSON.stringify(this.selectedCell.getData()))
          data.imgUrl = this.form.imgUrl
          this.selectedCell.setData(data)
          this.loading = false
        }

        if (info.file.status === 'error') {
          this.loading = false
          this.message.error('上传图片失败')
        }
      },
      // 将rgb颜色转成hex
      colorRGB2Hex(color) {
        var rgb = color.split(',')
        var r = parseInt(rgb[0].split('(')[1])
        var g = parseInt(rgb[1])
        var b = parseInt(rgb[2].split(')')[0])

        var hex =
          '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
        return hex
      },
      // 将hex颜色转成rgb
      hexToRgba(hex, opacity) {
        var RGBA =
          'rgba(' +
          parseInt('0x' + hex.slice(1, 3)) +
          ',' +
          parseInt('0x' + hex.slice(3, 5)) +
          ',' +
          parseInt('0x' + hex.slice(5, 7)) +
          ',' +
          opacity +
          ')'
        return {
          red: parseInt('0x' + hex.slice(1, 3)),
          green: parseInt('0x' + hex.slice(3, 5)),
          blue: parseInt('0x' + hex.slice(5, 7)),
          rgba: RGBA,
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  .editor-main {
    height: 100%;
    width: 100%;
  }
  .card-main {
    ::v-deep .ant-card-body {
      padding: 0;
    }
  }
  .x6-graph-background {
    background-size: 100% 100% !important;
  }
  .footer-buttons {
    text-align: center;
    margin-top: 10px;
  }
  .editor-com {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
  }
  .form-main {
    height: calc(100vh - 100px);
    overflow: auto;
  }
  .tool-bar {
    margin: 10px auto;
    height: 40px;
    width: 300px;
    // transform: translateX(-50%);
    display: flex;
    justify-content: space-evenly;
    border: 1px solid #555555;
    border-radius: 8px;
    align-items: center;
    i {
      font-size: 18px;
      color: #555555;
      font-weight: bold;
      cursor: pointer;
      &:hover {
        color: #4093ff;
      }
    }
  }
  #container {
    display: flex;
    // border: 1px solid #dfe3e8;
    width: 100%;
    width: 1626px;
    height: 755px;
  }
  #stencil {
    width: 250px;
    // height: 100%;
    position: relative;
    border-right: 1px solid #dfe3e8;
  }
  #graph-container {
    width: calc(100% - 180px);
    height: 100%;
    flex: 1;
  }
  .x6-widget-stencil {
    background-color: #fff;
  }
  .x6-widget-stencil-title {
    background-color: #fff;
  }
  .x6-widget-stencil-group-title {
    background-color: #fff !important;
  }
  .x6-widget-transform {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }
  .x6-widget-transform > div {
    border: 1px solid #239edd;
  }
  .x6-widget-transform > div:hover {
    background-color: #3dafe4;
  }
  .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }
  .x6-widget-transform-resize {
    border-radius: 0;
  }
  .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }
  .x6-widget-selection-box {
    opacity: 0;
  }
</style>
