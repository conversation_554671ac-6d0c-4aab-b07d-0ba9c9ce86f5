<template>
  <div>
    <div>
      系统名称：
      <a-input v-model:value="value" style="width: 200px" allowClear />
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <template v-if="editFalg">
        <a-button type="primary" style="margin: 0 10px 0 50px" @click="showModal(1, '')">
          新建
        </a-button>
        <a-button type="primary" danger @click="showDeleteConfirm('')">
          删除
        </a-button>
      </template>
    </div>
    <div style="margin-top: 20px">
      <a-table :columns="columns" :data-source="dataSource" bordered :row-selection="rowSelection" :pagination="false"
        :scroll="{ y: scrollHight }" :size="'small'">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <a type="link" @click="showModal(2, record)">编辑</a>
              <a type="link" danger @click="showDeleteConfirm(record)">删除</a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange"
          @pageChange="pageChange" />
      </div>
      <a-modal v-model:visible="visible" :footer="null" :title="`${modalType == 1 ? '新建' : '编辑'}系统类型`" @cancel="cancel">
        <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item ref="name" label="系统类型名称:" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入系统类型名称" :maxlength="10" />
          </a-form-item>
          <a-form-item label="系统类型ID:" name="code">
            <a-input v-model:value="formState.code" placeholder="请输入系统类型ID" :maxlength="50" />
          </a-form-item>
        </a-form>
        <div class="modalBtn">
          <template v-if="modalType === 1">
            <a-button type="primary" @click="onSubmit(1)">保存并新建</a-button>
          </template>
          <a-button type="primary" @click="onSubmit(2)">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
import {
  getlist,
  doEdit,
  doBatch,
  doAdd
} from '../../../api/dataDeal.js'
import Pagination from '../../../components/pagination'
import {
  defineComponent,
  ref,
  reactive,
  createVNode,
  onMounted,
  computed,
  toRaw,
} from 'vue'
import { Modal, message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
const columnsDefault = [
  {
    title: '系统类型名称',
    dataIndex: 'name',
    width: '300px',
  },
  {
    title: '系统类型编号',
    dataIndex: 'code',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '140px',
  },
]
const formRef = ref()
const formState = reactive({
  name: '',
  code: '',
})
const rules = {
  name: [
    {
      required: true,
      message: '请输入系统类型名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入系统类型编号',
      trigger: 'blur',
    },
  ],
}
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (RowKeys, selectedRows) => {
    selectedRowKeys.value = RowKeys
  }
}
export default defineComponent({
  name: 'system',
  components: { Pagination },
  setup() {
    const columns = ref([])
    const store = useStore()
    const visible = ref(false)
    const id = ref('')
    const total = ref(0)
    const dataSource = ref([])
    const value = ref('')
    let page = 1
    let size = 10
    let modalType = ref(1)
    const scrollHight = ref(0)
    const editFalg = ref(false)
    const status = reactive({
      roles: computed(() => store.getters['acl/role']),
    })
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 250
      startGetSystemList()

      let numbers = status.roles
      if (numbers.length > 0) {
        let result = numbers.find((item) => {
          return item == 'admin' || item == 'debugger'
        })
        editFalg.value = result ? true : false
        if (editFalg.value) {
          columns.value = columnsDefault
        } else {
          columns.value = columnsDefault.slice(0, columnsDefault.length - 1)
        }
      }
    })
    const getListByName = () => {
      page = 1
      startGetSystemList()
    }
    const startDelSystem = () => {
      doBatch('systems', { ids: selectedRowKeys.value }).then((res) => {
        selectedRowKeys.value = []
        startGetSystemList()
      })
    }
    const startGetSystemList = () => {
      let where = {}
      if (value.value) {
        where.name = value.value
      }
      getlist('systems', { page, limit: size, where: where }).then((res) => {
        let { data, pagination } = res
        dataSource.value = data.map(item => ({
          ...item,
          key: item.id || item._id
        }))
        page = pagination.page
        size = pagination.pageSize
        total.value = pagination.total
      })
    }
    const startUpdateSystem = () => {
      updateSystem(id.value, formState).then((res) => {
        visible.value = false
        startGetSystemList()
      })
    }
    const pageChange = (Page) => {
      page = Page
      startGetSystemList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page = Page
      size = pageSize
    }
    const showModal = (type, data) => {
      modalType.value = type
      if (type === 2) {
        formState.name = data.name
        formState.code = data.code
        id.value = data.key
      } else {
        formState.name = ''
        formState.code = ''
      }
      visible.value = true
    }
    const handleOk = (e) => {
      visible.value = false
    }
    const cancel = () => {
      formRef.value.resetFields()
      visible.value = false
    }
    const onSubmit = async (type) => {
      formRef.value
        .validate()
        .then(() => {
          if (modalType.value === 1) {
            doAdd('systems', formState).then((res) => {
              if (type == 2) {
                visible.value = false
              } else {
                formRef.value.resetFields()
              }
              startGetSystemList()
            })
          } else {
            doEdit('systems', id.value, formState).then((res) => {
              visible.value = false
              startGetSystemList()
            })
          }
        })
        .catch((error) => { })
    }
    const showDeleteConfirm = (data) => {
      if (data) {
        let idlist = []
        idlist.push(data.key)
        selectedRowKeys.value = idlist
      }
      Modal.confirm({
        title: '删除系统类型',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定删除选择的内容吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk() {
          startDelSystem()
        },
        onCancel() { },
      })
    }
    return {
      ...toRaw(status),
      selectedRowKeys,
      dataSource,
      columns,
      editingKey: '',
      cancel,
      value,
      rowSelection,
      showModal,
      handleOk,
      visible,
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
      formRef,
      formState,
      rules,
      onSubmit,
      showDeleteConfirm,
      modalType,
      page,
      size,
      total,
      pageChange,
      pageSizeChange,
      id,
      startGetSystemList,
      startUpdateSystem,
      startDelSystem,
      getListByName,
      editFalg,
      scrollHight,
    }
  },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
  margin-right: 8px;
}

.modalBtn {
  text-align: right;
  margin-top: 30px;

  .ant-btn+.ant-btn {
    margin-left: 10px;
  }
}
</style>
