<template>
  <div>
    <div>
      型号编号：
      <a-input v-model:value="typeDefCode" style="width: 200px" allowClear />
      &emsp;型号名称：
      <a-input v-model:value="typeDefName" style="width: 200px" allowClear />
      &emsp;品牌名称：
      <a-select v-model:value="brand" :options="brandList" show-search style="width: 200px"
        :filter-option="filterOption" allowClear :fieldNames="{ label: 'name', value: 'code' }"></a-select>
      &emsp;设备类型：
      <a-select v-model:value="deviceType" :options="deviceTypeList" show-search style="width: 200px"
        :filter-option="filterOption" allowClear :fieldNames="{ label: 'name', value: 'code' }"></a-select>
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <template v-if="editFalg">
        <a-button type="primary" style="margin: 0 10px 0 50px" @click="showModal(1, '')">
          新建
        </a-button>
        <a-button type="primary" danger @click="showDeleteConfirm('')">
          删除
        </a-button>
        <a-button type="primary" @click="addStandardMth" style="margin-left: 10px">添加到标准库</a-button>
      </template>
    </div>
    <div style="margin-top: 20px">
      <a-table :columns="columns" :data-source="dataSource" bordered :row-selection="rowSelection" :pagination="false"
        :scroll="{ y: scrollHight }" :size="'small'" rowKey="id" :loading="loading">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <a type="link" @click="showModal(2, record)">编辑</a>
              <a type="link" danger @click="showDeleteConfirm(record)">删除</a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </div>
    <div class="pagination">
      <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange" @pageChange="pageChange" />
    </div>
    <a-modal v-model:visible="visible" :footer="null" :title="`${modalType == 1 ? '新建' : '编辑'}型号`" width="750px"
      @cancel="cancel">
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-item label="设备类型:" name="deviceTypeCode">
          <a-select v-model:value="formState.deviceTypeCode" :options="deviceTypeOptions" show-search :filter-option="filterOption"
            @select="deviceTypeSelect" :fieldNames="{ label: 'name', value: 'code' }"></a-select>
        </a-form-item>
        <a-form-item label="所属品牌:" name="brandCode" v-show="formState.deviceTypeCode">
          <a-select v-model:value="formState.brandCode" :options="brandOptions" show-search :filter-option="filterOption"
            :fieldNames="{ label: 'name', value: 'code' }" @select="brandSelect"></a-select>
        </a-form-item>
        <a-form-item ref="name" label="型号名称:" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入型号名称" />
        </a-form-item>
        <a-form-item label="型号编号:" name="code">
          <a-input v-model:value="formState.code" placeholder="请输入型号编号" :maxlength="50" />
        </a-form-item>
        <a-form-item label="备注:" name="remarks">
          <a-textarea v-model:value="formState.remarks" showCount :maxlength="100" />
        </a-form-item>
      </a-form>
      <div class="modalBtn">
        <template v-if="modalType === 1">
          <a-button type="primary" @click="onSubmit(1)">保存并新建</a-button>
        </template>
        <a-button type="primary" @click="onSubmit(2)">保存</a-button>
        <a-button @click="cancel">取消</a-button>
      </div>
    </a-modal>
  </div>
</template>
<script>
import {
  getlist,
  doEdit,
  doBatch,
  doAdd
} from '../../../api/dataDeal.js'
import { getBrandList, addStandard, getModelsList } from '../../../api/products.js';
import TreeData from '../../../components/treeData.vue'
import Pagination from '../../../components/pagination'
import { defineComponent, ref, reactive, createVNode, onMounted, computed, toRaw } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
const columnsDefault = [
  {
    title: '型号编号',
    dataIndex: 'code',
    width: '100px',
  },
  {
    title: '型号名称',
    dataIndex: 'name',
    width: '300px',
  },
  {
    title: '所属品牌',
    dataIndex: 'brandName',
    width: '200px',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceTypeName',
    width: '200px',
  },
  {
    title: '备注',
    dataIndex: 'remarks',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '140px',
  },
]

const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (RowKeys, selectedRows) => {
    selectedRowKeys.value = RowKeys
  }
}
const formRef = ref()
const formState = reactive({
  name: '',
  code: '',
  deviceTypeCode: '',
  brandCode: '',
  remarks: '',
})
const rules = {
  name: [
    {
      required: true,
      message: '请输入型号名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入型号编号',
      trigger: 'blur',
    },
    {
      pattern: /^[A-Za-z][A-Za-z0-9_]*$/,
      message: '必须以字母开头，只能包含字母、数字和下划线',
      trigger: 'blur',
    }
  ],
  deviceTypeCode: [
    {
      required: true,
      message: '请选择设备类型',
      trigger: 'change',
    },
  ],
  brandCode: [
    {
      required: true,
      message: '请选择所属品牌',
      trigger: 'change',
    },
  ]
}
export default defineComponent({
  name: 'model',
  components: { Pagination, TreeData },
  setup() {
    const visible = ref(false)
    const scrollHight = ref(0)
    const brandList = ref()
    const deviceTypeList = ref()
    const store = useStore()
    const editFalg = ref(false)
    const columns = ref([])
    const status = reactive({
      roles: computed(() => store.getters['acl/role']),
    })
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 250
      getlist('brands', { page: 1, limit: 1000000 }).then((res) => {
        let { data } = res
        brandList.value = data
      })
      getlist('device_types', { page: 1, limit: 1000000 }).then((res) => {
        let { data } = res
        deviceTypeList.value = data
      })
      startGetList()

      let numbers = status.roles
      if (numbers.length > 0) {
        let result = numbers.find((item) => {
          return item == 'admin' || item == 'debugger'
        })
        editFalg.value = result ? true : false
        console.log(editFalg.value)
        if (editFalg.value) {
          columns.value = columnsDefault
        } else {
          columns.value = columnsDefault.slice(0, columnsDefault.length - 1)
        }
      }
    })
    const filterOption = (input, option) => {
      return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
    const loading = ref(false)
    const page = ref(1)
    const size = ref(10)
    const total = ref(0)
    const id = ref('')
    const deviceTypeOptions = ref()
    const brandOptions = ref()
    const dataSource = ref()
    const typeDefName = ref('')
    const typeDefCode = ref('')
    const brand = ref('')
    const deviceType = ref('')
    const getListByName = () => {
      page.value = 1
      startGetList()
    }
    const addStandardMth = () => {
      if (selectedRowKeys.value.length === 0) {
        message.warning('请选择要添加到标准库的型号')
        return
      }
      addStandard({ ids: selectedRowKeys.value }).then((res) => {
          message.success('添加到标准库成功')
      })
    }
    const startGetAllDeviceType = () => {
      getlist('device_types', { page: 1, limit: 1000 }).then((res) => {
        let { data } = res
        deviceTypeOptions.value = data
      })
    }
    const deviceTypeSelect = (value, option) => {
      formState.brandCode = ''
      formState.deviceType = option.name
      startGetBrandList([value])
    }
    const brandSelect = (_value, option) => {
      formState.brand = option.name
    }
    const startGetBrandList = (value) => {
      getBrandList({ page: 1, limit: 1000000, deviceTypeCode: value }).then((res) => {
        let { data } = res
        brandOptions.value = data
      })
    }
    const pageChange = (Page) => {
      page.value = Page
      startGetList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
      startGetList()
    }
    let modalType = ref(1)
    const showModal = (type, data) => {
      startGetAllDeviceType()
      modalType.value = type
      if (type === 2) {
        formState.name = data.name
        formState.code = data.code
        formState.deviceType = data.deviceType
        formState.deviceTypeCode = [data.deviceTypeCode]
        startGetBrandList(formState.deviceTypeCode)
        formState.brand = data.brand
        formState.brandCode = data.brandCode
        formState.remarks = data.remarks
        id.value = data.id
      } else {
        formState.name = ''
        formState.code = ''
        formState.deviceType = ''
        formState.deviceTypeCode = ''
        formState.brandCode = ''
        formState.brand = ''
        formState.remarks = ''
        id.value = ''
      }
      visible.value = true
    }

    const cancel = () => {
      formRef.value.resetFields()
      visible.value = false
    }
    const startGetList = () => {
      loading.value = true
      let where = {}
      if (typeDefName.value) {
        where.name = typeDefName.value
      }
      if (typeDefCode.value) {
        where.code = typeDefCode.value
      }
      if (brand.value) {
        where.brandCode = brand.value
      }
      if (deviceType.value) {
        where.deviceTypeCode = deviceType.value
      }
      getModelsList({
        page: page.value,
        limit: size.value,
        where: where
      }).then((res) => {
        let { data, pagination } = res
        total.value = pagination.total
        dataSource.value = data
        loading.value = false
      })
    }
    const startDel = () => {
      doBatch('models', { ids: selectedRowKeys.value }).then((res) => {
        selectedRowKeys.value = []
        startGetList()
      })
    }
    const onSubmit = async (type) => {
      formRef.value
        .validate()
        .then(() => {
          if (modalType.value === 2) {
            doEdit('models', id.value, formState).then((res) => {
              visible.value = false
              formRef.value.resetFields()
              startGetList()
            })
          } else {
            doAdd('models', formState).then((res) => {
              if (type == 2) {
                visible.value = false
              }
              formRef.value.resetFields()
              startGetList()
            })
          }
        })
    }
    const showDeleteConfirm = (data) => {
      if (data) {
        let idlist = []
        idlist.push(data.id)
        selectedRowKeys.value = idlist
      }
      Modal.confirm({
        title: '删除系统类型',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定删除选择的内容吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',

        onOk() {
          startDel()
        },

        onCancel() {
          // console.log('Cancel');
        },
      })
    }
    return {
      dataSource,
      columns,
      editingKey: '',
      cancel,
      typeDefName,
      rowSelection,
      showModal,
      visible,
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
      formRef,
      formState,
      rules,
      onSubmit,
      showDeleteConfirm,
      modalType,
      page,
      size,
      total,
      pageChange,
      pageSizeChange,
      id,
      deviceTypeOptions,
      brandOptions,
      startGetBrandList,
      deviceTypeSelect,
      startGetList,
      startDel,
      selectedRowKeys,
      getListByName,
      scrollHight,
      brandList,
      deviceTypeList,
      filterOption,
      deviceType,
      brand,
      editFalg,
      ...toRaw(status),
      brandSelect,
      typeDefCode,
      addStandardMth,
      loading
    }
  },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
  margin-right: 8px;
}

.modalBtn {
  text-align: right;
  margin-top: 30px;

  .ant-btn+.ant-btn {
    margin-left: 10px;
  }
}
</style>
