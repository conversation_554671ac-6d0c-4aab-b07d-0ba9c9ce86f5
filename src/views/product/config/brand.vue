<template>
  <div>
    <div>
      品牌名称：
      <a-input v-model:value="value" style="width: 200px" allowClear />
      &emsp;设备类型：
      <a-select v-model:value="selectedItems" show-search mode="multiple" style="width: 530px" :options="OPTIONS"
        :field-names="{ label: 'name', value: 'code' }"  :filter-option="filterOption"></a-select>
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <template v-if="editFalg">
        <a-button type="primary" style="margin: 0 10px 0 50px" @click="showModal(1, '')">
          新建
        </a-button>
        <a-button type="primary" danger @click="showDeleteConfirm('')">
          删除
        </a-button>
      </template>
    </div>
    <div style="margin-top: 20px">
      <a-table :columns="columns" :data-source="dataSource" bordered :row-selection="rowSelection" :pagination="false"
        :scroll="{ y: scrollHight }" :size="'small'" rowKey="id" :loading="loading">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <a type="link" @click="showModal(2, record)">编辑</a>
              <a type="link" danger @click="showDeleteConfirm(record)">删除</a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </div>
    <div class="pagination">
      <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange" @pageChange="pageChange" />
    </div>
    <a-modal v-model:visible="visible" :footer="null" :title="`${modalType == 1 ? '新建' : '编辑'}品牌`" @cancel="cancel"
      width="750px">
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-item ref="name" label="品牌名称:" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入品牌名称" :maxlength="10" />
        </a-form-item>
        <a-form-item label="品牌编号:" name="code">
          <a-input v-model:value="formState.code" placeholder="请输入品牌编号" :maxlength="50" />
        </a-form-item>
        <a-form-item label="字母简称:" name="scode">
          <a-input v-model:value="formState.scode" placeholder="请输入字母简称" :maxlength="50" />
        </a-form-item>
        <a-form-item label="关联设备类型:" name="deviceId">
          <a-button type="primary" @click="showDeviceTypeModal" size="small" style="margin-right: 10px">新增</a-button>
          <div style="margin-top: 10px">
            <a-tag  style="margin: 0 5px 5px 0" v-for="(item, index) in formState.deviceTypeName" :key="index" closable @close="handleClose(item, index)">{{item}}</a-tag>
          </div>
        </a-form-item>
      </a-form>
          <!-- <TreeSelect v-model:value="formState.deviceTypeCode" @treeSelectChange="treeSelectChange"></TreeSelect> -->

      <div class="modalBtn">
        <template v-if="modalType === 1">
          <a-button type="primary" @click="onSubmit(1)">保存并新建</a-button>
        </template>
        <a-button type="primary" @click="onSubmit(2)">保存</a-button>
        <a-button @click="cancel">取消</a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="deviceTypeVisible" 
      title="选择设备类型" 
      @ok="handleDeviceTypeOk" 
      @cancel="handleDeviceTypeCancel"
      width="60%">
      <div style="margin-bottom: 16px">
        <a-space>
          设备类型名称：
          <a-input v-model:value="deviceTypeSearchName" placeholder="请输入设备类型名称" style="width: 200px" allowClear />
          &emsp;系统名称：
          <a-input v-model:value="deviceTypeSearchSysName" placeholder="请输入系统名称" style="width: 200px" allowClear />
          <a-button type="primary" @click="searchDeviceType">查询</a-button>
        </a-space>
      </div>
      <a-table 
        :columns="deviceTypeColumns" 
        :data-source="deviceTypeList" 
        :row-selection="deviceTypeRowSelection"
        :pagination="false"
        size="small"
        rowKey="code"
        :scroll="{ y: 400 }"
        bordered>
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'name'">
            {{ text }}
          </template>
          <template v-if="column.dataIndex === 'code'">
            {{ text }}
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination 
          :page="deviceTypePage" 
          :size="deviceTypeSize" 
          :total="deviceTypeTotal" 
          @pageSizeChange="deviceTypePageSizeChange" 
          @pageChange="deviceTypePageChange" 
        />
      </div>
    </a-modal>
  </div>
</template>
<script>
import {
  getlist,
  doEdit,
  doBatch,
  doAdd
} from '../../../api/dataDeal.js'
import { getBrandList, addAndUpdateBrand } from '../../../api/products.js';
import TreeSelect from '../../../components/treeSelect.vue'
import Pagination from '../../../components/pagination'
import {
  defineComponent,
  ref,
  reactive,
  createVNode,
  onMounted,
  computed,
  toRaw,
} from 'vue'
import { Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
const columnsDefault = [
  {
    title: '品牌编号',
    dataIndex: 'code',
    width: '100px',
  },
  {
    title: '品牌名称',
    dataIndex: 'name',
    width: '300px',
  },
  {
    title: '关联设备类型',
    dataIndex: 'deviceTypeNames'
  },
  {
    title: '字母简称',
    dataIndex: 'scode',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '140px',
  },
]
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (RowKeys, selectedRows) => {
    selectedRowKeys.value = RowKeys
  }
}
const formRef = ref()
const formState = reactive({
  name: '',
  code: '',
  scode: '',
  deviceTypeCode: [],
  deviceTypeName: [],
  
})
const rules = {
  name: [
    {
      required: true,
      message: '请输入品牌名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入品牌ID',
      trigger: 'blur',
    },
  ],
  deviceTypeCode: [
    {
      required: true,
      message: '请选择关联设备类型',
      type: 'array',
    },
  ],
}
export default defineComponent({
  name: 'brand',
  components: { Pagination, TreeSelect },
  setup() {
    const loading = ref(false)
    const page = ref(1)
    const size = ref(10)
    const total = ref(0)
    const id = ref('')
    const visible = ref(false)
    const modalType = ref(1)
    const selectedItems = ref([])
    const OPTIONS = ref([])
    const pageChange = (Page) => { 
      page.value = Page
      startGetList()
    }
    const handleClose = (item, index) => {
      formState.deviceTypeName.splice(index, 1)
      formState.deviceTypeCode.splice(index, 1)
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
    }
    const showModal = (type, data) => {
      modalType.value = type
      if (type === 2) {
        if(data.deviceTypeNames) {
          let deviceTypeName = data.deviceTypeNames.split(',')  
          formState.deviceTypeName = deviceTypeName
        }

        if(data.deviceTypeCode) {
          let deviceTypeCode = data.deviceTypeCode.split(',') 
          formState.deviceTypeCode = deviceTypeCode
        }
        formState.name = data.name
        formState.code = data.code
        formState.scode = data.scode  
        id.value = data.id
      } else {
        formState.name = ''
        formState.scode = ''
        formState.code = ''
        formState.deviceTypeCode = []
        formState.deviceTypeName = []
        id.value = ''
      }
        visible.value = true
    }
    const dataSource = ref()
    const value = ref('')
    const deviceTypeCode = ref('')
    const cancel = () => {
      formRef.value.resetFields()
      visible.value = false
    }
    const scrollHight = ref(0)

    const store = useStore()
    const editFalg = ref(false)
    const columns = ref([])
    const status = reactive({
      roles: computed(() => store.getters['acl/role']),
    })
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 250
      startGetList()
      let numbers = status.roles
      if (numbers.length > 0) {
        let result = numbers.find((item) => {
          return item == 'admin' || item == 'debugger'
        })
        editFalg.value = result ? true : false
        if (editFalg.value) {
          columns.value = columnsDefault
        } else {
          columns.value = columnsDefault.slice(0, columnsDefault.length - 1)
        }
      }
    })
    const treeSelectChange = (value, label) => {
      formState.deviceTypeCode = value
      formState.deviceTypeName = label
    }
    const getListByName = () => {
      let deviceStr = []
      Object.values(selectedItems.value).forEach((res) => {
        deviceStr.push(res)
      })
      deviceTypeCode.value = deviceStr
      page.value = 1
      startGetList()
    }
    const startGetList = () => {
      loading.value = true
      let name = null
      let typeCode = null
      if(value.value) {
        name = value.value
      }
      if(deviceTypeCode.value.length > 0) {
        typeCode = deviceTypeCode.value
      }
      getBrandList({
        page: page.value,
        limit: size.value,
        deviceTypeCode: typeCode,
        name
      }).then((res) => {
        let { data, pagination } = res
        total.value = pagination.total
        dataSource.value = data
        loading.value = false
      })
    }
    const onSubmit = async (type) => {
      formRef.value
        .validate()
        .then(() => {
          let deviceTypeCode = formState.deviceTypeCode.join(',')
          let deviceTypeName = formState.deviceTypeName.join(',')
          if (modalType.value === 2) {
            addAndUpdateBrand({
              id: id.value,
              name: formState.name,
              code: formState.code,
              scode: formState.scode,
              deviceTypeCode: deviceTypeCode,
              deviceTypeName: deviceTypeName
            }).then((res) => {
              visible.value = false
              startGetList()
            })
          } else {
            addAndUpdateBrand({
              name: formState.name,
              code: formState.code,
              scode: formState.scode,
              deviceTypeCode: deviceTypeCode,
              deviceTypeName: deviceTypeName
            }).then((res) => {
              if (type == 2) {
                visible.value = false
              }
              startGetList()
            })
          } 
        })
    }
    const filterOption = (input, option) => {
      return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
    const startDel = () => {
      doBatch('brands',{ids:selectedRowKeys.value}).then((res) => {
        startGetList()
      })
    }
    const showDeleteConfirm = (data) => {
      if (data) {
        let idlist = []
        idlist.push(data.id)
        selectedRowKeys.value = idlist
      }
      Modal.confirm({
        title: '删除系统类型',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定删除选择的内容吗？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',

        onOk() {
          startDel()
        },

        onCancel() {
        },

      })
    }
    const deviceTypeVisible = ref(false)
    const selectedDeviceTypes = ref([])
    const selectedDeviceKeys = ref([])
    const deviceTypeRowSelection = {
      selectedRowKeys: selectedDeviceKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        selectedDeviceKeys.value = selectedRowKeys
        selectedDeviceTypes.value = selectedRows.map(row => row.name)
      }
    }
    const deviceTypePage = ref(1)
    const deviceTypeSize = ref(10)
    const deviceTypeTotal = ref(0)
    const deviceTypeList = ref([])
    const deviceTypeSearchName = ref('')
    const deviceTypeSearchSysName = ref('')
    const getDeviceTypeList = () => {
      let params = {
        page: deviceTypePage.value,
        limit: deviceTypeSize.value,
      }
      let where = {}
      if (deviceTypeSearchName.value) {
        where.name = deviceTypeSearchName.value
      }
      if (deviceTypeSearchSysName.value) {
        where.sysName = deviceTypeSearchSysName.value
      }
      params.where = where

      return getlist('device_types', params).then((res) => {
        deviceTypeList.value = res.data
        deviceTypeTotal.value = res.pagination.total
      })
    }
    const deviceTypePageChange = (page) => {
      deviceTypePage.value = page
      getDeviceTypeList().then(() => {
        if (formState.deviceTypeCode && formState.deviceTypeCode.length > 0) {
          const selectedItems = deviceTypeList.value.filter(item => 
            formState.deviceTypeCode.includes(item.code)
          )
          
          const currentPageKeys = selectedItems.map(item => item.code)
          selectedDeviceKeys.value = [...new Set([...selectedDeviceKeys.value, ...currentPageKeys])]
          
          const currentPageTypes = selectedItems.map(item => item.name)
          selectedDeviceTypes.value = [...new Set([...selectedDeviceTypes.value, ...currentPageTypes])]
        }
      })
    }
    const deviceTypePageSizeChange = (page, size) => {
      deviceTypePage.value = page
      deviceTypeSize.value = size
    }
    const showDeviceTypeModal = () => {
      deviceTypeVisible.value = true
      deviceTypePage.value = 1
      deviceTypeSearchName.value = ''
      deviceTypeSearchSysName.value = ''
      
      selectedDeviceKeys.value = []
      selectedDeviceTypes.value = []

      getDeviceTypeList().then(() => {
        if (formState.deviceTypeCode && formState.deviceTypeCode.length > 0) {
          const selectedItems = deviceTypeList.value.filter(item => 
            formState.deviceTypeCode.includes(item.code)
          )
          selectedDeviceKeys.value = selectedItems.map(item => item.code)
        }
      })
    }
    const handleDeviceTypeOk = () => {
      // 获取当前选中的项
      const selectedItems = deviceTypeList.value.filter(item => 
        selectedDeviceKeys.value.includes(item.code)
      )
      
      // 遍历选中项,如果code不在formState中就添加
      selectedItems.forEach(item => {
        const index = formState.deviceTypeCode.indexOf(item.code)
        if (index === -1) {
          formState.deviceTypeCode.push(item.code)
          formState.deviceTypeName.push(item.name)
        }
      })

      // 处理取消选中的项
      const currentPageCodes = deviceTypeList.value.map(item => item.code)
      formState.deviceTypeCode = formState.deviceTypeCode.filter((code, index) => {
        // 如果code在当前页面但未被选中,则移除
        if (currentPageCodes.includes(code) && !selectedDeviceKeys.value.includes(code)) {
          formState.deviceTypeName.splice(index, 1)
          return false
        }
        return true
      })
      
      deviceTypeVisible.value = false
    }
    const handleDeviceTypeCancel = () => {
      deviceTypeVisible.value = false
      selectedDeviceKeys.value = []
      selectedDeviceTypes.value = []
    }
    const deviceTypeColumns = [
      {
        title: '设备类型名称',
        dataIndex: 'name',
      },
      {
        title: '设备类型编号',
        dataIndex: 'code',
      },
      {
        title: '系统名称',
        dataIndex: 'sysName',
      }
    ]
    const searchDeviceType = () => {
      deviceTypePage.value = 1
      getDeviceTypeList().then(() => {
        if (formState.deviceTypeCode && formState.deviceTypeCode.length > 0) {
          const selectedItems = deviceTypeList.value.filter(item => 
            formState.deviceTypeCode.includes(item.code)
          )
          selectedDeviceKeys.value = selectedItems.map(item => item.code)
        }
      })
    }
    return {
      dataSource,
      columns,
      cancel,
      value,
      rowSelection,
      showModal,
      visible,
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
      formRef,
      formState,
      rules,
      onSubmit,
      showDeleteConfirm,
      modalType,
      page,
      size,
      total,
      pageChange,
      pageSizeChange,
      id,
      selectedRowKeys,
      getListByName,
      deviceTypeCode,
      scrollHight,
      OPTIONS,
      selectedItems,
      editFalg,
      ...toRaw(status),
      treeSelectChange,
      startDel,
      filterOption,
      loading,
      handleClose,
      deviceTypeVisible,
      deviceTypeColumns,
      deviceTypeRowSelection,
      selectedDeviceTypes,
      selectedDeviceKeys,
      showDeviceTypeModal,
      handleDeviceTypeOk,
      handleDeviceTypeCancel,
      deviceTypePage,
      deviceTypeSize,
      deviceTypeTotal,
      deviceTypeList,
      deviceTypePageChange,
      deviceTypePageSizeChange,
      deviceTypeSearchName,
      deviceTypeSearchSysName,
      searchDeviceType,
    }
  },
})
</script>
<style scoped lang="less">
.editable-row-operations a {
  margin-right: 8px;
}

.modalBtn {
  text-align: right;
  margin-top: 30px;

  .ant-btn+.ant-btn {
    margin-left: 10px;
  }
}
</style>
