<template>
  <div>
    <div>
      设备类型名称：
      <a-input v-model:value="value" style="width: 200px" allowClear />
      &emsp;所属系统：
      <a-input v-model:value="systemValue" style="width: 200px" allowClear />
      &emsp;设备类型编号：
      <a-input v-model:value="codeValue" style="width: 200px" allowClear />
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <template v-if="editFalg">
        <a-button
          type="primary"
          style="margin: 0 10px 0 50px"
          @click="showModal(1, '')"
        >
          新建
        </a-button>
        <a-button type="primary" danger @click="showDeleteConfirm('')">
          删除
        </a-button>
      </template>
    </div>
    <div style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        bordered
        :row-selection="rowSelection"
        :pagination="false"
        :scroll="{ y: scrollHight }"
        :size="'small'"
        :loading="loading"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <a type="link" @click="showModal(2, record)">编辑</a>
              <a type="link" danger @click="showDeleteConfirm(record)">删除</a>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination
          :page="page"
          :size="size"
          :total="total"
          @pageSizeChange="pageSizeChange"
          @pageChange="pageChange"
        />
      </div>
      <a-modal
        v-model:visible="visible"
        :footer="null"
        :title="`${modalType == 1 ? '新建' : '编辑'}设备类型`"
        @cancel="cancel"
        width="750px"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
        >
          <a-form-item ref="name" label="设备类型名称:" name="name">
            <a-input
              v-model:value="formState.name"
              placeholder="请输入设备类型名称"
              :maxlength="10"
            />
          </a-form-item>
          <a-form-item label="设备类型编号:" name="code">
            <a-input
              v-model:value="formState.code"
              placeholder="请输入设备类型编号"
              :maxlength="50"
            />
          </a-form-item>
          <a-form-item label="字母简称:" name="scode">
            <a-input
              v-model:value="formState.scode"
              placeholder="请输入字母简称"
              :maxlength="10"
            />
          </a-form-item>
          <a-form-item label="所属系统:" name="sysCode">
            <a-select
              v-model:value="formState.sysCode"
              :options="options"
              @change="sysNameChange"
            ></a-select>
          </a-form-item>
          <div class="upload-container">
            <div class="upload-item">
              <div class="upload-label">类型图片:</div>
              <div style="color: #1890ff; margin: 5px 0 7px">
                支持图片格式：png、jpg、jpeg
              </div>
              <Upload
                :imageObj="formState.typePic"
                @imageUpChange="typePicChange"
              ></Upload>
            </div>
            <div class="upload-item">
              <div class="upload-label">正常默认:</div>
              <div style="color: #1890ff; margin: 5px 0 7px">
                支持图片格式：png、jpg、jpeg
              </div>
              <Upload
                :imageObj="formState.normalPic"
                @imageUpChange="normalPicChange"
              ></Upload>
            </div>
          </div>
          <div class="upload-container">
            <div class="upload-item">
              <div class="upload-label">正常选中:</div>
              <div style="color: #1890ff; margin: 5px 0 7px">
                支持图片格式：png、jpg、jpeg
              </div>
              <Upload
                :imageObj="formState.normalSelectedPic"
                @imageUpChange="normalSelectedPicChange"
              ></Upload>
            </div>
            <div class="upload-item">
              <div class="upload-label">告警默认:</div>
              <div style="color: #1890ff; margin: 5px 0 7px">
                支持图片格式：png、jpg、jpeg
              </div>
              <Upload
                :imageObj="formState.alarmPic"
                @imageUpChange="alarmPicChange"
              ></Upload>
            </div>
          </div>
          <div class="upload-container">
            <div class="upload-item">
              <div class="upload-label">告警选中:</div>
              <div style="color: #1890ff; margin: 5px 0 7px">
                支持图片格式：png、jpg、jpeg
              </div>
              <Upload
                :imageObj="formState.alarmPicSelectedPic"
                @imageUpChange="alarmPicSelectedPicChange"
              ></Upload>
            </div>
          </div>
        </a-form>
        <div class="modalBtn">
          <template v-if="modalType === 1">
            <a-button type="primary" @click="onSubmit(1)">保存并新建</a-button>
          </template>
          <a-button type="primary" @click="onSubmit(2)">保存</a-button>
          <a-button @click="cancel">取消</a-button>
        </div>
      </a-modal>
    </div>
  </div>
</template>
<script>
  import { getlist, doBatch } from '../../../api/dataDeal.js'
  import { addDeviceType } from '../../../api/devices.js'
  import Pagination from '../../../components/pagination'
  import Upload from '../../../components/upload.vue'
  import {
    defineComponent,
    ref,
    reactive,
    createVNode,
    onMounted,
    toRaw,
    computed,
  } from 'vue'
  import { Modal, message } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { useStore } from 'vuex'
  const columnsDefault = [
    {
      title: '设备类型名称',
      dataIndex: 'name',
      width: '300px',
    },
    {
      title: '所属系统',
      dataIndex: 'sysName',
      width: '200px',
    },
    {
      title: '设备类型编号',
      dataIndex: 'code',
    },
    {
      title: '字母简称',
      dataIndex: 'scode',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '140px',
    },
  ]

  const selectedRowKeys = ref([])
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (RowKeys, _selectedRows) => {
      selectedRowKeys.value = RowKeys
    },
  }
  const formRef = ref()
  const options = ref()
  const rules = {
    name: [
      {
        required: true,
        message: '请输入设备类型名称',
        trigger: 'blur',
      },
    ],
    code: [
      {
        required: true,
        message: '请输入设备类型编号',
        trigger: 'blur',
      },
    ],
    sysName: [
      {
        required: true,
        message: '所属系统不能为空',
      },
    ],
  }
  export default defineComponent({
    name: 'typeDef',
    components: { Pagination, Upload },
    setup() {
      const formState = reactive({
        id: null,
        name: '',
        code: '',
        sysName: '',
        scode: '',
        sysCode: '',
        typePic: '',
        normalPic: '',
        normalSelectedPic: '',
        alarmPic: '',
        alarmPicSelectedPic: '',
      })
      const loading = ref(false)
      const id = ref('')
      const dataSource = ref([])
      const value = ref('')
      const systemValue = ref('')
      const codeValue = ref('')
      const imageUrl = ref()
      const previewVisible = ref(false)
      const visible = ref(false)
      const store = useStore()
      const editFalg = ref(false)
      const columns = ref([])
      const status = reactive({
        roles: computed(() => store.getters['acl/role']),
      })
      let modalType = ref(1)
      const page = ref(1)
      const size = ref(10)
      const total = ref(0)
      const scrollHight = ref(0)
      onMounted(() => {
        let o = document.getElementById('vab-content')
        let h = o.clientHeight || o.offsetHeight
        scrollHight.value = h - 280
        startGetDeviceTypeList()

        let numbers = status.roles
        if (numbers.length > 0) {
          let result = numbers.find((item) => {
            return item == 'admin' || item == 'debugger'
          })
          editFalg.value = result ? true : false
          if (editFalg.value) {
            columns.value = columnsDefault
          } else {
            columns.value = columnsDefault.slice(0, columnsDefault.length - 1)
          }
        }
      })

      const typePicChange = (obj) => {
        formState.typePic = obj
      }

      const normalPicChange = (obj) => {
        formState.normalPic = obj
      }

      const normalSelectedPicChange = (obj) => {
        formState.normalSelectedPic = obj
      }

      const alarmPicChange = (obj) => {
        formState.alarmPic = obj
      }

      const alarmPicSelectedPicChange = (obj) => {
        formState.alarmPicSelectedPic = obj
      }

      const getListByName = () => {
        page.value = 1
        startGetDeviceTypeList()
      }
      const pageChange = (Page) => {
        page.value = Page
        startGetDeviceTypeList()
      }
      const pageSizeChange = (Page, pageSize) => {
        page.value = Page
        size.value = pageSize
      }
      const startGetDeviceTypeList = () => {
        loading.value = true
        let where = {}
        if (value.value) {
          where.name = value.value
        }
        if (systemValue.value) {
          where.sysName = systemValue.value
        }
        if (codeValue.value) {
          where.code = codeValue.value
        }
        getlist('device_types', {
          page: page.value,
          limit: size.value,
          where: where,
        }).then((res) => {
          let { data, pagination } = res
          dataSource.value = data.map((item) => ({
            ...item,
            key: item.id || item._id,
          }))
          page.value = pagination.page
          size.value = pagination.pageSize
          total.value = pagination.total
          loading.value = false
        })
      }
      const startDelDeviceType = () => {
        doBatch('device_types', { ids: selectedRowKeys.value }).then((res) => {
          selectedRowKeys.value = []
          startGetDeviceTypeList()
        })
      }
      const startGetSystemList = () => {
        getlist('systems', { page: 1, limit: 1000 }).then((res) => {
          let { data } = res
          options.value = data.map((item) => ({
            label: item.name,
            value: item.code,
          }))
        })
      }
      const showModal = (type, data) => {
        visible.value = true
        modalType.value = type
        startGetSystemList()
        if (type === 2) {
          formState.scode = data.scode
          formState.name = data.name
          formState.code = data.code
          formState.sysName = data.sysName
          formState.sysCode = data.sysCode
          id.value = data.key
          formState.typePic = data.typePic
          formState.normalPic = data.normalPic
          formState.normalSelectedPic = data.normalSelectedPic
          formState.alarmPic = data.alarmPic
          formState.alarmPicSelectedPic = data.alarmPicSelectedPic
        } else {
          formState.scode = ''
          formState.name = ''
          formState.code = ''
          formState.sysName = ''
          formState.typePic = null
          formState.normalPic = null
          formState.normalSelectedPic = null
          formState.alarmPic = null
          formState.alarmPicSelectedPic = null
        }
      }
      const handleOk = (e) => {
        visible.value = false
      }
      const cancel = () => {
        formRef.value.resetFields()
        visible.value = false
      }
      const onSubmit = async (type) => {
        formRef.value
          .validate()
          .then(async () => {
            if (modalType.value === 2) {
              formState.id = id.value
              await addDeviceType(formState)
            } else {
              await addDeviceType(formState)
            }
            visible.value = false
            startGetDeviceTypeList()
          })
          .catch((error) => {})
      }
      const sysNameChange = (_value, option) => {
        formState.sysCode = option.value
        formState.sysName = option.label
      }
      const showDeleteConfirm = (data) => {
        if (data) {
          let idlist = []
          idlist.push(data.key)
          selectedRowKeys.value = idlist
        }
        Modal.confirm({
          title: '删除系统类型',
          icon: createVNode(ExclamationCircleOutlined),
          content: '确定删除选择的内容吗？',
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',

          onOk() {
            startDelDeviceType()
          },

          onCancel() {},
        })
      }
      return {
        imageUrl,
        previewVisible,
        dataSource,
        columns,
        editingKey: '',
        cancel,
        value,
        rowSelection,
        showModal,
        handleOk,
        visible,
        labelCol: {
          span: 6,
        },
        wrapperCol: {
          span: 15,
        },
        formRef,
        formState,
        rules,
        onSubmit,
        showDeleteConfirm,
        modalType,
        options,
        startGetSystemList,
        startGetDeviceTypeList,
        page,
        size,
        total,
        pageChange,
        pageSizeChange,
        getListByName,
        selectedRowKeys,
        systemValue,
        codeValue,
        scrollHight,
        typePicChange,
        normalPicChange,
        normalSelectedPicChange,
        alarmPicChange,
        alarmPicSelectedPicChange,
        ...toRaw(status),
        editFalg,
        sysNameChange,
        loading,
      }
    },
  })
</script>
<style scoped lang="less">
  .editable-row-operations a {
    margin-right: 8px;
  }

  .modalBtn {
    text-align: right;
    margin-top: 30px;

    .ant-btn + .ant-btn {
      margin-left: 10px;
    }
  }
  .upload-container {
    display: flex;
    gap: 20px;
    width: 100%;
    padding-left: 120px;
    .upload-item {
      flex: 1;
    }
  }
</style>
