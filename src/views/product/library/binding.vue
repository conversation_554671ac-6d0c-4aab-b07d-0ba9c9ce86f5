<template>
  <div class="gutter-example">
    <div>
      分类选择
      <a-select v-model:value="categoryValue" style="width: 220px">
        <template v-for="(item, index) in categoryOptions" :key="index">
          <a-select-option :value="item.categoryValue">
            {{ item.categoryKey }}
          </a-select-option>
        </template>
      </a-select>
      <a-button type="primary" style="margin-left: 30px" @click="search">

        
        查询
      </a-button>
      <a-button type="primary" style="margin-left: 30px" @click="binding">
        绑定
      </a-button>
      <a-button type="primary" style="margin-left: 30px" @click="toAddScreen">
        添加大屏
      </a-button>
    </div>
    <div style="margin-top: 20px">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        bordered
        :pagination="false"
        :loading="tableLoading"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
        }"
        rowKey="id"
		    :scroll="{ y: scrollHight }"
        :size="'small'"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'backgroundUrl'">
            <div>
              <a-image :width="50" :src="record.backgroundUrl" />
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </div>
    <div class="pagination">
      <Pagination
        :page="page"
        :size="size"
        :total="total"
        @pageSizeChange="pageSizeChange"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>
<script>
import { message } from 'ant-design-vue'
import Pagination from '../../../components/pagination'
// import { bindingDetailPage, isDetailPageExisted } from '../../../api/product.js'
import {
  defineComponent,
  reactive,
  ref,
  toRaw,
  onMounted,
  watch,
  toRefs,
} from 'vue'
import { useRoute } from 'vue-router'
const columns = [
  {
    title: '缩略图',
    dataIndex: 'backgroundUrl',
    width: '100px',
  },
  {
    title: '名称',
    dataIndex: 'title',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
]
import axios from 'axios'
export default defineComponent({
  name: 'CollectionRule',
  components: { Pagination },
  setup() {
    let page = 1
    let size = 10
    const total = ref(0)
    const model = useRoute().query.model
    const categoryOptions = ref('')
    const categoryValue = ref('')
    const httpapi = axios.create({
      baseURL: 'http://*************:48050',
    })
    const tableLoading = ref(false)
    const dataSource = ref()
    const formState = ref({})
    const state = reactive({
      selectedRowKeys: [],
      selectedRow: [],
      // Check here to configure the default column
    })
    const scrollHight = ref(0)
    onMounted(() => {
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 190
      httpapi.get('/blade-visual/category/list').then((res) => {
        categoryOptions.value = res.data.data
        getList()
      })
    })
    const search = () => {
      page = 1
      getList()
    }
    const getList = () => {
      tableLoading.value = true
      
      httpapi
        .get('/blade-visual/visual/list', {
          params: { category: categoryValue.value, current: page, size: size },
        })
        .then((res) => {
          tableLoading.value = false
          total.value = res.data.data.total
          dataSource.value = res.data.data.records
        })
    }
    const pageChange = (Page) => {
      page = Page
      state.selectedRowKeys = []
      getList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page = Page
      size = pageSize
      state.selectedRowKeys = []
      getList()
    }
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys
      state.selectedRow = selectedRows
    }
    // 绑定详情页
    const binding = async () => {
      // for (let item of state.selectedRow) {
      //   let params = toRaw(item)
      //   let isExist = await isDetailPageExisted(item.id, model)
      //   if (isExist) {
      //   } else {
      //     await bindingDetailPage(params, model)
      //   }
      // }
      message.success('绑定成功')
    }
    const toAddScreen = () => {
        let url = `http://*************:8899/`
        if (window.require) {
          const shell = window.require('electron').shell
          shell.openExternal(url)
        } else {
          window.open(url, '_blank')
        }
      }
    return {
      model,
      page,
      size,
      binding,
      onSelectChange,
      ...toRefs(state),
      total,
      pageChange,
      pageSizeChange,
      search,
      getList,
      categoryOptions,
      categoryValue,
      httpapi,
      tableLoading,
      formState,
      dataSource,
      columns,
      scrollHight,
      toAddScreen
    }
  },
})
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}
.gutter-row {
  margin-bottom: 50px;
}
</style>
