<template>
  <div class="gutter-example">
    <div class="gutter-row">
      <div>设备类型：{{ formState.deviceTypeName }}</div>&emsp;
      <div>所属品牌：{{ formState.brandName }}</div>&emsp;
      <div>所属系统：{{ formState.sysName }}</div>&emsp;
      <div>型号名称：{{ formState.modelName }}</div>&emsp;
    </div>
    <div>
      查询参数：<a-input v-model:value="paramName" style="width: 200px" allowClear />
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <a-table :columns="columns" :data-source="dataSource" bordered :pagination="false" :loading="tableLoading"
        :scroll="{ y: scrollHight }" :size="'small'">
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'serialNumber'">
            {{ index + 1 }}
          </template>
          <template v-else-if="
            [
              'paramName',
              'tx',
              'dataUnit',
              'dataCalib',
              'dataStartPos',
              'dataValidLen',
            ].includes(column.dataIndex)
          ">
            <div>
              <a-input v-if="editableData[record.id]"
                v-model:value="editableData[record.id][column.dataIndex]" style="margin: -5px 0" />
              <template v-else>
                {{ text }}
              </template>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.id]">
                <a-typography-link @click="save(record)">
                  保存
                </a-typography-link>
                <a-popconfirm title="确认取消?" @confirm="cancel(record)">
                  <a>取消</a>
                </a-popconfirm>
              </span>
              <span v-else>
                <a @click="edit(record)">编辑</a>
              </span>
            </div>  
          </template>
        </template>
      </a-table>
      <div class="pagination">
        <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange"
          @pageChange="pageChange" />
      </div>
    </div>
  </div>
</template>
<script>
import { getlist, doEdit } from '../../../api/dataDeal.js'
import { getDetail } from '../../../api/products.js'
import { cloneDeep } from 'lodash-es'
import Pagination from '../../../components/pagination'
import { defineComponent, reactive, ref, toRaw, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
const columns = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
    width: '50px'
  },
  {
    title: '参数名称',
    dataIndex: 'paramName',
  },
  {
    title: '参数ID',
    dataIndex: 'paramCode',
  },
  {
    title: '参数类型',
    dataIndex: 'paramDataType',
  },
  {
    title: '引用ID',
    dataIndex: 'refParamCode',
  },
  {
    title: '发送串',
    dataIndex: 'tx',
  },
  {
    title: '计算公式',
    dataIndex: 'dataCalib',
  },
  {
    title: '单位',
    dataIndex: 'dataUnit',
  },
  {
    title: '起始位',
    dataIndex: 'dataStartPos',
  },
  {
    title: '截取长度',
    dataIndex: 'dataValidLen',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '120px',
  },
]
export default defineComponent({
  name: 'CollectionRule',
  components: { Pagination },
  setup() {
    const tableLoading = ref(false)
    const dataSource = ref()
    const total = ref(0)
    const page = ref(1)
    const size = ref(10)
    const formState = ref({
      deviceTypeName: '',
      brandName: '',
      sysName: '',
      modelName: '',
    })
    const id = useRoute().query.key

    const scrollHight = ref(0)
    onMounted(() => {
      getDetailMethod()
      let o = document.getElementById('vab-content')
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 200
      tableLoading.value = true
      getlist('productattributes', {
        where: {
          productId: id
        }
      }).then((res) => {
        let { data, pagination } = res
        dataSource.value = data
        total.value = pagination.total
        tableLoading.value = false
      })
    })
    const paramName = ref('')
    const editableData = reactive({})
    const edit = (record) => {
      let id = record.id
      editableData[id] = cloneDeep(
        dataSource.value.filter((item) => id === item.id)[0]
      )
    }
    const getDetailMethod = () => {
      getDetail(id).then((res) => {
        formState.value = res
      })
    }
    const save = (record) => {
      let id = record.id
      Object.assign(
        dataSource.value.filter((item) => id === item.id)[0],
        editableData[id]
      )
      doEdit('productattributes', id, editableData[id]).then((res) => {
        delete editableData[id]
        message.success('保存成功')
      })
    }

    const cancel = (record) => {
      let id = record.id
      delete editableData[id]
    }
    const pageChange = (Page) => {
      page.value = Page
      startGetList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
    }
    const getListByName = () => {
      page.value = 1
      startGetList()
    }
    const startGetList = () => {
      tableLoading.value = true
      let where = {
        productId: id,
      }
      if (paramName.value) {
        where.paramName = paramName.value
      }
      getlist('productattributes', {
        limit: size.value,
        page: page.value,
        where: where,
      }).then((res) => {
        let { data, pagination } = res
        dataSource.value = data
        total.value = pagination.total
        tableLoading.value = false
      })
    }
    return {
      tableLoading,
      formState,
      dataSource,
      columns,
      editableData,
      edit,
      save,
      cancel,
      scrollHight,
      pageChange,
      pageSizeChange,
      page,
      size,
      total,
      getDetailMethod,
      paramName,
      getListByName
    }
  },
})
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}

.gutter-row {
  display: flex;
}
</style>
