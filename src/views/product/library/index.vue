<template>
  <div>
    <div>
      型号名称：<a-input v-model:value.trim="searchParams.name" style="width: 200px" allowClear />
      &emsp;所属系统：<a-input v-model:value.trim="searchParams.system" style="width: 200px" allowClear />
      &emsp;所属品牌：<a-input v-model:value.trim="searchParams.brand" style="width: 200px" allowClear />
      &emsp;设备类型名称：<a-input v-model:value.trim="searchParams.deviceType" style="width: 200px" allowClear />
      <a-button type="primary" style="margin-left: 10px" @click="getListByName">
        查询
      </a-button>
      <a-button type="primary" danger @click="showDeleteConfirm('')" style="margin: 0 10px">
        删除
      </a-button>
    </div>
    <div style="margin-top: 20px">
      <a-table :columns="columns" :data-source="dataSource" bordered :row-selection="rowSelection" :pagination="false"
        :loading="tableLoading" :scroll="{ y: scrollHight }" :size="'small'" :row-key="'id'">
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'serialNumber'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span><a @click="openPage(1, record)">采集规则</a></span>
              <a-dropdown>
                <!-- <a class="ant-dropdown-link" @click.prevent>
                  详情页
                  <DownOutlined />
                </a> -->
                <template #overlay>
                  <a-menu @click="(key) => onClick(key, record)">
                    <a-menu-item key="3">
                      <a href="javascript:;">详情页列表</a>
                    </a-menu-item>
                    <a-menu-item key="4">
                      <a href="javascript:;">绑定详情页</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <span>
                <a class="font-danger" @click="showDeleteConfirm(record)">
                  删除
                </a>
              </span>
            </div>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <!-- 				</div>
			</div> -->
    </div>
    <div class="pagination">
      <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange" @pageChange="pageChange" />
    </div>
    <a-modal v-model:visible="delOpen" title="删除操作" @ok="handleDelOk">
      <p>确定删除选择的内容吗？</p>
    </a-modal>
  </div>
</template>
<script>
import { getProductList, delProduct } from '../../../api/products.js'
import { DownOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import Pagination from '../../../components/pagination'
import { defineComponent, ref, reactive, onMounted } from 'vue'
const columns = [
{
    title: '序号',  
    dataIndex: 'serialNumber',
    width: '50px'
  },
  {
    title: '型号名称',
    dataIndex: 'modelName',
  },
  {
    title: '所属品牌',
    dataIndex: 'brandName',
  },
  {
    title: '设备类型名称',
    dataIndex: 'deviceTypeName',
  },
  {
    title: '所属系统',
    dataIndex: 'sysName',
  },
  {
    title: '参数个数',
    dataIndex: 'attributeCount',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '220px',
  },
]
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (RowKeys, selectedRows) => {
    selectedRowKeys.value = RowKeys
    console.log(selectedRowKeys.value)
  },
}
const formRef = ref()
const formState = reactive({
  name: '',
  code: '',
  system: '',
  brand: '',
  remarks: '',
})
const rules = {
  name: [
    {
      title: '型号名称',
      dataIndex: 'name',
      width: '350px',
    },
    {
      title: '所属品牌',
      dataIndex: 'brand',
    },
  ],
}

export default defineComponent({
  name: 'ProductLibrary',
  components: { Pagination, DownOutlined },
  setup() {
    const delOpen = ref(false)
    const tableLoading = ref(false)
    const id = ref('')
    const total = ref(0)
    const searchParams = ref({
      name: '',
      system: '',
      brand: '',
      deviceType: ''
    })
    const page = ref(1)
    const size = ref(10)
    const dataSource = ref([])
    const startGetList = () => {
      tableLoading.value = true
      let where = {}
      if (searchParams.value.name) {
        where.modelName = searchParams.value.name
      }
      if (searchParams.value.system) {
        where.sysName = searchParams.value.system
      }
      if (searchParams.value.brand) {
        where.brandName = searchParams.value.brand
      }
      if (searchParams.value.deviceType) {
        where.deviceTypeName = searchParams.value.deviceType
      }
      getProductList({
        page: page.value,
        limit: size.value,
        where
      }).then(async (res) => {
        let { data, pagination } = res
        dataSource.value = data
        total.value = pagination.total
        tableLoading.value = false
      })
    }
    const getListByName = () => {
      page.value = 1
      startGetList()
    }
    const scrollHight = ref(0)
    onMounted(() => {
      let o = document.getElementById("vab-content")
      let h = o.clientHeight || o.offsetHeight
      scrollHight.value = h - 220
      startGetList()
    })
    const visible = ref(false)
    const router = useRouter()
    const imageUrl = ref(
      'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png'
    )
    const previewVisible = ref(false)
    let modalType = ref(1)
    const pageChange = (Page) => {
      page.value = Page
      startGetList()
    }
    const pageSizeChange = (Page, pageSize) => {
      page.value = Page
      size.value = pageSize
    }
    const showModal = (type, data) => {
      modalType.value = type
      if (type === 2) {
        formState.name = data.name
        formState.code = data.code
      } else {
        formState.name = ''
        formState.code = ''
      }
      visible.value = true
    }
    const handleOk = (e) => {
      visible.value = false
    }
    const cancel = () => {
      formRef.value.resetFields()
      visible.value = false
    }
    const onSubmit = (type) => {
      formRef.value
        .validate()
        .then(() => {
          if (type == 2) {
            visible.value = false
          }
          formRef.value.resetFields()
        })
        .catch((error) => {
        })
    }
    const startDel = () => {
      delProduct({ ids: selectedRowKeys.value }).then((res) => {
        selectedRowKeys.value = []
        delOpen.value = false
        startGetList()
      })
    }
    const showDeleteConfirm = (data) => {
      if (data) {
        let idlist = []
        idlist.push(data.id)
        selectedRowKeys.value = idlist
      }
      delOpen.value = true
    }
    const handleDelOk = () => {
      startDel()
    }
    const openPage = (type, data) => {
      let name = ''
      let key = ''
      let model = ''
      switch (type) {
        case 1:
        case '1':
          name = 'CollectionRule'
          key = data.id
          break
        case 2:
        case '2':
          name = 'ProductAdd'
          break
        case 3:
        case '3':
          name = 'CollectionDevice'
          key = data.key
          model = data.model
          break
        case 4:
        case '4':
          name = 'bindingDevice'
          key = data.key
          model = data.model
          break

        default:
          break
      }
      router.push({ name, query: { key } })
    }
    const onClick = ({ key }, record) => {
      openPage(key, record)
    }
    return {
      tableLoading,
      openPage,
      imageUrl,
      previewVisible,
      dataSource,
      columns,
      editingKey: '',
      cancel,
      searchParams,
      rowSelection,
      visible,
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 15,
      },
      formRef,
      formState,
      rules,
      onSubmit,
      showDeleteConfirm,
      page,
      size,
      total,
      pageChange,
      pageSizeChange,
      id,
      startGetList,
      getListByName,
      startDel,
      selectedRowKeys,
      onClick,
      scrollHight,
      delOpen,
      handleDelOk
    }
  },
})
</script>
<style scoped lang="less">
.font-primary {
  color: #1890ff;
}

.font-danger {
  color: #ff4d4f;
}

.editable-row-operations a {
  margin-right: 8px;
}
</style>
