<template>
	<div class="gutter-example">
		<div style="margin-top: 20px;">
			<a-table :columns="columns" :data-source="dataSource" bordered :pagination="false" :loading="tableLoading" rowKey="id">
				<template #bodyCell="{ column, text, record }">
					<template v-if="column.dataIndex === 'backgroundUrl'">
						<div>
							<a-image
								:width="50"
								:src="record.backgroundUrl"
							/>
						</div>
					</template>
					<template v-else>
						{{ text }}
					</template>
				</template>
			</a-table>
		</div>
		<div class="pagination"><Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange" @pageChange="pageChange" /></div>
	</div>
</template>
<script>
import Pagination from '../../../components/pagination';
import { defineComponent, reactive, ref, toRaw, onMounted, watch, toRefs } from 'vue';
import { useRoute } from 'vue-router';
const columns = [
	{
		title: '缩略图',
		dataIndex: 'backgroundUrl',
		width: '100px',

	},
	{
		title: '名称',
		dataIndex: 'title',

	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
	},
	{
		title: '更新时间',
		dataIndex: 'updateTime',
	}
	// {
	// 	title: '操作',
	// 	dataIndex: 'operation',
	// 	slots: {
	// 		customRender: 'operation'
	// 	}
	// }
];
export default defineComponent({
	components: { Pagination },
	setup() {
		let page = 1;
		let size = 10;
		const total = ref(0);
		const model = useRoute().query.model;
		const categoryOptions = ref('');
		const categoryValue = ref('');
		const tableLoading = ref(false);
		const dataSource = ref();
		const state = reactive({
		  selectedRowKeys: [],
		  selectedRow: []
		  // Check here to configure the default column
		});
		onMounted(() => {
			search();
		});
		const search = () => {
			tableLoading.value = true;
			getDetailPageById(model,size,page).then(res => {
				tableLoading.value = false;
				total.value = res.count;
				let list = res.results;
				let newlist = [];
				for (let item of list) {
					let param = item.attributes;
					newlist.push({backgroundUrl:param.backgroundUrl,title:param.title,createTime:param.createTime,updateTime:param.updateTime,id:item.id})
				}
				dataSource.value = newlist
			});
		}
		const pageChange = Page => {
			page = Page;
			state.selectedRowKeys = [];
			search();
		};
		const pageSizeChange = (Page, pageSize) => {
			page = Page;
			size = pageSize;
			state.selectedRowKeys = [];
			search();
		};
		return {
			model,
			page,
			size,
			...toRefs(state),
			total,
			pageChange,
			pageSizeChange,
			search,
			categoryOptions,
			categoryValue,
			tableLoading,
			dataSource,
			columns
		};
	}
});
</script>
<style scoped>
.editable-row-operations a {
	margin-right: 8px;
}
.gutter-row {
	margin-bottom: 50px;
}
</style>
