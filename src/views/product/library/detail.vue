<template>
  <div class="gutter-example">
    <a-form
      ref="formRef"
      :model="d"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="8">
          <a-form-item label="系统名称:" name="system">
            <a-select
              v-model:value="d.system"
              :options="systemOptions"
              @select="systemSelected"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="8" v-show="d.system">
          <a-form-item label="设备类型:" name="deviceType">
            <a-select
              v-model:value="d.deviceType"
              :options="deviceTypeOptions"
              @select="deviceTypeSelected"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="8" v-show="d.deviceType">
          <a-form-item label="所属品牌:" required name="brand">
            <a-select
              v-model:value="d.brand"
              :options="brandOptions"
              @select="brandSelected"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="8">
          <a-form-item ref="name" label="产品名称:" name="name">
            <a-input v-model:value="d.name" />
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="8">
          <a-form-item label="协议类型:">
            <a-select
              v-model:value="d.protocolType"
              :options="protocolTypeOptions"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="8" v-show="d.brand">
          <a-form-item label="型号名称:" name="model">
            <a-select
              v-model:value="d.model"
              :options="modelOptions"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12">
          <a-form-item ref="name" label="采集规则:">
            <a-button type="link">导入Execl文件</a-button>
          </a-form-item>
        </a-col>
        <a-col class="gutter-row" :span="12">
          <div class="editable-row-operations">
            <a-button
              type="primary"
              style="margin-right: 20px"
              @click="onSubmit"
            >
              保存
            </a-button>
            <a-button @click="resetForm">取消</a-button>
          </div>
        </a-col>
      </a-row>
    </a-form>
    <div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        bordered
        :pagination="false"
      >
        <template
          v-for="col in ['refParamCode', 'nameRef', 'unit']"
          #[col]="{ text, record }"
          :key="col"
        >
          <div>
            <a-input
              v-if="editableData[record.key]"
              v-model:value="editableData[record.key][col]"
              style="margin: -5px 0"
            />
            <template v-else>
              {{ text }}
            </template>
          </div>
        </template>
        <template #operation="{ record }">
          <div class="editable-row-operations">
            <span v-if="editableData[record.key]">
              <a @click="save(record.key)">保存</a>
              <a-popconfirm title="确定取消吗?" @confirm="cancel(record.key)">
                <a>取消</a>
              </a-popconfirm>
            </span>
            <span v-else><a @click="edit(record.key)">编辑</a></span>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script>
  import { cloneDeep } from 'lodash-es'
  import { defineComponent, reactive, ref, toRaw, onMounted, watch } from 'vue'
  const columns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      slots: {
        customRender: 'name',
      },
    },
    {
      title: '参数ID',
      dataIndex: 'id',
      slots: {
        customRender: 'id',
      },
    },
    // {
    // 	title: '参数类型',
    // 	dataIndex: 'type',
    // 	slots: {
    // 		customRender: 'type'
    // 	}
    // },
    {
      title: '单位',
      dataIndex: 'calcResultUnit',
      width: '100px',
      slots: {
        customRender: 'calcResultUnit',
      },
    },
    {
      title: '公共参数标识',
      dataIndex: 'isMasterId',
      width: '130px',
      slots: {
        customRender: 'isMasterId',
      },
    },
    {
      title: '发送串',
      dataIndex: 'tx',
      slots: {
        customRender: 'tx',
      },
    },
    {
      title: '引用ID',
      dataIndex: 'refParamCode',
      slots: {
        customRender: 'refParamCode',
      },
    },
    {
      title: '计算公式',
      dataIndex: 'calc',
      slots: {
        customRender: 'calc',
      },
    },
    {
      title: '起始位',
      dataIndex: 'startPos',
      slots: {
        customRender: 'startPos',
      },
    },
    {
      title: '截取长度',
      dataIndex: 'truncLength',
      slots: {
        customRender: 'truncLength',
      },
    },
    // {
    // 	title: '操作',
    // 	dataIndex: 'operation',
    // 	slots: {
    // 		customRender: 'operation'
    // 	}
    // }
  ]
  const data = [
    {
      key: '1',
      name: '通讯状态',
      id: 'Ups_Status_Comm',
      type: 'CommStatus',
      tx: '01040000000271CB',
      refParamCode: '',
      calc: 'if(length=9,0,1)',
      startPos: 1,
      truncLength: 9,
      calcResultValue: undefined,
      isMasterId: '是',
      calcResultUnit: 'kwh',
    },
    {
      key: '2',
      name: '空气温度',
      id: 'HT_Temperature',
      type: 'Analogy',
      tx: '',
      refParamCode: 'Ups_Status_Comm',
      calc: 'A$(1,4)*0.1',
      startPos: 7,
      truncLength: 4,
      calcResultValue: undefined,
      isMasterId: '是',
      calcResultUnit: '℃',
    },
    {
      key: '3',
      name: '空气湿度',
      id: 'HT_Humidity',
      type: 'Analogy',
      tx: '',
      refParamCode: 'Ups_Status_Comm',
      calc: 'A$(1,4)*0.1',
      startPos: 11,
      truncLength: 4,
      calcResultValue: undefined,
      isMasterId: '是',
      calcResultUnit: '%',
    },
  ]

  export default defineComponent({
    name: '',
    setup() {
      const dataSource = ref(data)
      const editableData = reactive({})

      const edit = (key) => {
        editableData[key] = cloneDeep(
          dataSource.value.filter((item) => key === item.key)[0]
        )
      }

      const save = (key) => {
        Object.assign(
          dataSource.value.filter((item) => key === item.key)[0],
          editableData[key]
        )
        delete editableData[key]
      }

      const cancel = (key) => {
        delete editableData[key]
      }
      const systemOptions = ref()
      onMounted(() => {
        getSystemList().then((res) => {
          let data = []
          let list = res
          for (let item of list) {
            data.push({
              label: item.attributes.name,
              value: item.attributes.name,
            })
          }
          systemOptions.value = data
        })
      })
      const deviceTypeOptions = ref()
      const systemSelected = (value) => {
        getDeviceType({ system: value }).then((res) => {
          let data = []
          let list = res
          for (let item of list) {
            data.push({
              label: item.attributes.name,
              value: item.attributes.name,
            })
          }
          deviceTypeOptions.value = data
        })
      }
      const brandOptions = ref()
      const deviceTypeSelected = (value) => {
        getBrandList({ deviceType: value }).then((res) => {
          let data = []
          let list = res
          for (let item of list) {
            data.push({
              label: item.attributes.name,
              value: item.attributes.name,
            })
          }
          brandOptions.value = data
        })
      }
      const modelOptions = ref()
      const brandSelected = (value) => {
        getModelList({ brand: value }).then((res) => {
          let data = []
          let list = res
          for (let item of list) {
            data.push({
              label: item.attributes.name,
              value: item.attributes.name,
            })
          }
          modelOptions.value = data
        })
      }
      const formRef = ref()
      const d = reactive({
        system: '',
        deviceType: '',
        brand: '',
        model: '',
        name: '',
        protocolType: 'Modbus_RTU',
        rules: [],
      })
      watch(
        () => d.system,
        (newValue, oldValue) => {
          d.deviceType = ''
        },
        // watch 刚被创建的时候不执行
        { lazy: true }
      )
      watch(
        () => d.deviceType,
        (newValue, oldValue) => {
          d.brand = ''
        },
        // watch 刚被创建的时候不执行
        { lazy: true }
      )
      watch(
        () => d.brand,
        (newValue, oldValue) => {
          d.model = ''
        },
        // watch 刚被创建的时候不执行
        { lazy: true }
      )
      const protocolTypeOptions = ref([
        {
          value: 'Modbus_RTU',
        },
        {
          value: 'Modbus_TCP',
        },
        {
          value: 'Http',
        },
        {
          value: 'BacNel',
        },
        {
          value: 'Snmp',
        },
      ])
      const rules = {
        name: [
          {
            required: true,
            message: '请输入产品名称',
            trigger: 'blur',
          },
        ],
        system: [
          {
            required: true,
            message: '请选择所属系统',
            trigger: 'change',
          },
        ],
        brand: [
          {
            required: true,
            message: '请选择所属品牌',
            trigger: 'change',
          },
        ],
        deviceType: [
          {
            required: true,
            message: '请选择设备类型',
            trigger: 'change',
          },
        ],
        model: [
          {
            required: true,
            message: '请选择设备型号',
            trigger: 'change',
          },
        ],
      }

      const onSubmit = () => {
        formRef.value
          .validate()
          .then(() => {
            d.rules = data
            createProduct(d).then((res) => {
              // console.log(res)
            })
          })
          .catch((error) => {
            console.log('error', error)
          })
      }

      const resetForm = () => {
        formRef.value.resetFields()
      }

      return {
        formRef,
        labelCol: {
          style: { width: '100px' },
        },
        wrapperCol: {
          span: 14,
        },
        other: '',
        d,
        rules,
        onSubmit,
        resetForm,
        protocolTypeOptions,
        systemOptions,
        deviceTypeOptions,
        systemSelected,
        brandOptions,
        deviceTypeSelected,
        modelOptions,
        brandSelected,
        dataSource,
        columns,
        editingKey: '',
        editableData,
        edit,
        save,
        cancel,
      }
    },
  })
</script>
<style scoped>
  .editable-row-operations a {
    margin-right: 8px;
  }
</style>
