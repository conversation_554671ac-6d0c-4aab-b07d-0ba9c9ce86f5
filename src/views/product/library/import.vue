<template>
  <div>
    <a-row :gutter="16">
      <a-col :span="24">
        <p style="color: red; font-weight: bold">
          提示：导入产品规则时，请先到
          <span style="color: blue">【型号管理】</span>
          中查找，如果有型号，直接选中后点击
          <span style="color: green">【添加到标准库】</span>
          ，如果没有型号，请先添加型号，然后点击
          <span style="color: green">【添加到标准库】</span>
        </p>
        <a-button type="primary" @click="import_data">导入产品规则</a-button>
      </a-col>
    </a-row>
    <a-row style="margin-top: 20px" :gutter="16">
      <a-col :span="12">
        型号名称：
        <a-input
          v-model:value.trim="modelName"
          placeholder="型号名称"
          style="width: 400px; margin-right: 8px"
          allowClear
        />
        <a-button type="primary" @click="getProductListByModelName">获取产品参数</a-button>
        <a-button type="primary" @click="exportExcel" style="margin-left: 8px">导出产品参数</a-button>
      </a-col>
      <a-col :span="12">
        <a-button type="primary" @click="clearLog">清空日志</a-button>
      </a-col>
    </a-row>
    <div
      style="
        height: 60vh;
        overflow-y: auto;
        background: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        margin-top: 20px;
      "
      id="log"
    >
      <div
        v-for="(log, idx) in logs"
        :key="'log_' + idx"
        :style="{ color: log.isError ? 'red' : 'black', marginBottom: '5px' }"
      >
        {{ log.text }}
      </div>
    </div>
  </div>
</template>

<script>
  import { updateProductRules, getProductListByModelName } from '../../../api/products'
  import { message } from 'ant-design-vue'
  import { convertExcelToStandard, convertStandardToExcel, getExcelHeader } from '../../../config/parameterMapping'
  import { saveAs } from 'file-saver'
  var XLSX = require('xlsx')

  export default {
    data() {
      return {
        workbook: [],
        logs: [],
        systemTypeMap: {},
        projectCode: '',
        modelName: 'ATS_Schneider_WATSG100_630',
        parameterList: [],
      }
    },
    mounted() {},
    methods: {
      clearLog() {
        this.logs = []
      },
      getProductListByModelName() {
        getProductListByModelName({
          modelName: this.modelName,
        }).then((res) => {
          if(res.length <= 0) {
            this.appendLog('没有查询到产品参数', true)
            return
          }
          this.appendLog('获取产品参数成功')
          this.parameterList = res
        })
      },
      exportExcel() {
        if (!this.parameterList || this.parameterList.length <= 0) {
          message.error('请先获取产品参数')
          return
        }

        const wb = XLSX.utils.book_new()

        // 添加 Devhead sheet
        const devheadHeaders = [
          '设备类型ID',
          '设备类型名称',
          '品牌ID',
          '品牌名称',
          '型号ID',
          '型号名称',
        ]
        const firstItem = this.parameterList[0]
        const devheadData = [
          devheadHeaders,
          [
            firstItem.deviceTypeCode || '',
            firstItem.deviceTypeName || '',
            firstItem.brandCode || '',
            firstItem.brandName || '',
            firstItem.modelCode || '',
            firstItem.modelName || '',
          ]
        ]
        const wsDevhead = XLSX.utils.aoa_to_sheet(devheadData)
        
        // 设置列宽
        wsDevhead['!cols'] = devheadHeaders.map(() => ({ width: 15 }))
        
        XLSX.utils.book_append_sheet(wb, wsDevhead, 'Devhead')

        // 添加 Devvou sheet
        const headers = getExcelHeader()
        const data = [headers]
        
        this.parameterList.forEach(item => {
          const excelRow = convertStandardToExcel(item)
          const rowData = headers.map(header => excelRow[header] || '')
          data.push(rowData)
        })

        const wsDevvou = XLSX.utils.aoa_to_sheet(data)
        wsDevvou['!cols'] = headers.map(() => ({ width: 15 }))

        XLSX.utils.book_append_sheet(wb, wsDevvou, 'Devvou')

        // 写入选项
        const wopts = {
          bookType: 'xlsx',
          type: 'binary'
        }

        const wbout = XLSX.write(wb, wopts)
        
        // 转换为 Blob 并保存
        const buf = new ArrayBuffer(wbout.length)
        const view = new Uint8Array(buf)
        for (let i = 0; i != wbout.length; ++i) {
          view[i] = wbout.charCodeAt(i) & 0xFF
        }
        const blob = new Blob([buf], { type: 'application/octet-stream' })
        
        saveAs(blob, `${this.modelName}.xlsx`)
        this.appendLog(`已导出产品参数模板：${this.modelName}.xlsx`)
      },
      appendLog(log, isError = false) {
        const logEntry = {
          text: log,
          isError: isError,
        }
        this.logs.push(logEntry)
        this.$nextTick(() => {
          const elem = document.getElementById('log')
          elem.scrollTop = elem.scrollHeight
        })
      },
      import_data() {
        this.importSuccessLogged = false
        const input = document.createElement('input')
        input.type = 'file'
        input.addEventListener('change', this.parse_imported_data)
        input.click()
      },
      async parse_imported_data(e) {
        this.appendLog('开始导入...')
        const files = e.target.files
        if (files.length <= 0) {
          this.appendLog('请上传文件', true)
          return false
        } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
          this.appendLog('上传格式不正确，请上传xls或者xlsx格式', true)
          return false
        }

        const fileReader = new FileReader()
        fileReader.onload = async (ev) => {
          try {
            const data = new Uint8Array(ev.target.result)
            this.workbook = XLSX.read(data, { type: 'array' })
            await this.process_productSheet(this.workbook)
          } catch (error) {
            this.appendLog('文件格式错误', true)
          }
        }
        fileReader.readAsArrayBuffer(files[0])
      },
      async process_productSheet(workbook) {
        const productParams = XLSX.utils.sheet_to_json(
          workbook.Sheets.Devhead,
          {}
        )
        if (productParams.length <= 0) {
          this.appendLog('文档缺少sheet页：Devhead', true)
          return false
        }
        const modelId = productParams[0]['型号ID']
        let productDevhead = XLSX.utils.sheet_to_json(
          workbook.Sheets.Devvou,
          {}
        )

        // 使用 convertExcelToStandard 转换数据
        productDevhead = productDevhead.map((item) =>
          convertExcelToStandard(item)
        )

        // 检查重复参数ID和格式
        const checkDuplicateParamCodes = (productDevhead) => {
          const paramCodeMap = new Map()
          const paramCodeRegex = /^[A-Za-z][A-Za-z0-9_]*$/
          
          for (let i = 0; i < productDevhead.length; i++) {
            const item = productDevhead[i]
            let paramCode = item.paramCode

            // 检查参数ID格式
            if (!paramCodeRegex.test(paramCode)) {
              this.appendLog(
                `参数ID格式错误: ${paramCode}，位于第 ${i + 2} 行。参数ID必须以字母开头，只能包含字母、数字和下划线`,
                true
              )
              return false
            }

            // 检查重复参数ID
            if (paramCodeMap.has(paramCode)) {
              this.appendLog(
                `发现重复的参数ID: ${paramCode}，位于第 ${
                  paramCodeMap.get(paramCode) + 2
                } 行和第 ${i + 2} 行`,
                true
              )
              return false
            }
            paramCodeMap.set(paramCode, i)
          }
          return true
        }

        this.appendLog('开始导入产品参数...')
        if (!checkDuplicateParamCodes(productDevhead)) {
          this.appendLog('导入终止：存在重复的参数ID', true)
          return false
        }

        let devvouList = []
        let count = 0
        if (productDevhead && productDevhead.length > 0) {
          for (let j = 0; j < productDevhead.length; j++) {
            let item = productDevhead[j]
            count++
            this.appendLog(`查询到产品参数${item.paramName}，等待录入...`)
            devvouList.push(item)
          }
        }

        if (devvouList.length > 0) {
          updateProductRules({
            modelId,
            devvouList,
          }).then((res) => {
            this.appendLog(res.message)
          }).catch((err) => {
            this.appendLog(err.message, true)
          })
        } else {
          this.appendLog('没有查询到产品参数', true)
        }
      },
    },
  }
</script>
