<template>
  <div>
    <a-button @click="import_data">导入Excel标准库</a-button>
    <div
      style="height: 70vh; overflow: scroll; background: white; padding: 5px"
      id="log"
    >
      <div
        v-for="(log, idx) in logs"
        v-bind:key="'log_' + idx"
        style="color: black"
      >
        {{ log }}
      </div>
    </div>
  </div>
</template>

<script>
  import { doAdd, doExists, getlist } from '../../api/dataDeal.js'

  var XLSX = require('xlsx')

  export default {
    data() {
      return {
        workbook: [],
        systemTypeMap: {},
        deviceTypeMap: {},
        brandMap: {},
        productMap: {},
        systemTypeSheet: undefined,
        deviceTypeSheet: undefined,
        brandSheet: undefined,
        modelSheet: undefined,
        logs: [],
      }
    },
    mounted() {},
    methods: {
      appendLog(log, isError = false) {
        // 创建日志条目
        const logEntry = {
          text: log,
          isError: isError,
        }
        // 添加到日志列表
        this.logs.push(logEntry)
        // 使用Vue的响应式系统更新视图
        this.$nextTick(() => {
          const elem = document.getElementById('log')
          elem.scrollTop = elem.scrollHeight
        })
      },
      import_data() {
        let input = document.createElement('input')
        input.type = 'file'
        input.click()
        input.onchange = ($event) => {
          this.parse_imported_data($event)
        }
      },
      parse_imported_data(e) {
        let that = this

        this.appendLog('正在读取文件内容...')

        // 读取表格文件
        let fileName = ''
        const files = e.target.files
        if (files.length <= 0) {
          return false
        } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
          this.appendLog('上传格式不正确，请上传xls或者xlsx格式')
          return false
        } else {
          // 更新获取文件名
          fileName = files[0].name
        }

        const fileReader = new FileReader()
        fileReader.onload = async (ev) => {
          try {
            const data = new Uint8Array(ev.target.result)
            that.workbook = XLSX.read(data, { type: 'array' })
            that.systemTypeSheet = XLSX.utils.sheet_to_json(
              that.workbook.Sheets['系统类型'],
              {}
            ) // 生成json表格内容
            await that.process_systemTypeSheet()

            that.deviceTypeSheet = XLSX.utils.sheet_to_json(
              that.workbook.Sheets['设备类型'],
              {}
            ) // 生成json表格内容
            await that.process_deviceTypeSheet()

            that.brandSheet = XLSX.utils.sheet_to_json(
              that.workbook.Sheets['品牌'],
              {}
            ) // 生成json表格内容
            await that.process_brandSheet()

            that.modelSheet = XLSX.utils.sheet_to_json(
              that.workbook.Sheets['型号'],
              {}
            ) // 生成json表格内容
            await that.process_modelSheet()
          } catch (error) {
            console.log(error)
          }
        }
        fileReader.readAsArrayBuffer(files[0])
      },
      // 处理系统类型数据
      async process_systemTypeSheet() {
        this.appendLog('导入 系统定义')
        for (let i = 0; i < this.systemTypeSheet.length; i++) {
          let code = this.systemTypeSheet[i]['系统类型ID']
          this.systemTypeMap[code] = this.systemTypeSheet[i]['系统类型名称']
          const isExisted = await doExists('systems', { code: code })
          if (isExisted.exists) {
            // 跳过
            this.appendLog(
              '重复，跳过: ' + this.systemTypeSheet[i]['系统类型名称']
            )
          } else {
            // 不存在
            this.appendLog(
              '创建 系统类型: ' + this.systemTypeSheet[i]['系统类型名称']
            )
            let item = this.systemTypeSheet[i]
            try {
              await doAdd('systems', {
                code: item['系统类型ID'],
                name: item['系统类型名称'],
              })
            } catch (error) {
              this.appendLog(error.message, true)
            }
          }
        }
        this.appendLog('导入 系统类型完成')
      },
      // 处理设备类型数据
      async process_deviceTypeSheet() {
        this.appendLog('导入 设备类型定义')

        for (let i = 0; i < this.deviceTypeSheet.length; i++) {
          let item = this.deviceTypeSheet[i]
          let code = item['设备类型编号']
          const isExisted = await doExists('device_types', { code: code })
          if (isExisted.exists) {
            // 跳过
            this.appendLog('重复，跳过: ' + item['设备类型名称'])
          } else {
            // 不存在
            let sysCode = ''
            let sysName = ''
            for (let m = 0; m < this.systemTypeSheet.length; m++) {
              let sysIdItem = this.systemTypeSheet[m]['系统类型ID']
              let sysItem = this.systemTypeSheet[m]['系统类型名称']
              if (sysIdItem === item['所属系统类型ID']) {
                sysCode = sysIdItem
                sysName = sysItem
                break
              }
            }
            this.appendLog('创建 设备类型: ' + item['设备类型名称'])
            try {
              await doAdd('device_types', {
                code: item['设备类型编号'],
                name: item['设备类型名称'],
                sysCode: sysCode,
                sysName: sysName,
                scode: item['设备类型字母简称'] + '',
              })
            } catch (error) {
              this.appendLog(error.message, true)
            }
          }
        }
        this.appendLog('导入 设备类型完成')
      },
      // 处理品牌数据
      async process_brandSheet() {
        this.appendLog('导入 品牌')
        for (let i = 0; i < this.brandSheet.length; i++) {
          let item = this.brandSheet[i]
          this.appendLog(item['名称'])
          const isExisted = await doExists('brands', { code: item['ID'] + '' })
          if (isExisted.exists) {
            // 跳过
            this.appendLog('跳过 品牌: ' + item['名称'])
          } else {
            // 不存在
            let deviceTypeList = item['关联设备类型名称'].split('\\')
            let deviceTypeId = []
            let deviceTypeName = []
            for (let j = 0; j < deviceTypeList.length; j++) {
              for (let m = 0; m < this.deviceTypeSheet.length; m++) {
                let deviceTypeIdItem = this.deviceTypeSheet[m]['设备类型编号']
                let deviceTypeItem = this.deviceTypeSheet[m]['设备类型名称']
                if (deviceTypeItem === deviceTypeList[j]) {
                  deviceTypeId.push(deviceTypeIdItem)
                  deviceTypeName.push(deviceTypeItem)
                  break
                }
              }
            }
            let deviceTypeIds = deviceTypeId.join(',')
            let deviceTypeNames = deviceTypeName.join(',')
            let brand = {
              name: item['名称'],
              code: item['ID'] + '',
              deviceTypeCode: deviceTypeIds,
              deviceTypeName: deviceTypeNames,
              scode: item['品牌名称简称'],
            }
            try {
              await doAdd('brands', brand)
              this.appendLog('创建 品牌: ' + item['名称'])
            } catch (error) {
              this.appendLog(error.message, true)
            }
          }
        }
        this.appendLog('导入 品牌完成')
      },
      // 处理型号数据
      async process_modelSheet() {
        this.appendLog('导入 型号数据')
        for (let i = 0; i < this.modelSheet.length; i++) {
          let item = this.modelSheet[i]
          const isExisted = await doExists('models', { code: item['ID'] + '' })
          if (isExisted.exists) {
            // 跳过
            this.appendLog('跳过 型号:' + item['型号'])
          } else {
            // 不存在
            this.appendLog('创建 型号:' + item['型号'])
            let deviceType = ''
            let deviceTypeCode = ''
            let brand = ''
            let brandCode = ''
            for (let m = 0; m < this.brandSheet.length; m++) {
              let brandIdItem = this.brandSheet[m]['ID']
              let brandItem = this.brandSheet[m]['名称']
              if (brandIdItem === item['所属品牌ID']) {
                brand = brandItem
                brandCode = brandIdItem
                break
              }
            }
            for (let m = 0; m < this.deviceTypeSheet.length; m++) {
              let deviceTypeIdItem = this.deviceTypeSheet[m]['设备类型编号']
              let deviceTypeItem = this.deviceTypeSheet[m]['设备类型名称']
              if (deviceTypeIdItem === item['所属设备类型编号']) {
                deviceType = deviceTypeItem
                deviceTypeCode = deviceTypeIdItem
                break
              }
            }
            try {
              await doAdd('models', {
                name: item['型号'],
                code: item['ID'] + '',
                deviceType: deviceType,
                brandCode,
                deviceTypeCode,
                brand,
                remarks: item['备注'],
              })
            } catch (error) {
              this.appendLog(error.message, true)
            }
          }
        }
        this.appendLog('导入 型号完成')
      },
      async do_generate_product() {
        this.appendLog('生成标准产品库')
        console.log(this.workbook)
        // this.process_productSheet(this.workbook)
      },
      async process_productSheet(workbook) {
        this.productMap = {}

        let modelList = await getlist('models', { page: 1, limit: 100000 })

        for (let i = 0; i < deviceTypeKeys.length; i++) {
          let sheetName = this.deviceTypeMap[deviceTypeKeys[i]]
          this.appendLog('处理sheetName:' + sheetName)
          let ws = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName], {})
          if (ws) {
          } else {
            console.log(sheetName, '不存在！！！！！！')
          }

          for (let j = 0; j < ws.length; j++) {
            let w = ws[j]
            let productKey = w['品牌ID'] + '_' + w['型号ID']
            let product = this.productMap[productKey]
            if (product === undefined) {
              let systemKey =
                '00000000-' +
                w['设备类型ID'].split('-')[1] +
                '-0000-0000-000000000000'
              product = { rules: [] }
              // product.deviceTypeCode = w['设备类型ID']
              product.deviceType = w['设备类型名称']
              // product.brandCode = w['品牌ID']
              product.brand = this.brandMap['code_' + w['品牌ID']].name
              // product.modelCode = w['型号ID']
              product.system = this.systemTypeMap[systemKey]
              product.model = w['型号名称']
              product.name = product.brand + ' ' + product.model
              product.protocolType = ''
              product.commSetting = {}
              this.productMap[productKey] = product
            }

            if (w['参数ID'] && typeof w['参数ID'] === 'number') {
              w['参数ID'] = w['参数ID'] + ''
            }

            let devvou = {
              // paramCode: w['参数ID'] ? w['参数ID'].toUpperCase() : '',
              paramCode: w['参数ID'] ? w['参数ID'] : '',
              paramName: w['参数名称ch'],
              paramDataType: w['参数类型DataChar'] || null,
              needRec: w['是否启用记录历史RecHis'],
              needRpt: w['是否启用记录报表Report'],
              workState: w['是否是工作状态WrkState'] ? 0 : 1,
              tx: w['发送串Tx'] || null,
              gatherInterval: w['采集间隔(毫秒)RxTime']
                ? parseInt(w['采集间隔(毫秒)RxTime'])
                : 500,
              defValue: w['默认值DefValue']
                ? parseInt(w['默认值DefValue'])
                : null,
              refParamCode: w['引用属性的idMasterId'] || null,
              dataUnit: w['单位Unit'],
              dataStartPos: w['数据截取的起始位置BGPos']
                ? parseInt(w['数据截取的起始位置BGPos'])
                : null,
              dataValidLen: w['数据截取的长度Len']
                ? parseInt(w['数据截取的长度Len'])
                : null,
              dataCalib: w['计算公式Calib'],
              dataCrcType: w['采集值的校验类型CrcType'],
              alarmEnable: w['是否产生报警事件EventAlarm'],
              alarmDebounce: w['报警忽略次数NoEvent']
                ? parseInt(w['报警忽略次数NoEvent'])
                : 1,
              alarmThrottle: w['报警最大确认次数EmaxTimes']
                ? parseInt(w['报警最大确认次数EmaxTimes'])
                : 1,
              alarmRule: '[]',
              orig: w,
            }
            product.rules.push(devvou)
          }
        }
        let keys = Object.keys(this.productMap)
        for (let k = 0; k < keys.length; k++) {
          let p = this.productMap[keys[k]]
          let existed = await isProductExisted(p.name)
          if (!existed) {
            createProduct(p)
          }
        }
        this.appendLog('导入完成')
      },
    },
  }
</script>
