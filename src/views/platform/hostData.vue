<template>
  <div class="host-data-container">
    <!-- SSH连接信息卡片 -->
    <a-card class="ssh-card">
      <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
        <a-col :span="24">
          <h3>SSH连接信息</h3>
          <a-form :model="sshInfo" :rules="sshRules" ref="sshFormRef" 
            :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 6 } }" 
            :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 18 } }">
            <a-row :gutter="16">
              <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <a-form-item label="远程主机IP" name="target">
                  <a-input v-model:value="sshInfo.target" placeholder="请输入远程主机IP" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <a-form-item label="SSH用户名" name="username">
                  <a-input v-model:value="sshInfo.username" placeholder="请输入SSH用户名" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <a-form-item label="SSH密码" name="password">
                  <a-input-password v-model:value="sshInfo.password" placeholder="请输入SSH密码" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
                <a-form-item label="SSH端口" name="port">
                  <a-input-number v-model:value="sshInfo.port" :min="1" :max="65535" style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 功能卡片 -->
    <a-card class="script-card">
      <a-tabs v-model:activeKey="activeTabKey">
        <a-tab-pane key="runScript" tab="运行远程脚本">
          <p>通过SSH连接到远程主机并执行脚本。</p>
          <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
            <!-- 左侧表单 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="10" class="form-container">
              <a-form :model="formState" :rules="rules" ref="formRef" 
                :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 8 } }" 
                :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 16 } }">
                <a-divider>脚本配置</a-divider>
                
                <a-form-item label="本地脚本路径" name="local_script">
                  <a-input v-model:value="formState.local_script" placeholder="请输入本地脚本路径" />
                </a-form-item>
                
                <a-form-item label="远程脚本路径" name="remote_script">
                  <a-input v-model:value="formState.remote_script" placeholder="请输入远程脚本路径" />
                </a-form-item>
                
                <a-form-item label="脚本参数" name="args">
                  <a-textarea v-model:value="formState.argsString" placeholder="请输入脚本参数，多个参数用空格分隔" allow-clear/>
                </a-form-item>
                
                <a-form-item :wrapper-col="{ xs: { span: 18, offset: 6 }, sm: { span: 18, offset: 6 }, md: { span: 16, offset: 8 } }">
                  <a-button type="primary" @click="runScript" :loading="loading">执行脚本</a-button>
                  <a-button style="margin-left: 10px" @click="resetForm">重置</a-button>
                  <a-button 
                    type="link" 
                    style="margin-left: 10px" 
                    @click="historyVisible = true"
                    v-if="scriptHistory.length > 0"
                  >
                    查看历史记录
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
            
            <!-- 右侧执行结果 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="14" class="result-container">
              <div class="result-panel">
                <a-divider class="mobile-divider">执行结果</a-divider>
                
                <div v-if="scriptResult" class="script-result">
                  <a-alert
                    :message="scriptResult.success ? '脚本执行成功' : '脚本执行失败'"
                    :description="scriptResult.message"
                    :type="scriptResult.success ? 'success' : 'error'"
                    show-icon
                  />
                  
                  <a-divider orientation="left">输出</a-divider>
                  <pre class="script-output">{{ scriptResult.output || '无输出' }}</pre>
                  
                  <a-divider orientation="left">错误</a-divider>
                  <pre class="script-output" v-if="scriptResult.error">{{ scriptResult.error }}</pre>
                  <p v-else>无错误</p>
                </div>
                <a-empty v-else description="尚未执行脚本" />
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="writeFile" tab="写入远程文件">
          <p>通过SSH连接到远程主机并写入文件内容。</p>
          <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
            <!-- 左侧表单 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="10" class="form-container">
              <a-form :model="fileFormState" :rules="fileRules" ref="fileFormRef" 
                :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 8 } }" 
                :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 16 } }">
                <a-divider>文件配置</a-divider>
                
                <a-form-item label="远程文件路径" name="remote_file">
                  <a-input v-model:value="fileFormState.remote_file" placeholder="请输入远程文件路径" />
                </a-form-item>
                
                <a-form-item label="文件内容" name="content">
                  <a-textarea v-model:value="fileFormState.content" placeholder="请输入要写入的文件内容" 
                    :auto-size="{ minRows: 6, maxRows: 12 }" allow-clear />
                </a-form-item>
                
                <a-form-item :wrapper-col="{ xs: { span: 18, offset: 6 }, sm: { span: 18, offset: 6 }, md: { span: 16, offset: 8 } }">
                  <a-button type="primary" @click="writeContentToFile" :loading="fileLoading">写入文件</a-button>
                  <a-button style="margin-left: 10px" @click="resetFileForm">重置</a-button>
                  <a-button 
                    type="link" 
                    style="margin-left: 10px" 
                    @click="fileHistoryVisible = true"
                    v-if="fileHistory.length > 0"
                  >
                    查看历史记录
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
            
            <!-- 右侧执行结果 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="14" class="result-container">
              <div class="result-panel">
                <a-divider class="mobile-divider">执行结果</a-divider>
                
                <div v-if="fileResult" class="script-result">
                  <a-alert
                    :message="fileResult.success ? '文件写入成功' : '文件写入失败'"
                    :description="fileResult.message"
                    :type="fileResult.success ? 'success' : 'error'"
                    show-icon
                  />
                  
                  <a-divider orientation="left">详情</a-divider>
                  <pre class="script-output">{{ fileResult.details || '无详细信息' }}</pre>
                  
                  <a-divider orientation="left">错误</a-divider>
                  <pre class="script-output" v-if="fileResult.error">{{ fileResult.error }}</pre>
                  <p v-else>无错误</p>
                </div>
                <a-empty v-else description="尚未写入文件" />
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="pullFile" tab="拉取远程文件">
          <p>通过SSH连接到远程主机并拉取文件到本地。</p>
          <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
            <!-- 左侧表单 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="10" class="form-container">
              <a-form :model="pullFileFormState" :rules="pullFileRules" ref="pullFileFormRef" 
                :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 8 } }" 
                :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 16 } }">
                <a-divider>文件配置</a-divider>
                
                <a-form-item label="远程文件路径" name="remote_file">
                  <a-input v-model:value="pullFileFormState.remote_file" placeholder="请输入远程文件路径" />
                </a-form-item>
                
                <a-form-item label="本地保存路径" name="local_file">
                  <a-input v-model:value="pullFileFormState.local_file" placeholder="请输入本地保存路径" />
                </a-form-item>
                
                <a-form-item :wrapper-col="{ xs: { span: 18, offset: 6 }, sm: { span: 18, offset: 6 }, md: { span: 16, offset: 8 } }">
                  <a-button type="primary" @click="pullRemoteFile" :loading="pullFileLoading">拉取文件</a-button>
                  <a-button style="margin-left: 10px" @click="resetPullFileForm">重置</a-button>
                  <a-button 
                    type="link" 
                    style="margin-left: 10px" 
                    @click="pullFileHistoryVisible = true"
                    v-if="pullFileHistory.length > 0"
                  >
                    查看历史记录
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
            
            <!-- 右侧执行结果 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="14" class="result-container">
              <div class="result-panel">
                <a-divider class="mobile-divider">执行结果</a-divider>
                
                <div v-if="pullFileResult" class="script-result">
                  <a-alert
                    :message="pullFileResult.success ? '文件拉取成功' : '文件拉取失败'"
                    :description="pullFileResult.message"
                    :type="pullFileResult.success ? 'success' : 'error'"
                    show-icon
                  />
                  
                  <a-divider orientation="left">详情</a-divider>
                  <pre class="script-output">{{ pullFileResult.details || '无详细信息' }}</pre>
                  
                  <a-divider orientation="left">错误</a-divider>
                  <pre class="script-output" v-if="pullFileResult.error">{{ pullFileResult.error }}</pre>
                  <p v-else>无错误</p>
                </div>
                <a-empty v-else description="尚未拉取文件" />
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="uploadFile" tab="上传本地文件">
          <p>通过SSH连接到远程主机并上传本地文件。</p>
          <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
            <!-- 左侧表单 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="10" class="form-container">
              <a-form :model="uploadFileFormState" :rules="uploadFileRules" ref="uploadFileFormRef" 
                :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 8 } }" 
                :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 16 } }">
                <a-divider>文件配置</a-divider>
                
                <a-form-item label="本地文件路径" name="local_file">
                  <a-input v-model:value="uploadFileFormState.local_file" placeholder="请输入本地文件路径" />
                </a-form-item>
                
                <a-form-item label="远程保存路径" name="remote_file">
                  <a-input v-model:value="uploadFileFormState.remote_file" placeholder="请输入远程保存路径" />
                </a-form-item>
                
                <a-form-item :wrapper-col="{ xs: { span: 18, offset: 6 }, sm: { span: 18, offset: 6 }, md: { span: 16, offset: 8 } }">
                  <a-button type="primary" @click="uploadLocalFile" :loading="uploadFileLoading">上传文件</a-button>
                  <a-button style="margin-left: 10px" @click="resetUploadFileForm">重置</a-button>
                  <a-button 
                    type="link" 
                    style="margin-left: 10px" 
                    @click="uploadFileHistoryVisible = true"
                    v-if="uploadFileHistory.length > 0"
                  >
                    查看历史记录
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
            
            <!-- 右侧执行结果 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="14" class="result-container">
              <div class="result-panel">
                <a-divider class="mobile-divider">执行结果</a-divider>
                
                <div v-if="uploadFileResult" class="script-result">
                  <a-alert
                    :message="uploadFileResult.success ? '文件上传成功' : '文件上传失败'"
                    :description="uploadFileResult.message"
                    :type="uploadFileResult.success ? 'success' : 'error'"
                    show-icon
                  />
                  
                  <a-divider orientation="left">详情</a-divider>
                  <pre class="script-output">{{ uploadFileResult.details || '无详细信息' }}</pre>
                  
                  <a-divider orientation="left">错误</a-divider>
                  <pre class="script-output" v-if="uploadFileResult.error">{{ uploadFileResult.error }}</pre>
                  <p v-else>无错误</p>
                </div>
                <a-empty v-else description="尚未上传文件" />
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="removeFile" tab="删除远程文件">
          <p>通过SSH连接到远程主机并删除文件。</p>
          <a-row :gutter="{ xs: 0, sm: 0, md: 16, lg: 16, xl: 16 }">
            <!-- 左侧表单 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="10" class="form-container">
              <a-form :model="removeFileFormState" :rules="removeFileRules" ref="removeFileFormRef" 
                :label-col="{ xs: { span: 6 }, sm: { span: 6 }, md: { span: 8 } }" 
                :wrapper-col="{ xs: { span: 18 }, sm: { span: 18 }, md: { span: 16 } }">
                <a-divider>文件配置</a-divider>
                
                <a-form-item label="远程文件路径" name="remote_file">
                  <a-input v-model:value="removeFileFormState.remote_file" placeholder="请输入要删除的远程文件路径" />
                </a-form-item>
                
                <a-form-item :wrapper-col="{ xs: { span: 18, offset: 6 }, sm: { span: 18, offset: 6 }, md: { span: 16, offset: 8 } }">
                  <a-button type="primary" danger @click="removeRemoteFile" :loading="removeFileLoading">删除文件</a-button>
                  <a-button style="margin-left: 10px" @click="resetRemoveFileForm">重置</a-button>
                  <a-button 
                    type="link" 
                    style="margin-left: 10px" 
                    @click="removeFileHistoryVisible = true"
                    v-if="removeFileHistory.length > 0"
                  >
                    查看历史记录
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
            
            <!-- 右侧执行结果 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="14" class="result-container">
              <div class="result-panel">
                <a-divider class="mobile-divider">执行结果</a-divider>
                
                <div v-if="removeFileResult" class="script-result">
                  <a-alert
                    :message="removeFileResult.success ? '文件删除成功' : '文件删除失败'"
                    :description="removeFileResult.message"
                    :type="removeFileResult.success ? 'success' : 'error'"
                    show-icon
                  />
                  
                  <a-divider orientation="left">详情</a-divider>
                  <pre class="script-output">{{ removeFileResult.details || '无详细信息' }}</pre>
                  
                  <a-divider orientation="left">错误</a-divider>
                  <pre class="script-output" v-if="removeFileResult.error">{{ removeFileResult.error }}</pre>
                  <p v-else>无错误</p>
                </div>
                <a-empty v-else description="尚未删除文件" />
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-card>
    
    <!-- 运行脚本历史记录弹窗 -->
    <a-modal
      v-model:visible="historyVisible"
      title="脚本执行历史记录"
      width="1000px"
      @cancel="historyVisible = false"
      class="history-modal"
    >
      <a-table 
        :dataSource="scriptHistory" 
        :columns="historyColumns" 
        :pagination="false"
        :scroll="{ x: 800 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="loadHistoryRecord(record)">加载</a-button>
          </template>
          <template v-if="column.key === 'timestamp'">
            {{ formatDate(record.timestamp) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.result.success ? 'success' : 'error'">
              {{ record.result.success ? '成功' : '失败' }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-button type="primary" @click="historyVisible = false">关闭</a-button>
        <a-button danger style="margin-left: 10px" @click="clearHistory">清空历史</a-button>
      </template>
    </a-modal>
    
    <!-- 写入文件历史记录弹窗 -->
    <a-modal
      v-model:visible="fileHistoryVisible"
      title="文件写入历史记录"
      width="1000px"
      @cancel="fileHistoryVisible = false"
      class="history-modal"
    >
      <a-table 
        :dataSource="fileHistory" 
        :columns="fileHistoryColumns" 
        :pagination="false"
        :scroll="{ x: 800 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="loadFileHistoryRecord(record)">加载</a-button>
          </template>
          <template v-if="column.key === 'timestamp'">
            {{ formatDate(record.timestamp) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.result.success ? 'success' : 'error'">
              {{ record.result.success ? '成功' : '失败' }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-button type="primary" @click="fileHistoryVisible = false">关闭</a-button>
        <a-button danger style="margin-left: 10px" @click="clearFileHistory">清空历史</a-button>
      </template>
    </a-modal>
    
    <!-- 拉取文件历史记录弹窗 -->
    <a-modal
      v-model:visible="pullFileHistoryVisible"
      title="文件拉取历史记录"
      width="1000px"
      @cancel="pullFileHistoryVisible = false"
      class="history-modal"
    >
      <a-table 
        :dataSource="pullFileHistory" 
        :columns="pullFileHistoryColumns" 
        :pagination="false"
        :scroll="{ x: 800 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="loadPullFileHistoryRecord(record)">加载</a-button>
          </template>
          <template v-if="column.key === 'timestamp'">
            {{ formatDate(record.timestamp) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.result.success ? 'success' : 'error'">
              {{ record.result.success ? '成功' : '失败' }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-button type="primary" @click="pullFileHistoryVisible = false">关闭</a-button>
        <a-button danger style="margin-left: 10px" @click="clearPullFileHistory">清空历史</a-button>
      </template>
    </a-modal>
    
    <!-- 上传文件历史记录弹窗 -->
    <a-modal
      v-model:visible="uploadFileHistoryVisible"
      title="文件上传历史记录"
      width="1000px"
      @cancel="uploadFileHistoryVisible = false"
      class="history-modal"
    >
      <a-table 
        :dataSource="uploadFileHistory" 
        :columns="uploadFileHistoryColumns" 
        :pagination="false"
        :scroll="{ x: 800 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="loadUploadFileHistoryRecord(record)">加载</a-button>
          </template>
          <template v-if="column.key === 'timestamp'">
            {{ formatDate(record.timestamp) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.result.success ? 'success' : 'error'">
              {{ record.result.success ? '成功' : '失败' }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-button type="primary" @click="uploadFileHistoryVisible = false">关闭</a-button>
        <a-button danger style="margin-left: 10px" @click="clearUploadFileHistory">清空历史</a-button>
      </template>
    </a-modal>
    
    <!-- 删除文件历史记录弹窗 -->
    <a-modal
      v-model:visible="removeFileHistoryVisible"
      title="文件删除历史记录"
      width="1000px"
      @cancel="removeFileHistoryVisible = false"
      class="history-modal"
    >
      <a-table 
        :dataSource="removeFileHistory" 
        :columns="removeFileHistoryColumns" 
        :pagination="false"
        :scroll="{ x: 800 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="loadRemoveFileHistoryRecord(record)">加载</a-button>
          </template>
          <template v-if="column.key === 'timestamp'">
            {{ formatDate(record.timestamp) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tag :color="record.result.success ? 'success' : 'error'">
              {{ record.result.success ? '成功' : '失败' }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-button type="primary" @click="removeFileHistoryVisible = false">关闭</a-button>
        <a-button danger style="margin-left: 10px" @click="clearRemoveFileHistory">清空历史</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue'
import { runApi, writeContentToFileApi, pullRemoteFileApi, uploadLocalFileApi, removeRemoteFileApi } from '@/api/host'

export default {
  name: 'HostData',
  data() {
    return {
      activeTabKey: 'runScript',
      // 公共SSH连接信息
      sshInfo: {
        target: '**********',
        username: 'root',
        password: 'root',
        port: 22,
        use_password: true
      },
      // SSH连接信息验证规则
      sshRules: {
        target: [
          { required: true, message: '请输入远程主机IP', trigger: 'blur' },
        ],
        username: [
          { required: true, message: '请输入SSH用户名', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入SSH密码', trigger: 'blur' },
        ],
        port: [
          { required: true, message: '请输入SSH端口', trigger: 'blur' },
        ]
      },
      formState: {
        local_script: '/Users/<USER>/Desktop/LKsoft/test.sh',
        remote_script: '/tmp/test.sh',
        args: [],
        argsString: ''
      },
      rules: {
        local_script: [
          { required: true, message: '请输入本地脚本路径', trigger: 'blur' },
        ],
        remote_script: [
          { required: true, message: '请输入远程脚本路径', trigger: 'blur' },
        ]
      },
      loading: false,
      scriptResult: null,
      historyVisible: false,
      scriptHistory: [],
      historyColumns: [
        {
          title: '时间',
          dataIndex: 'timestamp',
          key: 'timestamp',
          width: 150,
        },
        {
          title: '主机',
          dataIndex: 'target',
          key: 'target',
          width: 120,
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
          width: 100,
        },
        {
          title: '本地脚本',
          dataIndex: 'local_script',
          key: 'local_script',
          width: 150,
          ellipsis: true,
        },
        {
          title: '远程脚本',
          dataIndex: 'remote_script',
          key: 'remote_script',
          width: 120,
          ellipsis: true,
        },
        {
          title: '结果',
          dataIndex: 'result',
          key: 'result',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
        },
      ],
      fileFormState: {
        remote_file: '/tmp/test.txt',
        content: ''
      },
      fileRules: {
        remote_file: [
          { required: true, message: '请输入远程文件路径', trigger: 'blur' },
        ],
        content: [
          { required: true, message: '请输入文件内容', trigger: 'blur' },
        ]
      },
      fileLoading: false,
      fileResult: null,
      fileHistoryVisible: false,
      fileHistory: [],
      fileHistoryColumns: [
        {
          title: '时间',
          dataIndex: 'timestamp',
          key: 'timestamp',
          width: 150,
        },
        {
          title: '主机',
          dataIndex: 'target',
          key: 'target',
          width: 120,
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
          width: 100,
        },
        {
          title: '远程文件',
          dataIndex: 'remote_file',
          key: 'remote_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '结果',
          dataIndex: 'result',
          key: 'result',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
        },
      ],
      pullFileFormState: {
        remote_file: '/tmp/test.txt',
        local_file: '/Users/<USER>/Desktop/LKsoft/test.sh'
      },
      pullFileRules: {
        remote_file: [
          { required: true, message: '请输入远程文件路径', trigger: 'blur' },
        ],
        local_file: [
          { required: true, message: '请输入本地保存路径', trigger: 'blur' },
        ]
      },
      pullFileLoading: false,
      pullFileResult: null,
      pullFileHistoryVisible: false,
      pullFileHistory: [],
      pullFileHistoryColumns: [
        {
          title: '时间',
          dataIndex: 'timestamp',
          key: 'timestamp',
          width: 150,
        },
        {
          title: '主机',
          dataIndex: 'target',
          key: 'target',
          width: 120,
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
          width: 100,
        },
        {
          title: '远程文件',
          dataIndex: 'remote_file',
          key: 'remote_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '本地保存路径',
          dataIndex: 'local_file',
          key: 'local_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '结果',
          dataIndex: 'result',
          key: 'result',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
        },
      ],
      uploadFileFormState: {
        local_file: '/Users/<USER>/Desktop/LKsoft/test.sh',
        remote_file: '/tmp/test.sh'
      },
      uploadFileRules: {
        local_file: [
          { required: true, message: '请输入本地文件路径', trigger: 'blur' },
        ],
        remote_file: [
          { required: true, message: '请输入远程保存路径', trigger: 'blur' },
        ]
      },
      uploadFileLoading: false,
      uploadFileResult: null,
      uploadFileHistoryVisible: false,
      uploadFileHistory: [],
      uploadFileHistoryColumns: [
        {
          title: '时间',
          dataIndex: 'timestamp',
          key: 'timestamp',
          width: 150,
        },
        {
          title: '主机',
          dataIndex: 'target',
          key: 'target',
          width: 120,
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
          width: 100,
        },
        {
          title: '本地文件',
          dataIndex: 'local_file',
          key: 'local_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '远程保存路径',
          dataIndex: 'remote_file',
          key: 'remote_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '结果',
          dataIndex: 'result',
          key: 'result',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
        },
      ],
      removeFileFormState: {
        remote_file: '/tmp/test.txt'
      },
      removeFileRules: {
        remote_file: [
          { required: true, message: '请输入远程文件路径', trigger: 'blur' },
        ]
      },
      removeFileLoading: false,
      removeFileResult: null,
      removeFileHistoryVisible: false,
      removeFileHistory: [],
      removeFileHistoryColumns: [
        {
          title: '时间',
          dataIndex: 'timestamp',
          key: 'timestamp',
          width: 150,
        },
        {
          title: '主机',
          dataIndex: 'target',
          key: 'target',
          width: 120,
        },
        {
          title: '用户名',
          dataIndex: 'username',
          key: 'username',
          width: 100,
        },
        {
          title: '远程文件',
          dataIndex: 'remote_file',
          key: 'remote_file',
          width: 150,
          ellipsis: true,
        },
        {
          title: '结果',
          dataIndex: 'result',
          key: 'result',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
        },
      ],
    }
  },
  watch: {
    'formState.argsString': {
      handler(val) {
        this.formState.args = val ? val.split(' ').filter(arg => arg.trim()) : [];
      }
    }
  },
  mounted() {
    // 加载历史记录
    this.loadHistory()
    // 加载文件历史记录
    this.loadFileHistory()
    // 加载拉取文件历史记录
    this.loadPullFileHistory()
    // 加载上传文件历史记录
    this.loadUploadFileHistory()
    // 加载删除文件历史记录
    this.loadRemoveFileHistory()
  },
  methods: {
    // 运行脚本
    runScript() {
      // 先验证SSH连接信息
      this.$refs.sshFormRef.validate().then(() => {
        // 再验证脚本表单
        this.$refs.formRef.validate().then(() => {
          this.loading = true
          
          // 准备请求参数
          const params = {
            target: this.sshInfo.target,
            username: this.sshInfo.username,
            password: this.sshInfo.password,
            port: this.sshInfo.port,
            local_script: this.formState.local_script,
            remote_script: this.formState.remote_script,
            args: this.formState.args,
            use_password: this.sshInfo.use_password
          }
          
          // 调用API
          runApi(params)
            .then(response => {
              this.scriptResult = {
                success: true,
                message: '脚本执行成功',
                output: response.output,
                error: response.error
              }
              
              // 保存到历史记录
              this.saveToHistory({
                ...params,
                timestamp: new Date().getTime(),
                result: this.scriptResult
              })
              
              message.success('脚本执行成功')
            })
            .catch(error => {
              this.scriptResult = {
                success: false,
                message: error.error || '脚本执行失败',
                output: '',
                error: error.error
              }
              message.error('脚本执行失败：' + (error.error || '未知错误'))
            })
            .finally(() => {
              this.loading = false
            })
        }).catch(error => {
          console.log('脚本表单验证失败', error)
        })
      }).catch(error => {
        console.log('SSH连接信息验证失败', error)
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields()
      this.scriptResult = null
    },
    
    // 保存到历史记录
    saveToHistory(record) {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('scriptHistory') || '[]')
      
      // 添加新记录
      history.unshift(record)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.pop()
      }
      
      // 保存回localStorage
      localStorage.setItem('scriptHistory', JSON.stringify(history))
      
      // 更新当前历史记录
      this.loadHistory()
    },
    
    // 加载历史记录
    loadHistory() {
      this.scriptHistory = JSON.parse(localStorage.getItem('scriptHistory') || '[]')
    },
    
    // 清空历史记录
    clearHistory() {
      localStorage.removeItem('scriptHistory')
      this.scriptHistory = []
      this.historyVisible = false
      message.success('历史记录已清空')
    },
    
    // 加载历史记录时同步更新SSH连接信息
    loadHistoryRecord(record) {
      // 更新SSH连接信息
      this.sshInfo = {
        target: record.target,
        username: record.username,
        password: record.password,
        port: record.port,
        use_password: true
      }
      
      // 更新表单状态
      this.formState = {
        local_script: record.local_script,
        remote_script: record.remote_script,
        args: record.args,
        argsString: record.args.join(' ')
      }
      
      this.historyVisible = false
      message.success('已加载历史配置')
    },
    
    // 格式化日期
    formatDate(timestamp) {
      const date = new Date(timestamp)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
    },
    
    // 写入文件
    writeContentToFile() {
      // 先验证SSH连接信息
      this.$refs.sshFormRef.validate().then(() => {
        // 再验证文件表单
        this.$refs.fileFormRef.validate().then(() => {
          this.fileLoading = true
          
          // 准备请求参数
          const params = {
            target: this.sshInfo.target,
            username: this.sshInfo.username,
            password: this.sshInfo.password,
            port: this.sshInfo.port,
            remote_file: this.fileFormState.remote_file,
            content: this.fileFormState.content,
            use_password: this.sshInfo.use_password
          }
          
          // 调用API
          writeContentToFileApi(params)
            .then(response => {
              this.fileResult = {
                success: true,
                message: '文件写入成功',
                details: response.details,
                error: response.error
              }
              
              // 保存到历史记录
              this.saveFileToHistory({
                ...params,
                timestamp: new Date().getTime(),
                result: this.fileResult
              })
              
              message.success('文件写入成功')
            })
            .catch(error => {
              this.fileResult = {
                success: false,
                message: error.error || '文件写入失败',
                details: '',
                error: error.error
              }
              message.error('文件写入失败：' + (error.error || '未知错误'))
            })
            .finally(() => {
              this.fileLoading = false
            })
        }).catch(error => {
          console.log('文件表单验证失败', error)
        })
      }).catch(error => {
        console.log('SSH连接信息验证失败', error)
      })
    },
    
    // 重置文件表单
    resetFileForm() {
      this.$refs.fileFormRef.resetFields()
      this.fileResult = null
    },
    
    // 保存到文件历史记录
    saveFileToHistory(record) {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('fileHistory') || '[]')
      
      // 添加新记录
      history.unshift(record)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.pop()
      }
      
      // 保存回localStorage
      localStorage.setItem('fileHistory', JSON.stringify(history))
      
      // 更新当前历史记录
      this.loadFileHistory()
    },
    
    // 加载文件历史记录
    loadFileHistory() {
      this.fileHistory = JSON.parse(localStorage.getItem('fileHistory') || '[]')
    },
    
    // 清空文件历史记录
    clearFileHistory() {
      localStorage.removeItem('fileHistory')
      this.fileHistory = []
      this.fileHistoryVisible = false
      message.success('文件历史记录已清空')
    },
    
    // 加载文件历史记录时同步更新SSH连接信息
    loadFileHistoryRecord(record) {
      // 更新SSH连接信息
      this.sshInfo = {
        target: record.target,
        username: record.username,
        password: record.password,
        port: record.port,
        use_password: true
      }
      
      // 更新表单状态
      this.fileFormState = {
        remote_file: record.remote_file,
        content: record.content
      }
      
      this.fileHistoryVisible = false
      message.success('已加载历史配置')
    },
    
    // 拉取远程文件
    pullRemoteFile() {
      // 先验证SSH连接信息
      this.$refs.sshFormRef.validate().then(() => {
        // 再验证文件表单
        this.$refs.pullFileFormRef.validate().then(() => {
          this.pullFileLoading = true
          
          // 准备请求参数
          const params = {
            target: this.sshInfo.target,
            username: this.sshInfo.username,
            password: this.sshInfo.password,
            port: this.sshInfo.port,
            remote_file: this.pullFileFormState.remote_file,
            local_file: this.pullFileFormState.local_file,
            use_password: this.sshInfo.use_password
          }
          
          // 调用API
          pullRemoteFileApi(params)
            .then(response => {
              this.pullFileResult = {
                success: true,
                message: '文件拉取成功',
                details: response.details,
                error: response.error
              }
              
              // 保存到历史记录
              this.savePullFileToHistory({
                ...params,
                timestamp: new Date().getTime(),
                result: this.pullFileResult
              })
              
              message.success('文件拉取成功')
            })
            .catch(error => {
              this.pullFileResult = {
                success: false,
                message: error.error || '文件拉取失败',
                details: '',
                error: error.error
              }
              message.error('文件拉取失败：' + (error.error || '未知错误'))
            })
            .finally(() => {
              this.pullFileLoading = false
            })
        }).catch(error => {
          console.log('文件表单验证失败', error)
        })
      }).catch(error => {
        console.log('SSH连接信息验证失败', error)
      })
    },
    
    // 保存到拉取文件历史记录
    savePullFileToHistory(record) {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('pullFileHistory') || '[]')
      
      // 添加新记录
      history.unshift(record)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.pop()
      }
      
      // 保存回localStorage
      localStorage.setItem('pullFileHistory', JSON.stringify(history))
      
      // 更新当前历史记录
      this.loadPullFileHistory()
    },
    
    // 加载拉取文件历史记录
    loadPullFileHistory() {
      this.pullFileHistory = JSON.parse(localStorage.getItem('pullFileHistory') || '[]')
    },
    
    // 清空拉取文件历史记录
    clearPullFileHistory() {
      localStorage.removeItem('pullFileHistory')
      this.pullFileHistory = []
      this.pullFileHistoryVisible = false
      message.success('文件拉取历史记录已清空')
    },
    
    // 加载拉取文件历史记录时同步更新SSH连接信息
    loadPullFileHistoryRecord(record) {
      // 更新SSH连接信息
      this.sshInfo = {
        target: record.target,
        username: record.username,
        password: record.password,
        port: record.port,
        use_password: true
      }
      
      // 更新表单状态
      this.pullFileFormState = {
        remote_file: record.remote_file,
        local_file: record.local_file
      }
      
      this.pullFileHistoryVisible = false
      message.success('已加载历史配置')
    },
    
    // 重置拉取文件表单
    resetPullFileForm() {
      this.$refs.pullFileFormRef.resetFields()
      this.pullFileResult = null
    },
    
    // 上传本地文件
    uploadLocalFile() {
      // 先验证SSH连接信息
      this.$refs.sshFormRef.validate().then(() => {
        // 再验证文件表单
        this.$refs.uploadFileFormRef.validate().then(() => {
          this.uploadFileLoading = true
          
          // 准备请求参数
          const params = {
            target: this.sshInfo.target,
            username: this.sshInfo.username,
            password: this.sshInfo.password,
            port: this.sshInfo.port,
            local_file: this.uploadFileFormState.local_file,
            remote_file: this.uploadFileFormState.remote_file,
            use_password: this.sshInfo.use_password
          }
          
          // 调用API
          uploadLocalFileApi(params)
            .then(response => {
              this.uploadFileResult = {
                success: true,
                message: '文件上传成功',
                details: response.details,
                error: response.error
              }
              
              // 保存到历史记录
              this.saveUploadFileToHistory({
                ...params,
                timestamp: new Date().getTime(),
                result: this.uploadFileResult
              })
              
              message.success('文件上传成功')
            })
            .catch(error => {
              this.uploadFileResult = {
                success: false,
                message: error.error || '文件上传失败',
                details: '',
                error: error.error
              }
              message.error('文件上传失败：' + (error.error || '未知错误'))
            })
            .finally(() => {
              this.uploadFileLoading = false
            })
        }).catch(error => {
          console.log('文件表单验证失败', error)
        })
      }).catch(error => {
        console.log('SSH连接信息验证失败', error)
      })
    },
    
    // 重置上传文件表单
    resetUploadFileForm() {
      this.$refs.uploadFileFormRef.resetFields()
      this.uploadFileResult = null
    },
    
    // 保存到上传文件历史记录
    saveUploadFileToHistory(record) {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('uploadFileHistory') || '[]')
      
      // 添加新记录
      history.unshift(record)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.pop()
      }
      
      // 保存回localStorage
      localStorage.setItem('uploadFileHistory', JSON.stringify(history))
      
      // 更新当前历史记录
      this.loadUploadFileHistory()
    },
    
    // 加载上传文件历史记录
    loadUploadFileHistory() {
      this.uploadFileHistory = JSON.parse(localStorage.getItem('uploadFileHistory') || '[]')
    },
    
    // 清空上传文件历史记录
    clearUploadFileHistory() {
      localStorage.removeItem('uploadFileHistory')
      this.uploadFileHistory = []
      this.uploadFileHistoryVisible = false
      message.success('文件上传历史记录已清空')
    },
    
    // 加载上传文件历史记录时同步更新SSH连接信息
    loadUploadFileHistoryRecord(record) {
      // 更新SSH连接信息
      this.sshInfo = {
        target: record.target,
        username: record.username,
        password: record.password,
        port: record.port,
        use_password: true
      }
      
      // 更新表单状态
      this.uploadFileFormState = {
        local_file: record.local_file,
        remote_file: record.remote_file
      }
      
      this.uploadFileHistoryVisible = false
      message.success('已加载历史配置')
    },
    
    // 删除远程文件
    removeRemoteFile() {
      // 先验证SSH连接信息
      this.$refs.sshFormRef.validate().then(() => {
        // 再验证文件表单
        this.$refs.removeFileFormRef.validate().then(() => {
          this.removeFileLoading = true
          
          // 准备请求参数
          const params = {
            target: this.sshInfo.target,
            username: this.sshInfo.username,
            password: this.sshInfo.password,
            port: this.sshInfo.port,
            remote_file: this.removeFileFormState.remote_file,
            use_password: this.sshInfo.use_password
          }
          
          // 调用API
          removeRemoteFileApi(params)
            .then(response => {
              this.removeFileResult = {
                success: true,
                message: '文件删除成功',
                details: response.details,
                error: response.error
              }
              
              // 保存到历史记录
              this.saveRemoveFileToHistory({
                ...params,
                timestamp: new Date().getTime(),
                result: this.removeFileResult
              })
              
              message.success('文件删除成功')
            })
            .catch(error => {
              this.removeFileResult = {
                success: false,
                message: error.error || '文件删除失败',
                details: '',
                error: error.error
              }
              message.error('文件删除失败：' + (error.error || '未知错误'))
            })
            .finally(() => {
              this.removeFileLoading = false
            })
        }).catch(error => {
          console.log('文件表单验证失败', error)
        })
      }).catch(error => {
        console.log('SSH连接信息验证失败', error)
      })
    },
    
    // 保存到删除文件历史记录
    saveRemoveFileToHistory(record) {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('removeFileHistory') || '[]')
      
      // 添加新记录
      history.unshift(record)
      
      // 只保留最近10条记录
      if (history.length > 10) {
        history.pop()
      }
      
      // 保存回localStorage
      localStorage.setItem('removeFileHistory', JSON.stringify(history))
      
      // 更新当前历史记录
      this.loadRemoveFileHistory()
    },
    
    // 加载删除文件历史记录
    loadRemoveFileHistory() {
      this.removeFileHistory = JSON.parse(localStorage.getItem('removeFileHistory') || '[]')
    },
    
    // 清空删除文件历史记录
    clearRemoveFileHistory() {
      localStorage.removeItem('removeFileHistory')
      this.removeFileHistory = []
      this.removeFileHistoryVisible = false
      message.success('文件删除历史记录已清空')
    },
    
    // 加载删除文件历史记录时同步更新SSH连接信息
    loadRemoveFileHistoryRecord(record) {
      // 更新SSH连接信息
      this.sshInfo = {
        target: record.target,
        username: record.username,
        password: record.password,
        port: record.port,
        use_password: true
      }
      
      // 更新表单状态
      this.removeFileFormState = {
        remote_file: record.remote_file
      }
      
      this.removeFileHistoryVisible = false
      message.success('已加载历史配置')
    },
    
    // 重置删除文件表单
    resetRemoveFileForm() {
      this.$refs.removeFileFormRef.resetFields()
      this.removeFileResult = null
    },
  }
}
</script>

<style scoped>
.host-data-container {
  padding: 24px 32px;
  width: 100%;
  margin: 0;
  height: calc(100vh - 200px); /* 增加可用高度 */
  overflow-y: auto;
  position: relative;
}
.ssh-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}
.script-card {
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}
.form-container {
  padding-right: 24px;
}
.result-container {
  padding-left: 24px;
}
.result-panel {
  height: 100%;
  min-height: 450px;
  max-height: 700px;
  overflow-y: auto;
  padding: 0 16px;
  border-left: 1px solid #f0f0f0;
}
.script-output {
  background-color: #f8f8f8;
  padding: 16px;
  border-radius: 6px;
  max-height: 350px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 14px;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;
}
.script-result {
  margin-top: 20px;
}
.history-modal {
  max-width: 95%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .host-data-container {
    padding: 16px;
    height: calc(100vh - 80px);
  }
  .script-card {
    margin: 16px 0;
  }
  :deep(.ant-form-item-label) {
    padding: 0 0 4px;
  }
  :deep(.ant-card-body) {
    padding: 16px;
  }
  .form-container, .result-container {
    padding: 0;
  }
  .result-panel {
    margin-top: 24px;
    border-left: none;
    border-top: 1px solid #f0f0f0;
    padding-top: 24px;
    min-height: 350px;
  }
  .mobile-divider {
    margin-top: 0;
  }
}

/* 自定义滚动条样式 */
.host-data-container::-webkit-scrollbar,
.result-panel::-webkit-scrollbar,
.script-output::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.host-data-container::-webkit-scrollbar-thumb,
.result-panel::-webkit-scrollbar-thumb,
.script-output::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  border-radius: 4px;
}
.host-data-container::-webkit-scrollbar-track,
.result-panel::-webkit-scrollbar-track,
.script-output::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

/* 美化表单元素 */
:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-input-password) {
  border-radius: 6px;
  padding: 8px 12px;
}
:deep(.ant-btn) {
  border-radius: 6px;
  height: 36px;
  padding: 0 16px;
}
:deep(.ant-divider) {
  color: #1890ff;
  font-weight: 500;
  margin: 24px 0 16px;
}
:deep(.ant-form-item) {
  margin-bottom: 20px;
}
:deep(.ant-alert) {
  margin-bottom: 16px;
}
:deep(.ant-card-body) {
  padding: 24px;
}
:deep(.ant-tabs-tab) {
  font-size: 16px;
  padding: 12px 16px;
}
:deep(.ant-tabs-tab.ant-tabs-tab-active) {
  font-weight: 500;
}
:deep(.ant-tabs-content) {
  padding-top: 16px;
}
:deep(.ant-textarea) {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  line-height: 1.6;
}
</style>
