<template>
  <div class="dcim-data">
    <div class="left-panel">
      <div class="panel-title">数据库配置</div>
      <a-form
        :model="mysqlForm"
        :rules="rules"
        ref="mysqlForm"
        layout="inline"
        class="mysql-form"
        :label-col="{ style: { width: '100px' } }"
      >
        <a-form-item label="MySQL地址" name="host">
          <a-input
            v-model:value="mysqlForm.host"
            placeholder="请输入MySQL服务器地址"
          />
        </a-form-item>

        <a-form-item label="端口" name="port">
          <a-input-number
            v-model:value="mysqlForm.port"
            placeholder="请输入端口号"
            :min="1"
            :max="65535"
          />
        </a-form-item>

        <a-form-item label="数据库名称" name="database">
          <a-input
            v-model:value="mysqlForm.database"
            placeholder="请输入数据库名称"
          />
        </a-form-item>

        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="mysqlForm.username"
            placeholder="请输入用户名"
          />
        </a-form-item>

        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="mysqlForm.password"
            placeholder="请输入密码"
          />
        </a-form-item>
        <a-form-item
          label="连接状态"
          style="display: flex; align-items: center"
        >
          <template v-if="connectionStatus.isConnected">
            <span style="margin-right: 10px">
              【
              {{ connectionInfo.host }}:{{ connectionInfo.port }}/{{
                connectionInfo.database
              }}
              】
            </span>
          </template>
          <a-tag :color="connectionStatus.color">
            {{ connectionStatus.text }}
          </a-tag>
          <a-button type="primary" @click="testConnection" size="small">
            连接
          </a-button>
        </a-form-item>
        <a-form-item label="项目编号" name="projectCode">
          <a-input
            v-model:value="projectCode"
            placeholder="请输入项目编号"
            allow-clear
          />
        </a-form-item>
        <a-form-item 
          label="采集主机" 
          name="hostInfo"
          v-if="['device', 'deviceParam'].includes(syncType)"
        >
          <a-select
            v-model:value="hostInfo"
            placeholder="请选择采集主机"
            allow-clear
            style="width: 400px"
            :label-in-value="true"
          >
            <a-select-option
              v-for="item in hostInfoList"
              :key="item.id"
              :value="item.code"
            >
              {{ item.code }}-{{ item.name }}
            </a-select-option>
          </a-select>
          <a-button
            type="primary"
            @click="getHostInfoList"
            style="margin-left: 10px"
          >
            获取采集主机
          </a-button>
        </a-form-item>
      </a-form>
      <div style="margin-top: 20px; text-align: center">
        <div style="color: red; font-size: 16px">
          <div>Tip:如果要同步单个采集主机上的设备和参数，请先获取采集主机信息，再同步</div>
          <div>同步产品类型后，需要到*************服务器上下载图片，放到DCIM服务器上，否则无法显示图片。</div>
          <div>选择覆盖数据后，同步时会覆盖原有数据，请谨慎选择。</div>
        </div>
        <div style="display: flex; justify-content: center; gap: 10px; align-items: center">
          <a-select
            v-model:value="syncType"
            style="width: 200px"
            placeholder="请选择同步类型"
          >
            <a-select-option value="location">地点信息</a-select-option>
            <a-select-option value="brand">品牌信息</a-select-option>
            <a-select-option value="productType">产品类型信息</a-select-option>
            <a-select-option value="productModel">产品型号信息</a-select-option>
            <a-select-option value="host">嵌入式信息</a-select-option>
            <a-select-option value="device">设备信息</a-select-option>
            <a-select-option value="deviceParam">设备参数信息</a-select-option>
          </a-select>
          <a-checkbox v-model:checked="coverDataFlag">
            覆盖数据
          </a-checkbox>
          <a-button type="primary" @click="clearData">
            清空数据
          </a-button>
          <a-button type="primary" @click="handleSync">
            开始同步
          </a-button>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <div style="display: flex; justify-content: space-between">
        <div class="panel-title">日志</div>
        <div style="text-align: right">
          <a-button type="primary" @click="clearLog" size="small">
            清空日志
          </a-button>
        </div>
      </div>
      <div
        style="
          height: 70vh;
          overflow-y: auto;
          background: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
        "
        id="log"
      >
        <div
          v-for="(log, idx) in logs"
          :key="'log_' + idx"
          :style="{ color: log.isError ? 'red' : 'black', marginBottom: '5px' }"
        >
          {{ log.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    connectToDatabase,
    syncAddressData,
    syncBrandData,
    syncProductModel,
    syncProductType,
    syncHostInfo,
    syncDeviceInfo,
    syncDeviceParam,
    getHostInfoList,
    getConnectionStatus,
    softDeleteData,
  } from '@/api/connect'
  import { Modal } from 'ant-design-vue'

  export default {
    name: 'DcimData',
    data() {
      return {
        connectionInfo: {},
        logs: [],
        projectCode: 'GHYX',
        rules: {
          host: [{ required: true, message: '请输入MySQL服务器地址' }],
          port: [{ required: true, message: '请输入端口号' }],
          database: [{ required: true, message: '请输入数据库名称' }],
          username: [{ required: true, message: '请输入用户名' }],
          password: [{ required: true, message: '请输入密码' }],
        },
        dbInfo: {
          version: '',
          charset: '',
          tableCount: '',
          size: '',
          lastConnectTime: '',
        },
        hostInfoList: [],
        hostInfo: '',
        connectionStatus: {
          text: '未连接',
          color: 'warning',
          isConnected: false,
        },
        syncType: '',
        coverDataFlag: false,
      }
    },
    computed: {
      mysqlForm: {
        get() {
          return this.$store.state.database.mysqlConfig
        },
        set(value) {
          this.$store.dispatch('database/updateMysqlConfig', value)
        },
      },
    },
    mounted() {
      this.getConnectionStatus()
    },
    methods: {
      getConnectionStatus() {
        getConnectionStatus().then((response) => {
          this.connectionStatus = {
            text: response.isConnected ? '已连接' : '未连接',
            color: response.isConnected ? 'success' : 'warning',
            isConnected: response.isConnected,
          }
          this.connectionInfo = response.connectionInfo
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      clearLog() {
        this.logs = []
      },
      appendLog(log, isError = false) {
        const logEntry = {
          text: log,
          isError: isError,
        }
        this.logs.push(logEntry)
        this.$nextTick(() => {
          const elem = document.getElementById('log')
          elem.scrollTop = elem.scrollHeight
        })
      },
      // 同步设备信息
      syncDeviceInfo() {
        let params = {}
        params.coverDataFlag = this.coverDataFlag
        if (!this.hostInfo) { 
          params.code = this.projectCode
        } else {
          params.gatewayId = this.hostInfo.key
        }
        syncDeviceInfo(params).then((response) => {
          this.appendLog(`开始同步设备信息`)
          this.appendLog(`需要同步${response.totalCount}条设备信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      // 同步设备参数信息
      syncDeviceParam() {
        let needId = null
        if(this.hostInfo.key) {
          needId = this.hostInfo.key
        }
        syncDeviceParam({
          code: this.projectCode,
          gatewayId: needId,
          coverDataFlag: this.coverDataFlag,
        }).then((response) => {
          this.appendLog(`开始同步设备参数信息`)
          this.appendLog(`需要同步${response.totalCount}条设备参数信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      // 同步嵌入式信息
      syncHostInfo() {
        syncHostInfo({
          code: this.projectCode,
          coverDataFlag: this.coverDataFlag,
        }).then((response) => {
          this.appendLog(`开始同步采集主机信息`)
          this.appendLog(`需要同步${response.totalCount}条采集主机信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },

      // 同步产品型号
      syncProductModel() {
        syncProductModel({ coverDataFlag: this.coverDataFlag }).then((response) => {
          this.appendLog(`开始同步产品型号信息`)
          this.appendLog(`需要同步${response.totalCount}条产品型号信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      // 同步产品类型
      syncProductType() {
        syncProductType({ coverDataFlag: this.coverDataFlag }).then((response) => {
          this.appendLog(`开始同步产品类型信息`)
          this.appendLog(`需要同步${response.totalCount}条产品类型信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      // 同步品牌信息
      syncBrandInfo() {
        syncBrandData({ coverDataFlag: this.coverDataFlag }).then((response) => {
          this.appendLog(`开始同步品牌信息`)
          this.appendLog(`需要同步${response.totalCount}条品牌信息`)
            this.appendLog(`同步完成`)
          })
          .catch((error) => {
            this.appendLog(error.message, true)
          })
      },
      // 同步地点信息
      syncLocationInfo() {
        syncAddressData({
          code: this.projectCode,
          coverDataFlag: this.coverDataFlag,
        }).then((response) => {
          this.appendLog(`开始同步地点信息`)
          this.appendLog(`需要同步${response.totalCount}条地点信息`)
          this.appendLog(`同步完成`)
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      // 测试连接
      testConnection() {
        this.$refs.mysqlForm.validate().then(() => {
          connectToDatabase(this.mysqlForm)
            .then(() => {
              this.getConnectionStatus()
            })
            .catch((error) => {
              this.appendLog(error.message, true)
              this.connectionStatus = {
                text: '连接失败',
                color: 'error',
                isConnected: false,
              }
              this.connectionInfo = {}
            })
        })
      },
      // 获取采集主机
      getHostInfoList() {
        getHostInfoList({ code: this.projectCode }).then((response) => {
          this.hostInfoList = response
          this.$message.success('获取采集主机成功')
        })
        .catch((error) => {
          this.appendLog(error.message, true)
        })
      },
      handleSync() {
        if (!this.syncType) {
          this.$message.warning('请选择要同步的数据类型')
          return
        }

        const syncMap = {
          location: this.syncLocationInfo,
          brand: this.syncBrandInfo,
          productType: this.syncProductType,
          productModel: this.syncProductModel,
          host: this.syncHostInfo,
          device: this.syncDeviceInfo,
          deviceParam: this.syncDeviceParam
        }

        if (syncMap[this.syncType]) {
          syncMap[this.syncType]()
        }
      },
      clearData() {
        if (!this.syncType) {
          this.$message.warning('请先选择要清空的数据类型')
          return
        }

        const tableMap = {
          location: 'topology_site',
          device: 'device',
          brand: 'device_brand',
          productModel: 'device_model',
          deviceParam: 'device_parameter',
          productType: 'device_type',
          host: 'agent'
        }

        const tableName = tableMap[this.syncType]
        if (!tableName) {
          this.$message.warning('无效的数据类型')
          return
        }

        Modal.confirm({
          title: '确认清空数据',
          content: '确定要清空当前选择类型的所有数据吗？此操作不可恢复！',
          okText: '确认',
          cancelText: '取消',
          onOk: () => { 
            let agentbm = null
            console.log(this.hostInfo)
            if(this.hostInfo) {
              agentbm = this.hostInfo.value
            }
            softDeleteData({
              tableName: tableName,
              agentbm,
            }).then(() => {   
              this.appendLog(`${tableMap[this.syncType]}表数据清空成功`)
            })
            .catch((error) => {
              this.appendLog(error.message, true)
            })
          }
        })
      },
    },
    watch: {
      // 当同步类型改变时，如果不是设备相关的，就清空采集主机选择
      syncType(newVal) {
        if (!['device', 'deviceParam'].includes(newVal)) {
          this.hostInfo = null
        }
      }
    },
  }
</script>

<style scoped>
  .dcim-data {
    padding: 20px;
    display: flex;
    gap: 20px;
  }

  .left-panel,
  .right-panel {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .panel-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #1f1f1f;
    border-left: 4px solid #1890ff;
    padding-left: 10px;
  }

  .mysql-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .mysql-form :deep(.ant-form-item) {
    margin-bottom: 0;
    margin-right: 0;
    width: 100%;
  }

  .mysql-form :deep(.ant-input),
  .mysql-form :deep(.ant-input-number) {
    width: 100%;
  }

  .info-box {
    background: #fafafa;
    padding: 16px;
    border-radius: 4px;
  }

  .info-box :deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
    width: 120px;
    background: #f0f5ff;
  }

  .mysql-form :deep(.ant-form-item-label) {
    text-align: right;
    padding-right: 8px;
  }

  .mysql-form :deep(.ant-form-item-control) {
    flex: 1;
  }
</style>
