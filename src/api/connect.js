import request from '@/utils/request'

export function connectToDatabase(data) {
  return request({
    url: `/connect/connectToDatabase`,
    onErrorMessage: true,
    method: 'post',
    data
  })
}

// 同步地点信息
export function syncAddressData(data) {
  return request({
    url: `/connect/syncAddressData`,
    onErrorMessage: true,
    method: 'post',
    data
  })
}

// 同步品牌信息
export function syncBrandData(data) {
  return request({
    url: `/connect/syncProductBrand`, 
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 同步产品型号信息
export function syncProductType(data) {
  return request({
    url: `/connect/syncProductType`,
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 同步产品型号信息
export function syncProductModel(data) {
  return request({
    url: `/connect/syncProductModel`,
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 同步嵌入式信息
export function syncHostInfo(data) {
  return request({
    url: `/connect/syncHostInfo`,
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 同步设备信息
export function syncDeviceInfo(data) {
  return request({
    url: `/connect/syncDeviceInfo`,
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 同步设备参数信息
export function syncDeviceParam(data) {
  return request({
    url: `/connect/syncDeviceParam`,
    method: 'post',
    onErrorMessage: true,
    data
  })
}

// 获取采集主机列表
export function getHostInfoList(params) {
  return request({
    url: `/connect/getHostInfoList`,
    onErrorMessage: true,
    method: 'get',
    params
  })
}

// 获取连接状态
export function getConnectionStatus() {
  return request({
    url: `/connect/getConnectionStatus`,
    onErrorMessage: true,
    method: 'get',
  })
}


// 软删除数据
export function softDeleteData(data) {
  return request({
    url: `/connect/softDelete`,
    method: 'post',
    data
  })
}