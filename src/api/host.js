import request from '@/utils/request'

export const runApi = (data) => {
  return request({
    url: '/host/run-api',
    method: 'post',
    data
  })
}

export const writeContentToFileApi = (data) => {
  return request({
    url: '/host/write-content-to-file',
    method: 'post',
    data
  })
}

export const pullRemoteFileApi = (data) => {
  return request({
    url: '/host/pull-remote-file',
    method: 'post',
    data
  })
}

export const uploadLocalFileApi = (data) => {
  return request({
    url: '/host/upload-local-file',
    method: 'post',
    data
  })
}

export const removeRemoteFileApi = (data) => {
  return request({
    url: '/host/remove-remote-file',
    method: 'post',
    data
  })
}