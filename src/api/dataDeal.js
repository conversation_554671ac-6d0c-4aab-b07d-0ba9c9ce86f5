import request from '@/utils/request'

export function getlist(tableName,params) {
  return request({
    url: `/dataMag/data/${tableName}`,
    method: 'get',
    params
  })
}

export function doEdit(tableName,id,data) {
  return request({
    url: `/dataMag/data/${tableName}/${id}`,
    method: 'put',
    data
  })
}

export function doDelete(tableName,data) {
  return request({
    url: `/dataMag/data/${tableName}`,
    method: 'delete',
    data
  })
}

export function doAdd(tableName,data) {
  return request({
    url: `/dataMag/data/${tableName}`,
    method: 'post',
    data
  })
}

export function doBatch(tableName,data) {
  return request({
    url: `/dataMag/data/${tableName}/batch`,
    method: 'delete',
    data
  })
}

export function doExists(tableName,params) {
  return request({
    url: `/dataMag/data/${tableName}/exists`,
    method: 'get',
    params
  })
}

export function doInfo(tableName,code) {
  return request({
    url: `/dataMag/data/${tableName}/info/${code}`,
    method: 'get',
  })
}

export function doInfoByName(tableName,params) {
  return request({
    url: `/dataMag/data/${tableName}/infoByName`,
    method: 'get',
    params
  })
}
