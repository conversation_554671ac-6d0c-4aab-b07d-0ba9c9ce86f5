import request from '@/utils/request'

export const addDevice = (data) => {
  return request({
    url: '/devices/add-device',
    method: 'post',
    data
  })
}

export const getDeviceList = (params) => {
  return request({
    url: '/devices/get-device-list',
    method: 'get',
    params
  })
}

export const deleteDevice = (data) => {
  return request({
    url: `/devices/delete-devices`,
    method: 'delete',
    data
  })
}

export const updatePortNo = (data) => {
  return request({
    url: '/devices/update-port-no',
    method: 'put',
    data
  })
}

export const updateCompletion = (data) => {
  return request({
    url: '/devices/update-completion',
    method: 'put',
    data
  })
}

export const uploadDeviceAttributes = (data) => {
  return request({
    url: '/devices/update-rules',
    method: 'put',
    data
  })
}

export const addDeviceType = (data) => {
  return request({
    url: '/devices/add-device-type',
    method: 'post',
    data
  })
}