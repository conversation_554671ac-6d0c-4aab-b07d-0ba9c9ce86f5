import request from '@/utils/request'

// 获取资源列表
export const getResources = (params) => {
  return request({
    url: '/resources/list',
    method: 'get',
    params
  })
}

// 添加资源
export const addResources = (data) => {
  return request({
    url: '/resources/add',
    method: 'post',
    data
  })
}

// 删除资源
export const deleteResource = (id) => {
  return request({
    url: `/resources/delete`,
    method: 'delete',
    params: {
      id
    }
  })
}

// 下载资源
export const downloadResource = (id) => {
  return request({
    url: `/resources/download/${id}`,
    method: 'get',
    responseType: 'blob',
    transformResponse: [data => data],
    headers: {
      'Accept': 'application/octet-stream'
    }
  })
} 