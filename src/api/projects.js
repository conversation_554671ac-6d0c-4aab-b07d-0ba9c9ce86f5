import request from '@/utils/request'

export function getProjectList(params) {
  return request({
    url: `/project/with-workers`,
    method: 'get',
    params
  })
}

export function projectAdd(data) {
  return request({
    url: `/project/add`,
    method: 'post',
    data
  })
}

export function deleteProject(data) {
  return request({
    url: `/project/delete`,
    method: 'delete',
    data
  })
}

export function getProjectInfo(projectId) {
  return request({
    url: `/project/info/${projectId}`,
    method: 'get'
  })
}

export function editProject(projectId, data) {
  return request({
    url: `/project/edit/${projectId}`,
    method: 'put',
    data
  })
}

export function addLocations(data) {
  return request({
    url: `/project/addLocations`,
    method: 'post',
    data
  })
}

export function locationsTree(projectId) {
  return request({
    url: `/project/locationsTree/${projectId}`,
    method: 'get'
  })
}

export function clearLocations(projectId, data) {
  return request({
    url: `/project/locationDelete/${projectId}`,
    method: 'delete',
    data
  })
}

export function locationEdit(projectId, data) {
  return request({
    url: `/project/locationEdit/${projectId}`,
    method: 'put',
    data
  })
}

export function locationsClear(projectId) {
  return request({
    url: `/project/locationsClear/${projectId}`,
    method: 'delete'
  })
}

export function addProjectProducts(data) {
  return request({  
    url: `/project/addProjectProducts`,
    method: 'post',
    data,
    needMessage: true
  })
}

export function getProjectProducts(projectId, params) {
  return request({
    url: `/project/projectProducts/${projectId}`,
    method: 'get',
    params
  })
}
export function addGateway(data) {
  return request({
    url: `/project/addGateway`,
    method: 'post',
    data
  })
}
export function getGatewayInfo(id) {
  return request({
    url: `/project/getGatewayInfo/${id}`,
    method: 'get'
  })
}
// 查询设备和采集主机数量
export function getDeviceStats(params) {
  return request({
    url: `/project/deviceStats`,
    method: 'get',
    params
  })
}
export function getGatewaysList(projectId, params) {
  return request({
    url: `/project/getGatewaysList/${projectId}`,
    method: 'get',
    params
  })
}

export function getDevicesByGatewayId(gatewayId) {
  return request({
    url: `/project/getDevicesByGatewayId/${gatewayId}`,
    method: 'get'
  })
}
export function getRulesByDeviceId(deviceId) {
  return request({
    url: `/project/getRulesByDeviceId/${deviceId}`,
    method: 'get'
  })
}
export function deleteGateway(gatewayId) {
  return request({
    url: `/project/deleteGateway/${gatewayId}`,
    method: 'delete'
  })
}
export function editGateway(gatewayId, data) {
  return request({
    url: `/project/editGateway/${gatewayId}`,
    method: 'put',
    data
  })
}

export function getAttributesByProjectProductId(projectProductId, params) {
  return request({
    url: `/project/getAttributesByProjectProductId/${projectProductId}`,
    method: 'get',
    params
  })
}
export function syncProductAttributes(data) {
  return request({
    url: `/project/syncProductAttributes`,
    method: 'post',
    data
  })
}
export function updateProjectProducts(id) {
  return request({
    url: `/project/updateProjectProducts/${id}`,
    method: 'put',
    needMessage: true
  })
}

// 根据设备型号一键导入
export function syncProductAttributesByModelName(data) {
  return request({
    url: `/project/syncProductAttributesByModelName`,
    method: 'post',
    data,
    needMessage: true
  })
}