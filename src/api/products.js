import request from '@/utils/request'

export function getProductList(params) {
  return request({
    url: `/product/products-with-attribute-count`,
    method: 'get',
    params
  })
}

export function getDetail(id) {
  return request({
    url: `/product/product/${id}`,
    method: 'get',
  })
}
export function delProduct(data) {
  return request({
    url: `/product/doBatchDelete`,
    method: 'delete',
    data
  })
}
export function getBrandList(params) {
  return request({
    url: `/product/getBrandList`,
    method: 'get',
    params
  })
}

export function addStandard(data) {
  return request({
    url: `/product/addStandard`,
    method: 'post',
    data
  })
}

export function getModelsList(params) {
  return request({
    url: `/product/getModelsList`,
    method: 'get',
    params
  })
}

export function deviceStatistics() {
  return request({
    url: `/product/deviceStatistics`,
    method: 'get',
  })
}

export function updateProductRules(data) {
  return request({
    url: `/product/updateProductRules`,
    method: 'post',
    needMessage: true,
    data
  })
}

export function addAndUpdateBrand(data) {
  return request({
    url: `/product/addAndUpdateBrand`,
    method: 'post',
    data
  })
}
export function getProductListByModelName(params) {
  return request({
    url: `/product/getProductParamsByModelName`,
    method: 'get',
    params
  })
}