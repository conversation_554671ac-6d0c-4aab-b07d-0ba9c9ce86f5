import request from '@/utils/request'


export function login(data) {
  return request({
    url: '/users/login',
    method: 'post',
    data
  })
}

export async function socialLogin(data) {
  return request({
    url: '/socialLogin',
    method: 'post',
    data,
  })
}

export function getUserInfo() {
  return request({
    url: '/users/profile/current',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/users/logout',
    method: 'post',
  })
}

export function register() {
  return request({
    url: '/register',
    method: 'post',
  })
}
export function getUserList(params) {
  return request({
    url: '/users/lists',
    method: 'get',
    params
  })
}

export function addUser(data) {
  return request({
    url: '/users/register',
    method: 'post',
    data
  })
}

export function deleteUser(id) {
  return request({
    url: `/users/delete/${id}`,
    method: 'delete',
  })
}