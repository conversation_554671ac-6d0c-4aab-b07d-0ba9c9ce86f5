<template>
	<div>
		<a-tree :show-line="true"  @select="onSelect" :tree-data="treeData">
			<template #icon>
				<carry-out-outlined />
			</template>
			<a-tree-node key="0-0">
				<template #icon>
					<carry-out-outlined />
				</template>
				<template #title>
					<span style="color: #1890ff">全部</span>
				</template>
			</a-tree-node>
		</a-tree>
	</div>
</template>
<script>
import { CarryOutOutlined, FormOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref, onMounted } from 'vue';
export default defineComponent({
	components: {
		CarryOutOutlined,
		FormOutlined
	},
	setup(props, ctx) {
		const treeData = ref([
			{
				title: '全部',
				key: '0',
				children: [],
				selectable: false
			}
		]);
		onMounted(() => {
			startGetSystemList();
		});
		const locationSelectedName = ref('');
		const onSelect = (selectedKeys, info) => {
			if (info.selected) {
				locationSelectedName.value = info.selectedNodes[0].props.title;
			} else{
				locationSelectedName.value = '';
			}
			ctx.emit("todata", locationSelectedName.value);
		};
		const startGetSystemList = () => {
			getSystemList().then(res => {
				startGetAllDeviceType(res);
			});
		};
		const startGetAllDeviceType = SystemList => {
			getAllDeviceType().then(DeviceTypeList => {
				let tree = []
				for (var systemIndex = 0; systemIndex < SystemList.length; systemIndex++) {
					let system = SystemList[systemIndex].attributes
					let params = {
						title: system.name,
						key: `0-${systemIndex}`,
						children: [],
						selectable: false
					}
					for (var deviceTypeIndex = 0; deviceTypeIndex < DeviceTypeList.length; deviceTypeIndex++) {
						let deviceType = DeviceTypeList[deviceTypeIndex].attributes
						if (system.name == deviceType.system) {
							let num = params.children.length;
							let deviceparams = {
								title: deviceType.name,
								key: `0-${systemIndex}-${num}`,
							}
							params.children.push(deviceparams)
						}
					}
					tree.push(params);
				}
				treeData.value[0].children = tree;
			})
		}
		return {
			onSelect,
			startGetSystemList,
			startGetAllDeviceType,
			treeData,
			locationSelectedName
		};
	}
});
</script>
