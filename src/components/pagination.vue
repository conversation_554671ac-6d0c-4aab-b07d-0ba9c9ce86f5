<template>
  <div class="pagination">
    <a-pagination show-quick-jumper v-model:current="pageNum" v-model:pageSize="pageSize"
      :page-size-options="pageSizeOptions" :show-size-changer="showSizeChanger"
      :show-total="(total, range) => `显示第 ${range[0]} 到第 ${range[1]} 条记录，总共 ${total} 条记录`" :total="total"
      @showSizeChange="sizeChange" @change="onChange" />
  </div>
</template>
<script>
export default {
  name: 'pagination',
  props: {
    page: {
      type: Number,
      default: () => {
        return 1;
      }
    },
    size: {
      type: Number,
      default: () => {
        return 10;
      }
    },
    total: {
      type: Number,
      default: () => {
        return 10;
      }
    },
    showSizeChanger: {
      type: Boolean,
      default: () => {
        return true;
      }
    },
    pageSizeOptions: {
      type: Array,
      default: () => {
        return ['10', '20', '30', '40'];
      }
    }
  },
  watch: {
    page: {
      handler(num) {
        this.pageNum = num;
      },
      deep: true,
      immediate: true
    },
    size: {
      handler(num) {
        this.pageSize = num;
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 0
    };
  },
  methods: {
    onChange(page) {
      this.$emit('pageChange', page);
    },
    sizeChange(page, pageSize) {
      this.$emit('pageSizeChange', page, pageSize);
    }
  }
};
</script>
<style lang="less" scoped>
.pagination {
  width: 100%;

  :deep(.ant-pagination-total-text) {
    float: left;
  }

  :deep(.ant-pagination) {
    text-align: right;
  }
}
</style>