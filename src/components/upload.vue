<template>
  <div class="upload-wrapper">
    <a-upload
      v-model:file-list="fileList"
      action=""
      list-type="picture-card"
      :before-upload="beforeUpload"
      @preview="handlePreview"
      @remove="imageUrlDelete"
      :maxCount="1"
    >
      <div>
        <plus-outlined />
        <div style="margin-top: 8px">上传</div>
      </div>
    </a-upload>
    <a-modal
      :visible="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="handleCancel"
    >
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
  import { uploadImage } from '@/api/upload'
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { defineComponent, ref, watch, onMounted, computed } from 'vue'

  export default defineComponent({
    name: 'uploadChild',
    components: {
      LoadingOutlined,
      PlusOutlined,
    },
    emits: ['imageUpChange'],

    props: {
      //指定接收的值，必须指定类型
      imageObj: {
        type: Object,
        default: () => ({}),
      },
    },
    setup(props, ctx) {
      const imgUrl = ref('http://beta.smartxdc.com:22289/')
      const previewVisible = ref(false)
      const loading = ref(false)
      const previewImage = ref('')
      const previewTitle = ref('')

      // 计算属性来处理 fileList
      const fileList = computed(() => {
        if (props.imageObj?.fileId) {
          return [{
            ...props.imageObj,
            uid: props.imageObj.fileId,
            status: 'done',
            url: imgUrl.value + props.imageObj.url || '',
            },
          ]
        }
        return []
      })

      const beforeUpload = async (file) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
        if (!isJpgOrPng) {
          message.error('只能上传 JPG/PNG 格式的图片!')
          return false
        }

        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
          message.error('图片大小不能超过 2MB!')
          return false
        }

        try {
          loading.value = true
          const res = await uploadImage(file)
          const uploadResult = {
            ...res,
            uid: res.fileId,
            status: 'done',
            url: res.url
          }
          ctx.emit('imageUpChange', uploadResult)
          loading.value = false
          return false
        } catch (err) {
          loading.value = false
          message.error('图片上传失败')
          return false
        }
      }

      const imageUrlDelete = () => {
        ctx.emit('imageUpChange', null)
      }

      const handlePreview = (file) => {
        previewVisible.value = true
        previewImage.value = imgUrl.value + file.url || ''
        previewTitle.value = file.name || (file.url && file.url.substring(file.url.lastIndexOf('/') + 1))
      }

      const handleCancel = () => {
        previewVisible.value = false
        previewTitle.value = ''
      }

      return {
        fileList,
        loading,
        beforeUpload,
        previewVisible,
        handlePreview,
        handleCancel,
        imageUrlDelete,
        previewImage,
        previewTitle,
      }
    },
  })
</script>
<style scoped>
  .upload-wrapper {
    display: inline-block;
    vertical-align: top;
    margin-right: 20px;
  }

  :deep(.ant-upload-select-picture-card) {
    width: 100px;
    height: 100px;
  }

  :deep(.ant-upload-list-picture-card-container) {
    width: 100px;
    height: 100px;
  }

  :deep(.ant-upload-select-picture-card i) {
    font-size: 32px;
    color: #999;
  }

  :deep(.ant-upload-text) {
    margin-top: 8px;
    color: #666;
  }

  .toolStyle {
    top: 0;
    left: 0;
    position: absolute;
    z-index: 1;
    width: 150px;
    height: 150px;
    background-color: #00000080;
    opacity: 0.9;
    color: #ffffff;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
