<template>
	<div>
		<a-tree-select
			v-if="treeData.length > 0"
			show-search
			style="width: 100%"
			v-model:value="locationValue"
			:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
			placeholder="请选择关联类型"
			allow-clear
			multiple
			:tree-data="treeData"
			@change="handleChange"
		></a-tree-select>
	</div>
</template>
<script>
import {
  getlist
} from '../api/dataDeal.js'
import { defineComponent, ref, watch, onMounted, computed, toRaw } from 'vue';
export default defineComponent({
	props: {
		value: Array
	},
	setup(props, ctx) {
		const treeData = ref([]);
		const handleChange = (value, label) => {
			ctx.emit('treeSelectChange', value, label);
		}
		const startGetSystemList = () => {
			getlist('systems',{page:1,limit:1000000}).then(res => {
				let { data } = res
				startGetAllDeviceType(data);
			});
		};
		startGetSystemList();
		const locationValue = computed({
		  get: () => props.value,
		  set: (newValue) => {
			let a = toRaw(newValue)
			ctx.emit('update:value', a)
		  }
		});
		const startGetAllDeviceType = SystemList => {
			getlist('device_types',{page:1,limit:1000000}).then(DeviceTypeList => {
				let tree = [];
				for (var systemIndex = 0; systemIndex < SystemList.length; systemIndex++) {
					let system = SystemList[systemIndex];
					let params = {
						title: system.name,
						value: 'system' + system.code,
						children: [],
						selectable: false
					};
					let list = DeviceTypeList.data
					for (var deviceTypeIndex = 0; deviceTypeIndex < list.length; deviceTypeIndex++) {
						let deviceType = list[deviceTypeIndex];
						if (system.code == deviceType.sysCode) {
							let deviceparams = {
								title: deviceType.name,
								value: deviceType.code + '',
							};
							params.children.push(deviceparams);
						}
					}
					tree.push(params);
				}
				treeData.value = tree;
			});
		};
		return {
			treeData,
			startGetSystemList,
			startGetAllDeviceType,
			locationValue,
			handleChange
		};
	}
});
</script>
