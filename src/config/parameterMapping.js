export const parameterMapping = {
  // 基本信息映射
  paramCode: '参数ID',
  paramName: '参数名称ch',
  paramDataType: '参数类型DataChar',
  needRec: '是否启用记录历史RecHis',
  needRpt: '是否启用记录报表Report',
  workState: '是否是工作状态WrkState',
  tx: '发送串Tx',
  gatherInterval: '采集间隔(毫秒)RxTime',
  defValue: '默认值DefValue',
  refParamCode: '引用属性的idMasterId',
  dataUnit: '单位Unit',

  // 数据处理相关
  dataStartPos: '数据截取的起始位置BGPos',
  dataValidLen: '数据截取的长度Len',
  dataCalib: '计算公式Calib',
  dataCrcType: '采集值的校验类型CrcType',

  // 模拟值范围
  simMinNormalValue: '模拟最小正常值',
  simMaxNormalValue: '模拟最大正常值',
  simMinAbnormalValue: '模拟最小异常值',
  simMaxAbnormalValue: '模拟最大异常值',

  // 告警相关
  alarmEnable: '是否产生报警事件EventAlarm',
  alarmDebounce: '报警忽略次数NoEvent',
  alarmThrottle: '报警最大确认次数EmaxTimes',
  alarmRule: '告警等级阈值设置alarmrule',
  MaxAlarm: '上限一级告警阈值MaxAlarm',
  MinAlarm: '下限一级告警阈值MinAlarm',
  MaxContentDo: '上限告警内容MaxContentDo',
  MaxContentUnDo: '上限告警解除内容MaxContentUnDo',
  MinContentDo: '下限告警内容MinContentDo',
  MinContentUnDo: '下限告警解除内容MinContentUnDo',

  // 状态相关
  State0: '状态0State0',
  State1: '状态1State1',
  MaxEvent: '上限告警事件MaxEvent',
  IsCoreParameter: '公共参数标识',
}

// 需要进行数值转换的字段
export const numberFields = [
  'gatherInterval',
  'defValue',
  'dataStartPos',
  'dataValidLen',
  'alarmDebounce',
  'alarmThrottle',
]

// 需要进行布尔值转换的字段（0/1转换）
export const booleanFields = ['workState', 'needRec', 'needRpt', 'alarmEnable']

// 新增处理参数ID大小写的辅助方法
function processParamCode(code, aOrAFalg) {
  if (!code) return code
  if (typeof aOrAFalg === 'boolean') {
    return aOrAFalg ? `${code}`.toUpperCase() : `${code}`
  }
  return `${code}`.toUpperCase()
}

// 将Excel数据转换为标准格式
export function convertExcelToStandard(excelData) {
  const result = {}

  for (const [key, excelKey] of Object.entries(parameterMapping)) {

    let value = excelData[excelKey]

    if (['paramCode', 'refParamCode'].includes(key)) {
      value = processParamCode(value, excelData['aOrAFalg'])
    } 

    if (key === 'alarmRule' && !value) {
      value = '[]'
    }

    // 数值类型转换
    if (numberFields.includes(key) && value !== null && value !== undefined) {
      value = parseInt(value)
    }

    // 布尔值转换
    if (booleanFields.includes(key)) {
      if (key === 'workState') {
        value = value ? 0 : 1 // 工作状态特殊处理
      } else {
        value = !!value
      }
    }

    if (!value) {
      value = ''
    }

    result[key] = value
  }

  return result
}

// 将标准格式转换为Excel表头格式
export function convertStandardToExcel(standardData ) {
  const result = {}
  for (const [key, excelKey] of Object.entries(parameterMapping)) {
    let value = standardData[key]
    // 布尔值转换回Excel格式
    if (booleanFields.includes(key)) {
      if (key === 'workState') {
        value = value === 0 ? '是' : '否'
      } else {
        value = value ? '是' : '否'
      }
    }

    result[excelKey] = value
  }

  return result
}

// 获取表头
export function getExcelHeader() {
  return Object.values(parameterMapping)
}

