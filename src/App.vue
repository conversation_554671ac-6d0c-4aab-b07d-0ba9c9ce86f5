<template>
  <a-config-provider :locale="locale">
    <div id="vue-admin-beautiful">
      <router-view v-slot="{ Component }">
        <transition>
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </a-config-provider>
</template>
<script>
  import zhCN from 'ant-design-vue/es/locale/zh_CN'

  export default {
    name: 'App',
    data() {
      return {
        locale: zhCN,
      }
    },
    methods: {},
  }
</script>
<style lang="less">
  @import '~@/vab/styles/vab.less';
</style>
