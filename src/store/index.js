/**
 * @<NAME_EMAIL>
 * @description 导入所有 vuex 模块，自动加入namespaced:true，用于解决vuex命名冲突，请勿修改。
 */
import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate';
import database from './modules/database'

const files = require.context('./modules', false, /\.js$/)
const modules = {}
files.keys().forEach((key) => {
  modules[key.replace(/(\.\/|\.js)/g, '')] = files(key).default
})
Object.keys(modules).forEach((key) => {
  modules[key]['namespaced'] = true
})
export default createStore({
  modules: {
    ...modules,
    database
  },
  plugins: [createPersistedState({ paths: ['user', 'database'] })],
})
