export default {
  namespaced: true,
  state: {
    connectionStatus: {
      text: '未连接',
      color: 'warning',
      isConnected: false
    },
    mysqlConfig: {
      host: '127.0.0.1',
      port: 3306,
      database: 'dcim-cs',
      username: 'root',
      password: 'wangbin666'
    }
  },
  mutations: {
    SET_CONNECTION_STATUS(state, status) {
      state.connectionStatus = status
    },
    SET_MYSQL_CONFIG(state, config) {
      state.mysqlConfig = config
    }
  },
  actions: {
    updateConnectionStatus({ commit }, status) {
      commit('SET_CONNECTION_STATUS', status)
    },
    updateMysqlConfig({ commit }, config) {
      commit('SET_MYSQL_CONFIG', config)
    }
  }
} 