/**
 * @<NAME_EMAIL>
 * @description 登录、获取用户信息、退出登录、清除accessToken逻辑，不建议修改
 */
import { getUserInfo, login, logout } from '@/api/user'
import {
  removeAccessToken
} from '@/utils/accessToken'
import { title } from '@/config'
import { message, notification } from 'ant-design-vue'

const state = () => ({
  accessToken: '',
  username: '',
  avatar: '',
})

const getters = {
  accessToken: (state) => state.accessToken,
  username: (state) => state.username,
  avatar: (state) => state.avatar
}

const mutations = {
  /**
   * @<NAME_EMAIL>
   * @description 设置accessToken
   * @param {*} state
   * @param {*} accessToken
   */
  setAccessToken(state, token) {
    state.accessToken = token
  },
  /**
   * @<NAME_EMAIL>
   * @description 设置用户名
   * @param {*} state
   * @param {*} username
   */
  setUsername(state, username) {
    state.username = username
  },
  /**
   * @<NAME_EMAIL>
   * @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state, avatar) {
    state.avatar = avatar
  },
}

const actions = {
  /**
   * @<NAME_EMAIL>
   * @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }) {
    dispatch('acl/setFull', true, { root: true })
    commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
    commit('setUsername', 'admin(未开启登录拦截)')
  },
  /**
   * @<NAME_EMAIL>
   * @description 登录
   * @param {*} { commit, state }
   * @param {*} userInfo
   */
  async login({ commit }, userInfo) {
    try {
      const { token } = await login(userInfo)
      if (token) {
        commit('setAccessToken', token)
        const hour = new Date().getHours()
        const thisTime =
          hour < 8
            ? '早上好'
            : hour <= 11
            ? '上午好'
            : hour <= 13
            ? '中午好'
            : hour < 18
            ? '下午好'
            : '晚上好'
        notification.open({
          message: `欢迎登录${title}`,
          description: `${thisTime}！`,
        })
      } else {
        message.error(`登录接口异常，未正确返回`)
      }
    } catch (error) {
      throw error
    }
  },
  /**
   * @<NAME_EMAIL>
   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch, state }) {
    const data = await getUserInfo()  
    if (!data) {
      message.error(`验证失败，请重新登录...`)
      return false
    }
    let { role, username } = data
    const roles = [role.code]
    const ability = ['READ']
    dispatch('acl/setAbility', ability, { root: true })
    commit('setUsername', username)
    commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
    dispatch('acl/setRole', roles, { root: true })
  },

  /**
   * @<NAME_EMAIL>
   * @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }) {
    await logout(state.accessToken)
    await dispatch('resetAll')
  },
  /**
   * @<NAME_EMAIL>
   * @description 重置accessToken、roles、ability、router等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ dispatch }) {
    await dispatch('setAccessToken', '')
    await dispatch('acl/setFull', false, { root: true })
    await dispatch('acl/setRole', [], { root: true })
    await dispatch('acl/setAbility', [], { root: true })
    removeAccessToken()
  },
  /**
   * @<NAME_EMAIL>
   * @description 设置token
   */
  setAccessToken({ commit }, accessToken) {
    commit('setAccessToken', accessToken)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
