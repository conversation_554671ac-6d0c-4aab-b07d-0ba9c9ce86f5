import Layout from '@/layout'
const platformRouter = {
  path: '/platform',
  component: Layout,
  redirect: '/platform/dcim-data',
  alwaysShow: true,
  meta: {
    title: '平台管理',
    icon: 'apps-line',
    roles:['admin','debugger']
  },
  children: [
    {
      path: 'dcim-data',
      name: 'DcimData',
      component: () => import('@/views/platform/dcimData'),
      meta: {
        title: 'DCIM',
        icon: 'apps-line',
      },
    },
    {
      path: 'hostData',
      name: 'HostData',
      component: () => import('@/views/platform/hostData'),
      meta: {
        title: '采集主机',
        icon: 'apps-line',
      },
    },
  ],
}
export default platformRouter
