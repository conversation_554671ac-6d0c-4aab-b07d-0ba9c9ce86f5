import Layout from '@/layout'
const productRouter = {
  path: '/product',
  component: Layout,
  redirect: '/product/system',
  alwaysShow: true,
  meta: {
    title: '产品管理',
    icon: 'apps-line',
    roles:['admin','debugger']
  },
  children: [
    {
      path: 'library',
      name: 'ProductLibrary',
      component: () => import('@/views/product/library'),
      meta: {
        title: '标准产品库',
        icon: 'apps-line',
      },
    },
    {
      path: 'import',
      name: 'libraryImport',
      component: () => import('@/views/product/library/import'),
      meta: {
        title: '标准产品库导入',
        icon: 'apps-line',
      },
    },
    {
      path: 'add',
      name: 'ProductAdd',
      component: () => import('@/views/product/add'),
      hidden: true,
      meta: {
        title: '新增产品',
        icon: 'apps-line',
      },
    },
    {
      path: 'rule',
      name: 'CollectionRule',
      hidden: true,
      component: () => import('@/views/product/library/viewRule'),
      meta: {
        title: '采集规则',
        icon: 'apps-line',
      },
    },
    {
      path: 'device',
      name: 'CollectionDevice',
      hidden: true,
      component: () => import('@/views/product/library/deviceList'),
      meta: {
        title: '设备详情页列表',
        icon: 'apps-line',
      },
    },
    {
      path: 'device/binding',
      name: 'bindingDevice',
      hidden: true,
      component: () => import('@/views/product/library/binding'),
      meta: {
        title: '绑定设备详情页',
        icon: 'apps-line',
      },
    }
  ],
}
export default productRouter
