import Layout from '@/layout'
let projectRouter = {
  path: '/project',
  component: Layout,
  redirect: '/project/index',
  alwaysShow: true,
  meta: {
    title: '工程管理',
    icon: 'apps-line',
  },
  children: [
    {
      path: 'index',
      name: 'ProjectList',
      component: () => import('@/views/project/index'),
      meta: {
        title: '工程列表',
        icon: 'apps-line',
      },
    },
    // {
    //   path: 'roomDevice',
    //   name: 'RoomDevice',
    //   component: () => import('@/views/roomDevice/index'),
    //   meta: {
    //     title: '房间设备',
    //     icon: 'apps-line',
    //   },
    // },
    {
      path: 'debugging',
      name: 'Debugging',
      component: () => import('@/views/project/debugging'),
      hidden: true,
      meta: {
        title: '工程调试',
        icon: 'apps-line',
      },
    },
    {
      path: 'edit',
      name: 'ProjectEdit',
      hidden: true,
      component: () => import('@/views/project/edit'),
      meta: {
        title: '工程编辑',
        icon: 'apps-line',
      },
    },
    {
      path: 'collection',
      name: 'collectionManage',
      hidden: true,
      component: () => import('@/views/project/config/capture_mgmnt'),
      meta: {
        title: '采集管理',
        icon: 'apps-line',
      },
    },
  ],
}
function isElectron() {
  return true
  // Renderer process
  if (
    typeof window !== 'undefined' &&
    typeof window.process === 'object' &&
    window.process.type === 'renderer'
  ) {
    return true
  }

  // Main process
  if (
    typeof process !== 'undefined' &&
    typeof process.versions === 'object' &&
    !!process.versions.electron
  ) {
    return true
  }

  // Detect the user agent when the `nodeIntegration` option is set to true
  if (
    typeof navigator === 'object' &&
    typeof navigator.userAgent === 'string' &&
    navigator.userAgent.indexOf('Electron') >= 0
  ) {
    return true
  }

  return false
}

if (!isElectron()) {
  projectRouter.children = [
    {
      path: 'index',
      name: 'ProjectList',
      component: () => import('@/views/project/index'),
      meta: {
        title: '工程列表',
        icon: 'apps-line',
      },
    },
    {
      path: 'edit',
      name: 'ProjectEdit',
      hidden: true,
      component: () => import('@/views/project/edit'),
      meta: {
        title: '工程编辑',
        icon: 'apps-line',
      },
    },
  ]
}

export default projectRouter
