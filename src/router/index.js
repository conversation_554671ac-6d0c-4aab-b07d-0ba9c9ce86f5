import {
	createRouter,
	createWebHashHistory
} from 'vue-router'
import Layout from '@/layout'
/*加载主要路由*/
import product from './config/product';
import project from './config/project';
import platform from './config/platform';
export const constantRoutes = [{
	path: '/login',
	component: () => import('@/views/login'),
	hidden: true,
},
{
	path: '/403',
	name: '403',
	component: () => import('@/views/403'),
	hidden: true,
},
{
	path: '/404',
	name: '404',
	component: () => import('@/views/404'),
	hidden: true,
},
]
export const asyncRoutes = [{
	path: '/',
	component: Layout,
	redirect: '/index',
	meta: {
		title: '首页',
		icon: 'home-4-line',
		affix: true,
	},
	children: [{
		path: 'index',
		name: 'Index',
		component: () => import('@/views/index'),
		meta: {
			title: '首页',
			icon: 'home-4-line',
			affix: true,
		},
	},],
},
{
	path: '/basics',
	component: Layout,
	redirect: '/basics/system',
	meta: {
		title: '数据管理',
		icon: 'apps-line'
	},
	children: [{
		path: 'system',
		name: 'system',
		component: () => import('@/views/product/config/system.vue'),
		meta: {
			title: '系统管理',
			icon: 'table-2',
		},
	}, {
		path: 'brand',
		name: 'brand',
		component: () => import('@/views/product/config/brand.vue'),
		meta: {
			title: '品牌管理',
			icon: 'table-2',
		},
	}, {
		path: 'typeDef',
		name: 'typeDef',
		component: () => import('@/views/product/config/typeDef.vue'),
		meta: {
			title: '类型管理',
			icon: 'table-2',
		},
	},
	{
		path: 'model',
		name: 'model',
		component: () => import('@/views/product/config/model.vue'),
		meta: {
			title: '型号管理',
			icon: 'table-2',
		},
	},
	{
		path: 'import',
		name: 'systemImport',
		component: () => import('@/views/product/systemImport'),
		meta: {
			title: '数据导入',
			icon: 'table-2',
			roles: ['admin', 'debugger']
		},
	}
	],
},
	product,
	project,
	platform,
{
	path: '/personnelManagement',
	component: Layout,
	redirect: 'noRedirect',
	name: 'PersonnelManagement',
	meta: {
		title: '配置',
		icon: 'apps-line',
		roles: ['admin']
	},
	children: [{
		path: 'userManagement',
		name: 'UserManagement',
		component: () =>
			import('@/views/personnelManagement/userManagement/index'),
		meta: {
			title: '用户管理',
			icon: 'apps-line',
		},
	},
	],
},
{
	path: '/logs',
	component: Layout,
	redirect: '/logs/index',
	meta: {
		title: '日志管理',
		icon: 'apps-line',
	},
	children: [{
		path: 'index',
		name: 'Logs',
		component: () => import('@/views/index/logs'),
		meta: {
			title: '日志管理',
			icon: 'apps-line',
		},
	}]
},
{
	path: '/resources',
	component: Layout,
	redirect: '/resources/index',
	meta: {
		title: '资源管理',
		icon: 'apps-line',
	},
	children: [{
		path: 'index',
		name: 'Resources',
		component: () => import('@/views/resources/list'),
		meta: {
			title: '资源管理',
			icon: 'apps-line',
		},
	}]
},
// {
//   path: '/test',
//   component: Layout,
//   redirect: '/test/test',
//   meta: {
//     title: '动态路由测试',
//     icon: 'test-tube-line',
//   },
//   children: [
//     {
//       path: 'test',
//       name: 'Test',
//       component: () => import('@/views/test'),
//       meta: {
//         title: '动态路由测试',
//         icon: 'test-tube-line',
//       },
//     },
//   ],
// },
// {
//   path: '/error',
//   name: 'Error',
//   component: Layout,
//   redirect: '/error/403',
//   meta: {
//     title: '错误页',
//     icon: 'error-warning-line',
//   },
//   children: [
//     {
//       path: '403',
//       name: 'Error403',
//       component: () => import('@/views/403'),
//       meta: {
//         title: '403',
//         icon: 'error-warning-line',
//       },
//     },
//     {
//       path: '404',
//       name: 'Error404',
//       component: () => import('@/views/404'),
//       meta: {
//         title: '404',
//         icon: 'error-warning-line',
//       },
//     },
//   ],
// },
{
	path: '/*',
	redirect: '/404',
	hidden: true,
},
]
const router = createRouter({
	history: createWebHashHistory(),
	routes: constantRoutes
})

export default router