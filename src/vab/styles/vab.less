@import "./normalize.less";

html {
  body {

    * {
      box-sizing: border-box;
    }

    /* ant-input-search搜索框 */
    .ant-input-search {
      max-width: 250px;
    }

    /* ant-pagination分页 */
    .ant-pagination {
      margin-top: @vab-margin;
      text-align: center;

      &.ant-table-pagination {
        float: none !important;
        margin-top: @vab-margin;
      }

    }


  }
}

/***滚动条设置***/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgba(#ffffff, 0);
  border-left: none;
}
::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 5px;
  background-color: rgba(#b7c0cf, 0.2);
}
::-webkit-scrollbar-thumb:hover {
  border-radius: 3px;
  background-color: rgba(#b7c0cf, 0.4);
}