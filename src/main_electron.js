const electron = require('electron')
const app = electron.app
const BrowserWindow = electron.BrowserWindow

let win

function createWindow() {
  win = new BrowserWindow({
    autoHideMenuBar: true,
    width: 1280,
    height: 720,
    webPreferences: {
      contextIsolation: false,
      nodeIntegration: true,
    },
  })

  console.log('访问', process.argv[2])
  if (process.argv[2].startsWith('http')) {
    win.loadURL(process.argv[2])
  } else {
    win.loadFile(process.argv[2])
  }

  win.on('close', function () {
    win = null
  })
}

app.on('ready', createWindow)
