import axios from 'axios'
import { message } from 'ant-design-vue'
import { baseURL } from '@/config'
import store from '@/store'
import router from '@/router'

// 创建 axios 实例
const request = axios.create({
  baseURL: baseURL,
  timeout: 6000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加固定的appkey和appsecret头部信息
    config.headers['appkey'] = 'lksoft'
    config.headers['appsecret'] = 'lksoft'
    
    // 从vuex中获取token - 使用正确的getter路径
    const token = store.getters['user/accessToken']

    // 如果token存在则添加到请求头
    if (token) {
      config.headers['authorization'] = `Bearer ${token}` // 添加Bearer前缀
    }
    // 如果需要显示消息则添加needMessage属性
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 如果是blob类型的响应，直接返回数据
    if (response.config.responseType === 'blob') {
      return response
    }

    const res = response.data
    const needMessage = response.config.needMessage
    const onErrorMessage = response.config.onErrorMessage

    if(!res?.code) {
      return res
    }

    // 判断返回状态
    if (res.code !== 200) {
      if (!onErrorMessage) {
        message.error(res.message || '请求失败')
      }
      return Promise.reject(res)
    }
    if(needMessage){
      message.success(res.message || '请求成功')
      return res
    }
    // 直接返回数据部分
    return res.data
  },
  error => {
    // 处理 HTTP 状态码错误
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 401:
          message.error(data.message || '登录已过期，请重新登录')
          // 清除用户信息并跳转登录页
          store.dispatch('user/resetAll').then(() => {
            router.push('/login')
          })
          break
          
        case 403:
          message.error('没有权限访问')
          router.push('/403')
          break
          
        case 404:
          message.error(data.message || '请求的资源不存在')
          break
          
        case 500:
          message.error(data.message || '服务器错误')
          break

        default:
          message.error(data.message || `请求失败(${status})`)
      }
    } else if (error.message.includes('timeout')) {
      message.error('请求超时')
    } else {
      message.error('网络连接错误')
    }
    return Promise.reject(error.response.data)
  }
)

export default request
