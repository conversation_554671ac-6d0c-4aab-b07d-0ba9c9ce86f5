directories:
  output: out
  buildResources: build
productName: SmartXDCEnginnerTool
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
asarUnpack:
  - '**/*.node'
appId: com.smartxdc.configtool
copyright: LKSoft
compression: store
win:
  target: nsis
  icon: build/wechatimg.ico
nsis:
  oneClick: false
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: 工程配置工具
  include: build/installer.nsh
files: []
electronVersion: 17.4.11
